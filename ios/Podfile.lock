PODS:
  - app_links (0.0.2):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_pdfview (1.0.2):
    - Flutter
  - geocoding_ios (1.0.5):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
    - FlutterMacOS
  - Google-Maps-iOS-Utils (5.0.0):
    - GoogleMaps (~> 8.0)
  - google_maps_flutter_ios (0.0.1):
    - Flutter
    - Google-Maps-iOS-Utils (< 7.0, >= 5.0)
    - GoogleMaps (< 10.0, >= 8.4)
  - GoogleMaps (8.4.0):
    - GoogleMaps/Maps (= 8.4.0)
  - GoogleMaps/Base (8.4.0)
  - GoogleMaps/Maps (8.4.0):
    - GoogleMaps/Base
  - image_picker_ios (0.0.1):
    - Flutter
  - location (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - purchases_flutter (8.7.2):
    - Flutter
    - PurchasesHybridCommon (= 13.28.0)
  - purchases_ui_flutter (8.7.2):
    - Flutter
    - PurchasesHybridCommonUI (= 13.28.0)
  - PurchasesHybridCommon (13.28.0):
    - RevenueCat (= 5.21.0)
  - PurchasesHybridCommonUI (13.28.0):
    - PurchasesHybridCommon (= 13.28.0)
    - RevenueCatUI (= 5.21.0)
  - RevenueCat (5.21.0)
  - RevenueCatUI (5.21.0):
    - RevenueCat (= 5.21.0)
  - rive_common (0.0.1):
    - Flutter
  - SDWebImage (5.21.0):
    - SDWebImage/Core (= 5.21.0)
  - SDWebImage/Core (5.21.0)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqlite3 (3.49.1):
    - sqlite3/common (= 3.49.1)
  - sqlite3/common (3.49.1)
  - sqlite3/dbstatvtab (3.49.1):
    - sqlite3/common
  - sqlite3/fts5 (3.49.1):
    - sqlite3/common
  - sqlite3/math (3.49.1):
    - sqlite3/common
  - sqlite3/perf-threadsafe (3.49.1):
    - sqlite3/common
  - sqlite3/rtree (3.49.1):
    - sqlite3/common
  - sqlite3_flutter_libs (0.0.1):
    - Flutter
    - FlutterMacOS
    - sqlite3 (~> 3.49.1)
    - sqlite3/dbstatvtab
    - sqlite3/fts5
    - sqlite3/math
    - sqlite3/perf-threadsafe
    - sqlite3/rtree
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter

DEPENDENCIES:
  - app_links (from `.symlinks/plugins/app_links/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - geocoding_ios (from `.symlinks/plugins/geocoding_ios/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/darwin`)
  - google_maps_flutter_ios (from `.symlinks/plugins/google_maps_flutter_ios/ios`)
  - GoogleMaps
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - location (from `.symlinks/plugins/location/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - purchases_flutter (from `.symlinks/plugins/purchases_flutter/ios`)
  - purchases_ui_flutter (from `.symlinks/plugins/purchases_ui_flutter/ios`)
  - rive_common (from `.symlinks/plugins/rive_common/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqlite3_flutter_libs (from `.symlinks/plugins/sqlite3_flutter_libs/darwin`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - Google-Maps-iOS-Utils
    - GoogleMaps
    - PurchasesHybridCommon
    - PurchasesHybridCommonUI
    - RevenueCat
    - RevenueCatUI
    - SDWebImage
    - sqlite3
    - SwiftyGif

EXTERNAL SOURCES:
  app_links:
    :path: ".symlinks/plugins/app_links/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  geocoding_ios:
    :path: ".symlinks/plugins/geocoding_ios/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/darwin"
  google_maps_flutter_ios:
    :path: ".symlinks/plugins/google_maps_flutter_ios/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  location:
    :path: ".symlinks/plugins/location/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  purchases_flutter:
    :path: ".symlinks/plugins/purchases_flutter/ios"
  purchases_ui_flutter:
    :path: ".symlinks/plugins/purchases_ui_flutter/ios"
  rive_common:
    :path: ".symlinks/plugins/rive_common/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqlite3_flutter_libs:
    :path: ".symlinks/plugins/sqlite3_flutter_libs/darwin"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"

SPEC CHECKSUMS:
  app_links: f3e17e4ee5e357b39d8b95290a9b2c299fca71c6
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 15fd9539e4eb735dc54bae8c0534a7a9511a03de
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_pdfview: 2e4d13ffb774858562ffbdfdb61b40744b191adc
  geocoding_ios: a389ea40f6f548de6e63006a2e31bf66ff80769a
  geolocator_apple: 66b711889fd333205763b83c9dcf0a57a28c7afd
  Google-Maps-iOS-Utils: 66d6de12be1ce6d3742a54661e7a79cb317a9321
  google_maps_flutter_ios: e31555a04d1986ab130f2b9f24b6cdc861acc6d3
  GoogleMaps: 8939898920281c649150e0af74aa291c60f2e77d
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  location: d5cf8598915965547c3f36761ae9cc4f4e87d22e
  package_info_plus: c0502532a26c7662a62a356cebe2692ec5fe4ec4
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  purchases_flutter: 38d47f2bba4e5ebb261df5d27f272e8592c5bf7d
  purchases_ui_flutter: 3318228fbfc1ff0338b5777406b46e2a6e43e795
  PurchasesHybridCommon: f0d344e7073314988b3d98fbb9da69db85aaff38
  PurchasesHybridCommonUI: e582b0eea9b7413ee8b20f20ce30abef0412b3a6
  RevenueCat: 57028a8673201fdaacd1354065e8adbcba5739a4
  RevenueCatUI: 1433a74137c9297e7647037ea0a138921d6f9e16
  rive_common: 3a4c254c6e4db7e4b9e05daeb3d1f47ae4f7bf76
  SDWebImage: f84b0feeb08d2d11e6a9b843cb06d75ebf5b8868
  share_plus: c3fef564749587fc939ef86ffb283ceac0baf9f5
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sqlite3: fc1400008a9b3525f5914ed715a5d1af0b8f4983
  sqlite3_flutter_libs: 487032b9008b28de37c72a3aa66849ef3745f3e6
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe

PODFILE CHECKSUM: 8faf6b44f8131fd9ddb9b2a9299a9c8df8b2469b

COCOAPODS: 1.16.2
