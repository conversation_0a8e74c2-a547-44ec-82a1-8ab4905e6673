{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986821cab7b23375d203ab79aef76618fd", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9885cabbb6e788a762841591deaf1fcb57", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddd0604ca5b2bdcc19e75cbe9444516b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9849e98d1373ca5cb1dcdb26755e061e36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ddd0604ca5b2bdcc19e75cbe9444516b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "14.0", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/google_maps_flutter_ios/google_maps_flutter_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "google_maps_flutter_ios", "PRODUCT_NAME": "google_maps_flutter_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9827c7327ae6e108884301c07e4e124ab3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b764ae599b3a2f2e171c389d8ae9a1e7", "guid": "bfdfe7dc352907fc980b868725387e98fde1266a3cbeb2387eca315a29b956b2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875f356a2ae835ec55c1ff1596378de98", "guid": "bfdfe7dc352907fc980b868725387e980d81ce22815e581f738b740b9be82df3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc9b2a8b294fc62b4727305a8fa91ceb", "guid": "bfdfe7dc352907fc980b868725387e98d03928281fde63c866e3c5f56d8aebf0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845f6e4208975a7012cebe10ca95a92ab", "guid": "bfdfe7dc352907fc980b868725387e9880608433b3bc911542e75846113f96df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6ebe34415f431745c471cde63a9f6f8", "guid": "bfdfe7dc352907fc980b868725387e98e703d60fcb6c658f7f8b1d083ae20127", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890c17d272a7517fb1b9d915c57c61213", "guid": "bfdfe7dc352907fc980b868725387e982fabfb02ffaefc1653ee320b4642a5b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98335bb21e4ec812775734ee287b0ef8a2", "guid": "bfdfe7dc352907fc980b868725387e985f5e9e45104e687a83d73f75841899f7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867ff697ad34f4966b2e3afe0aba6ea2a", "guid": "bfdfe7dc352907fc980b868725387e9846660565bff3b5d3a041d625367b2492", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce400c048b36d4e8e09121507954f729", "guid": "bfdfe7dc352907fc980b868725387e98e61f79a3f9820b38d5ee7926a024c985", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c25bf2ba6140bb200a13b8a443e9c720", "guid": "bfdfe7dc352907fc980b868725387e98896c25c4c6d0fb8324dc0b4fee8cf7a4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856278355dedd176331022a4871a5d39b", "guid": "bfdfe7dc352907fc980b868725387e98faf5377cb2109b12d99b5ee0a70d4f65", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989e682b5913074eb3d2a1c1b74be0b7c3", "guid": "bfdfe7dc352907fc980b868725387e98ecc1104824c844dcd0de6da330bf59d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800195a29d91122c9a47c65a540bf5164", "guid": "bfdfe7dc352907fc980b868725387e98e3c0ad036e69201060ac06170d38f509", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988f2c09afa59f69eda4de17af22b000b6", "guid": "bfdfe7dc352907fc980b868725387e986d1ea00d8c42512d43efff03ea163539", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875298e5bd40a8e365fa873a2999d27a4", "guid": "bfdfe7dc352907fc980b868725387e983ff251c5640764c60e3b4ebe291dd35b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d3136d1bd52fa869c396194a899b2a3", "guid": "bfdfe7dc352907fc980b868725387e98e1c2d823b21f2fbc22110e24493b628b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852c44e8190290a39f50ef0f1bdf41291", "guid": "bfdfe7dc352907fc980b868725387e9819a58a3d592bd8a3c83f9588222cea4e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98371ea9dbe2abcb5906b3d3f36456d64b", "guid": "bfdfe7dc352907fc980b868725387e988536fcaf720f25055c5cf22f2f7e16e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98498809a74515487353b951df1917e13b", "guid": "bfdfe7dc352907fc980b868725387e98fb87df7b9bca68c93c8e6bb6b986cc70", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e42ce121a5d8f8199daf4dc9fd74efb", "guid": "bfdfe7dc352907fc980b868725387e98efd4640328b24ccefec8bf72ce2349c9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989a9f642c04145579f4c63780b0d3cd6d", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988da8a53992afe1ed42d03bb3ff281885", "guid": "bfdfe7dc352907fc980b868725387e987795a216303c82397a5e1dd33ad30da9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f7ad6c7859b024efa70c7a594419c43", "guid": "bfdfe7dc352907fc980b868725387e986785d17e53ba24c8e7e20eb0925e5ece"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b20562935fb9809d80fb03a1bdc16d9", "guid": "bfdfe7dc352907fc980b868725387e98e93d81db85c5b67850e534c0cec7b059"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8911b65cdef5d3036cc4640f6235e13", "guid": "bfdfe7dc352907fc980b868725387e989995f411a0f5cbcc8864563b4260b363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863dc5510c5ab9ebc94986dd7fb60c7a1", "guid": "bfdfe7dc352907fc980b868725387e9804d72ed45f5eac089558baf7c29443db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98799db996b8f4e18149075e106b63790b", "guid": "bfdfe7dc352907fc980b868725387e98bea62292c81ebd34b3b77676394ebb03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b703cdf94cc9704817129819750c9ec", "guid": "bfdfe7dc352907fc980b868725387e980e4457cbe7d07be6b2e63d86b6f30038"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7069b9607074d2e096e54ee162a113c", "guid": "bfdfe7dc352907fc980b868725387e98c559b262c99b95388db3cc0188fe3e9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989175384b4d93d9599bb104515167c3e6", "guid": "bfdfe7dc352907fc980b868725387e988f1c2f9f38a8971a44696694f473d7a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889176bccdc5431589644d5e52d842b7d", "guid": "bfdfe7dc352907fc980b868725387e984947256e39494dd81f138f72a81b6dd9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a840e011190a3e8da16083d7527305ad", "guid": "bfdfe7dc352907fc980b868725387e987446a53c9beed3d99946077648e987de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bace5ecd7c06ac802036d001127b27b5", "guid": "bfdfe7dc352907fc980b868725387e98294182e6f59862494590317cc9296551"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859770c9d05edd862dada4e282aeda6cf", "guid": "bfdfe7dc352907fc980b868725387e985debc93a15d8af7fefc9cc082a52aac3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3fa9469cfadc73ef7218b30e4b034a5", "guid": "bfdfe7dc352907fc980b868725387e985af1746bca85c672d9cad0aa2be3b903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8171550c1607da425b570e82eb4446", "guid": "bfdfe7dc352907fc980b868725387e989d80a9ddd977023ba19a6998d0e831a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848d29f166aa1c9141e82f869c08fab42", "guid": "bfdfe7dc352907fc980b868725387e9807d0f98efefea15ab6e0dd5bf094b4f3"}], "guid": "bfdfe7dc352907fc980b868725387e9840c2019223a31a24c9d5d772d5b682ef", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98f54b4a8670a17084faf9564d25bf3022"}], "guid": "bfdfe7dc352907fc980b868725387e98bbcbde54eadddc7dbda1d45d8331ae37", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9819d35fd5b4999f95b3d97e78794bae17", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98117b13c59de776c223f2f14af197afb1", "name": "Google-Maps-iOS-Utils"}, {"guid": "bfdfe7dc352907fc980b868725387e9818352c54edac2258b91768852065ce5e", "name": "GoogleMaps"}, {"guid": "bfdfe7dc352907fc980b868725387e9845fff747e8d3c707f1d7451d71a9982f", "name": "google_maps_flutter_ios-google_maps_flutter_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98df83286ef0c813795b2a6e5600f49912", "name": "google_maps_flutter_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98e749aca54f09b9c5c4f2ba052cee0d36", "name": "google_maps_flutter_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}