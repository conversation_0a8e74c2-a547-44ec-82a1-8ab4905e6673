{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980523992b257f777eedea3af36cdad675", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9873ddf834fad1f6388830b5d3eb133eaa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98345bc76bd93da24a6ed6a8181373c5a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f3490eb5957af8c1b31e47b08f5a1d8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98345bc76bd93da24a6ed6a8181373c5a2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseRemoteConfig/FirebaseRemoteConfig.modulemap", "PRODUCT_MODULE_NAME": "FirebaseRemoteConfig", "PRODUCT_NAME": "FirebaseRemoteConfig", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d7ee1db34c4df03b8f2a4edf7734b0d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9840ff919432e1f7142be2cc606f938566", "guid": "bfdfe7dc352907fc980b868725387e982403bc69513fada28abe9015613cea20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3403099a74ef526bec2eba267a120c", "guid": "bfdfe7dc352907fc980b868725387e9805155cd365e89ada5d2b2733ceb8d037"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830041dfd0f018f01260a8a23f9cc8d82", "guid": "bfdfe7dc352907fc980b868725387e98e9253fe56456c4ced906f21fd25943d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985f1f7b7e881c0685773c714756043e0b", "guid": "bfdfe7dc352907fc980b868725387e98cda70abfaa13618cec82b37d9fa61ef1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de26487eb914ae3e81d9b85eee8adfd6", "guid": "bfdfe7dc352907fc980b868725387e98c1d27f23312bd934eb0d09e8cc940217"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98514dd6f6155ce172ba41e52d2a36c25c", "guid": "bfdfe7dc352907fc980b868725387e988cce7240e41cff0c7d284cb85341e5f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98145774e0aae8bf12b77f91ed6a9fa7bf", "guid": "bfdfe7dc352907fc980b868725387e9882643b5f7b1b259e2b606fa3654e180b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c215d2d2f03131c8841afc56a168a653", "guid": "bfdfe7dc352907fc980b868725387e98f53644638b3207981f8e04c927c943cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c47f577bd58aa5fba316dfb887035ba", "guid": "bfdfe7dc352907fc980b868725387e98cca886a88f66876baa6671a12cd31aac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888db5c52a093085d4c5a6850a6617371", "guid": "bfdfe7dc352907fc980b868725387e98aee5e5b02379cd020ebc59c83e74adb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98987d78c7984772dd976366dd14de41b5", "guid": "bfdfe7dc352907fc980b868725387e9827f7ee1094d85297d6bf93b96ff1ca8d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7dbeae1255f048850dd918565a4250f", "guid": "bfdfe7dc352907fc980b868725387e98b15a446b7981f20989040fae4a206581", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ea02e702d47b573495bdc308d122f017", "guid": "bfdfe7dc352907fc980b868725387e98c5e72e26616da85536266e4a218cf961"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986cb21ef8748c935a4bca347ea6ad58e6", "guid": "bfdfe7dc352907fc980b868725387e983a5566765cba23788f9564a8c003001e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797b89c531832e042df0ad74d9f7b156", "guid": "bfdfe7dc352907fc980b868725387e9825a661c744d57d28f19936a29d109001"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847c37be26ac0ede5cba2152fd914f86b", "guid": "bfdfe7dc352907fc980b868725387e98d2ecd0fd183deab1dc3f0c39a04bbdb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7e38a47589a3b031c1bca617ed2ca33", "guid": "bfdfe7dc352907fc980b868725387e98888f2c3b66b4c9d98fc98c36445e444f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b2f06abf377c7fea69aa13b915dcff9", "guid": "bfdfe7dc352907fc980b868725387e98715aa72c043c16ddb4b4b1c4b412b446", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863c0589d1c3abab57f11855e3a0bdc61", "guid": "bfdfe7dc352907fc980b868725387e987d7b8ebc95c08cfc99ef99b0137143f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d5f91c31cb290542c47521e8d32bd38", "guid": "bfdfe7dc352907fc980b868725387e985703405d41c3c2f14130190d9b40c22e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ce448e4f2b506897b8574e9d903806f", "guid": "bfdfe7dc352907fc980b868725387e9876477284c13d6d32aef1ff8da807b415"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db67002962ded897db6e373ece1f4b7a", "guid": "bfdfe7dc352907fc980b868725387e9866cdfbfd796c93e2c13d4674a8e3e40a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98464f006aea9a22414217496ee250ae88", "guid": "bfdfe7dc352907fc980b868725387e9840f84c367dc15e31930c3e20325e8f2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9babd983b8eaea235f495521d1a3845", "guid": "bfdfe7dc352907fc980b868725387e98c30ef787fab4cbb35ee699d7d766ce56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2d7028daef29724f034736d6fb5c98", "guid": "bfdfe7dc352907fc980b868725387e984f4abee5fdd1499eba8387f2f89f9af2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9ac76a5a973ab8b3460f5514ac002a0", "guid": "bfdfe7dc352907fc980b868725387e981b14ccd7feca53d96c7c7843950a28c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982b1ebffd389a185f9052b35758b70995", "guid": "bfdfe7dc352907fc980b868725387e989ec3340a4e69403c3b4339742c005973"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b99d10639cdda14cd26278ddbee3ac9f", "guid": "bfdfe7dc352907fc980b868725387e9855b4cdba26b2ae2f775af1481a390d4c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854960b7ce3e4d2f276a40ea3e36718dd", "guid": "bfdfe7dc352907fc980b868725387e98dff6597720c3a932567f30e98fb7200e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e89423b3d4d5b53316e9b4066babe14", "guid": "bfdfe7dc352907fc980b868725387e9809282f44756e6116e706075922ba2e20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833bb3a2e9cbd4e6641e56c7db1506369", "guid": "bfdfe7dc352907fc980b868725387e98eb860f64fb7f86b5ea0c981473af9c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9860106a55c288935a0d6d01e56e280c6d", "guid": "bfdfe7dc352907fc980b868725387e98d222eb37d3e2698f3b00b1e15493b955"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983553ef1dcc7fd8df99963a4c91567ab0", "guid": "bfdfe7dc352907fc980b868725387e98c40236304274db387b78b14e50bb4f5f"}], "guid": "bfdfe7dc352907fc980b868725387e9886a196a95b8fff76dc9491f2960ce312", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be08e31ee1b48b5b2030da2737645128", "guid": "bfdfe7dc352907fc980b868725387e9866c71a7664c35343645ba50cf83eea81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb8dd543abb02ca0446354d1068603c5", "guid": "bfdfe7dc352907fc980b868725387e9881d3148e14012ea6e3b38969edc25386"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985849332fd05135acc45821b82656dfa8", "guid": "bfdfe7dc352907fc980b868725387e98cfa80f595058591faee70871a86e89ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c73ab32b0a3447779df579e7949faef8", "guid": "bfdfe7dc352907fc980b868725387e98e0bfc5cfe618576ac000ea878f1410ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2719f8b246b229bc4e073c4d0d69545", "guid": "bfdfe7dc352907fc980b868725387e9806081d356f8ee0e8cab139feee8a73ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b1138182e3efc911611cfb4331b08ca", "guid": "bfdfe7dc352907fc980b868725387e9882d50c663743d27f346917753d6125e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98104aa1deb54ac0d57e14cdae2d281b97", "guid": "bfdfe7dc352907fc980b868725387e982f84d7accc67293c2494daa77a3db245"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98522da00c2dc7d0736949973ae84d3f14", "guid": "bfdfe7dc352907fc980b868725387e984d41c09bafb39a623677a17afcd50ebf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98073729ae644752544c215148939f5f82", "guid": "bfdfe7dc352907fc980b868725387e982876bcb79861e99115db10ee2f63d70b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b1da24b4f56d86011f6717e5fa1c4bc", "guid": "bfdfe7dc352907fc980b868725387e98ec534176e1130ca2a12ec5b19fd6f99c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827c43859460faac8950eee8ef9a04836", "guid": "bfdfe7dc352907fc980b868725387e98eccfe44ec7d28e1f08187a5789a0ac06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73aa7dc809346b20e32e2ccfe10d966", "guid": "bfdfe7dc352907fc980b868725387e988910bb4e1e0a978b74e40be6b421b726"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986690145d00604775f4acc280a5f05d06", "guid": "bfdfe7dc352907fc980b868725387e98436ac783bb8d8867cd69b2fc71f98669"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a802463ac2c20c6ebc5d02472730f31e", "guid": "bfdfe7dc352907fc980b868725387e98192a58cb339869554fd323bd7c828f6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813b0af9093d2d0ffbd2bf6b2cd1a965e", "guid": "bfdfe7dc352907fc980b868725387e98dc61dc0f9c49304bf7f78f055da27558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd31bb05d7d14a7cdd1568ec16c9903c", "guid": "bfdfe7dc352907fc980b868725387e98009d628abebbbde922b46b87cb1848f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c84c2b4fb816fb32ebb9c94220a270a5", "guid": "bfdfe7dc352907fc980b868725387e98457c1227a1408c71d291a4034b77132d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d1bb923c3f6af5da391fb8acb21cdd9", "guid": "bfdfe7dc352907fc980b868725387e9847ad4da74f6a0d83528651349f1c1014"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883129398826c34d83666c94ed3acace8", "guid": "bfdfe7dc352907fc980b868725387e980129083594a82adad547651a303bc4f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdab97164a53c03c99970b31ced94210", "guid": "bfdfe7dc352907fc980b868725387e98a8b685570b93c2747226197a5edcd9e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d49e04b47c42550d76c35a90b264277", "guid": "bfdfe7dc352907fc980b868725387e98d5f2d1d39b937b80ca72e900f9a840d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b4f3586478fe46d1fe0ad098a52b81b", "guid": "bfdfe7dc352907fc980b868725387e989e304e210fa9bb539efcdf17daa8b5f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeb28b8341d56859125ddebabfd1e74a", "guid": "bfdfe7dc352907fc980b868725387e98e5372cc09700a4bd1b81323c8ea3485c"}], "guid": "bfdfe7dc352907fc980b868725387e987da4af2ade5b5bf712e12f86d4d5f252", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98d26f05535eca41f34a07b7e29eaa4f88"}], "guid": "bfdfe7dc352907fc980b868725387e988246262bc448db87362bc81380b52d67", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98553e110be8be11f540fef3ccace389b4", "targetReference": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca"}], "guid": "bfdfe7dc352907fc980b868725387e986fc96870bbeeb7089e31e91b557811d1", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98012330f90a37c9d15a390c6b73b6dbca", "name": "FirebaseRemoteConfig-FirebaseRemoteConfig_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e984b1e8e5f67fa144e5e34058df6e2f50c", "name": "FirebaseRemoteConfigInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98928855ae8620d13300183deed96c33a1", "name": "FirebaseRemoteConfig", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980b80126605cba44506bfa90fbbd69742", "name": "FirebaseRemoteConfig.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}