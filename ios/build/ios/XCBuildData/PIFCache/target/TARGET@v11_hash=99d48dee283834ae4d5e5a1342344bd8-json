{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980abab5970ebbf7a12894661821c184c4", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/RevenueCatUI", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "RevenueCatUI", "INFOPLIST_FILE": "Target Support Files/RevenueCatUI/ResourceBundle-RevenueCat_RevenueCatUI-RevenueCatUI-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "RevenueCat_RevenueCatUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98acded7e2a06b461f70c63454d78ffc56", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cd102818bad3fefc61d6f5a04b0338a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/RevenueCatUI", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "RevenueCatUI", "INFOPLIST_FILE": "Target Support Files/RevenueCatUI/ResourceBundle-RevenueCat_RevenueCatUI-RevenueCatUI-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "RevenueCat_RevenueCatUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e9801118d94e19f25fce51b0b050991143d", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988cd102818bad3fefc61d6f5a04b0338a", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/RevenueCatUI", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "RevenueCatUI", "INFOPLIST_FILE": "Target Support Files/RevenueCatUI/ResourceBundle-RevenueCat_RevenueCatUI-RevenueCatUI-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "PRODUCT_NAME": "RevenueCat_RevenueCatUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e985207d18dfcc8efbed6760ca9a4a76cc5", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9837f0a623c83155bb54179c583bb0dafa", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e986eb6786f980126b54c23066e3f954863", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a79f34931510b12cd2ac56d2601f3651", "guid": "bfdfe7dc352907fc980b868725387e98827f95b90dca9c47f21a27d96c877082"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e3af0defa369c6785e38ad2deb6899b", "guid": "bfdfe7dc352907fc980b868725387e98563f97586a801627ba1231e1ada65c48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98850adf6d7a585ba583bc5554c920b2fa", "guid": "bfdfe7dc352907fc980b868725387e9899fe92e580f2bb5506112cca9e68e49a"}], "guid": "bfdfe7dc352907fc980b868725387e98c893b8eddc1e22d24ef6b202ee83161a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98e5eef69198427ac802f968fdd50210a0", "name": "RevenueCatUI-RevenueCat_RevenueCatUI", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbaa3173eeaff727a5c8f56d37f1e707", "name": "RevenueCat_RevenueCatUI.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}