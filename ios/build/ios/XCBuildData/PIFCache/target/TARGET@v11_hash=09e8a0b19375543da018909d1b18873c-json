{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98261f4d02b605ce1a2b4304827296a1ab", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98356201475dd33c38e695fe6fffb23895", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5fef3fb30d9022937f5beb5e201b0cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e461d66796e7d2d570ea50d0d60f78ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b5fef3fb30d9022937f5beb5e201b0cc", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseSessions/FirebaseSessions.modulemap", "PRODUCT_MODULE_NAME": "FirebaseSessions", "PRODUCT_NAME": "FirebaseSessions", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c0ab614d44decc40e0ce48510ae91f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b16888a51047f345859999fd71ee4f70", "guid": "bfdfe7dc352907fc980b868725387e98a2f2b9ca91c22b1c586c82a248cbb41b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4a83c50e1274a0b9b531b50d0a5a4d0", "guid": "bfdfe7dc352907fc980b868725387e981eebf822503959435e857e5116ad819d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98642153579bec1bf9940f06f01d7bcef9", "guid": "bfdfe7dc352907fc980b868725387e987659cc5b6b47779d4dc16ca98434a415", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983981ee9f0b06e52bbdd2b745f3acaec0", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867e3d95a76dbe15ed28ba4e9fb45819f", "guid": "bfdfe7dc352907fc980b868725387e986a16029c6d4c188ff17b3bb0667c20c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8322dcd012922b56d994b6b6f6c560a", "guid": "bfdfe7dc352907fc980b868725387e98d9923d2aa5fdd90c0da5157c3b766d97"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5b94f8e7b81b1e0e0cee8da93341f19", "guid": "bfdfe7dc352907fc980b868725387e988d1b100bc6aa94f3fd56a096f7d5879e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2975554a2525b03e9a74f2b44ef3f5", "guid": "bfdfe7dc352907fc980b868725387e985806f3697a07175b43d8a7b66c4758ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a79b169f43d21f34e7da44d5a4535902", "guid": "bfdfe7dc352907fc980b868725387e980cdfaed3b2ef293ce46489841b94c04c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98783f76408a4d02b7693aa57f58486d1d", "guid": "bfdfe7dc352907fc980b868725387e98e4dabc1e61c8908e70c996d62776f413"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99fc863f089c04064eff02e72218380", "guid": "bfdfe7dc352907fc980b868725387e983db08044e9b2ae3d3322c76cbe2375c9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fdabe019998bb8c17a346792dfce4c3", "guid": "bfdfe7dc352907fc980b868725387e98769aaa959cdb141b74864a6476c6a32b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8e59ce5361a857d19e833a3f9d06bc7", "guid": "bfdfe7dc352907fc980b868725387e985d1051325db3799e1e30e847319c7248"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98293b0d70d9e84a722075a975eae4ad9c", "guid": "bfdfe7dc352907fc980b868725387e98831ff3b89dc2680df442e97accac05c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98392662fe2f5c443beb03d8669ee6d0a2", "guid": "bfdfe7dc352907fc980b868725387e988478e4bf9143c6fe69169f34f016fee1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845c0259a1e424bf74956e47aba045dc3", "guid": "bfdfe7dc352907fc980b868725387e9893f021d976bd4af220aad3ed0ac3b18e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2870658c4b1536e2c4af55216d998e", "guid": "bfdfe7dc352907fc980b868725387e9825ac3fb7cfb1fa898a85513b30a226c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9878b0166d673324f0ab8748bf1284e279", "guid": "bfdfe7dc352907fc980b868725387e98d09a4c97691e5767bcfee69457931e64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98330bc496f74403e83d4880978c3adf0d", "guid": "bfdfe7dc352907fc980b868725387e984acfe24e0760b69b2e0759cbc9f5183c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5a441f5768e054cef027531938dbbff", "guid": "bfdfe7dc352907fc980b868725387e9817d5b091a51429d4e7dc5a0b247fd0cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98366ac2a1332fa38a48636bb2ccf25585", "guid": "bfdfe7dc352907fc980b868725387e986beb83fbf0038ea31391ade101b3353c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de287733fa240722e59bcd48d9d00165", "guid": "bfdfe7dc352907fc980b868725387e988c27cbe6c6bce4a8b4d931f11f95067a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c28727e62d41eca61295a44ddbace9c6", "guid": "bfdfe7dc352907fc980b868725387e980e28ac1ccafdb38b099f54b6ffbae9c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533acc06a72d47ca27024857a67010f9", "guid": "bfdfe7dc352907fc980b868725387e98a95655c7b22bfd63da2a17a6ec8b5d16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980e17526aa0eaf28cc67140c3ea087b67", "guid": "bfdfe7dc352907fc980b868725387e985bfffbe45cc854f2760f624dbb4f8f5a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d69d28b49cc72895a38edd6a17b7c241", "guid": "bfdfe7dc352907fc980b868725387e983f9d792d39b388f2eb9c968942700c41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98896e74fad9a069f314c51fa299e3558b", "guid": "bfdfe7dc352907fc980b868725387e985e86c749550b7a8626af4cbb672079d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862e9b702488f73d0ef22725a5d72a1d8", "guid": "bfdfe7dc352907fc980b868725387e989700e9023bdb290e471946d4a6965ad4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a622d369aaafa16ef364a9b2173274e", "guid": "bfdfe7dc352907fc980b868725387e988d8171a5f040b6c9fe916bb59512e1b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982474b083e16d82a0d237ef5af99ea0e8", "guid": "bfdfe7dc352907fc980b868725387e982eb798be12fa90031d5187e463d9e39c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afc6f1e517d93492bbb7f7377603201d", "guid": "bfdfe7dc352907fc980b868725387e9891125247ba15e418dd0e179a12e82b44"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842eaa61c72faf219a7f0a34486180ae3", "guid": "bfdfe7dc352907fc980b868725387e9838aacf689e62129d38b931b20158c641"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98733a6596c6dccab6bdc13f822f08ffa5", "guid": "bfdfe7dc352907fc980b868725387e98e186b2ee9e5b8a05078e4bcb5f369c80"}], "guid": "bfdfe7dc352907fc980b868725387e9826b68836857c20493127bae08b908c24", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e987af83f47c6c646a095215e0b6f692f22"}], "guid": "bfdfe7dc352907fc980b868725387e98a4e1718e45e4ac6df8c0adc0824858ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9827427c4fe85a00def46b9a10664b5634", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations"}, {"guid": "bfdfe7dc352907fc980b868725387e98d3c8dfff2c580c352f83d3850ad17775", "name": "GoogleDataTransport"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98ed40b4d6efca84b18a65efda8999ea5d", "name": "PromisesSwift"}, {"guid": "bfdfe7dc352907fc980b868725387e980062393f91a1d2d94e3e5ed3a5aa5da9", "name": "nanopb"}], "guid": "bfdfe7dc352907fc980b868725387e98424a0579f05b8aa7b116a0e1ae14c72d", "name": "FirebaseSessions", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98a41ba860aa6fc56673ac239987133d67", "name": "FirebaseSessions.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}