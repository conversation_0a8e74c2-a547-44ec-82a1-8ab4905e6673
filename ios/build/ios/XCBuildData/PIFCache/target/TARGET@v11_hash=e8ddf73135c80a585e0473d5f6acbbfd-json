{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98603b45c5078116d252a897c578134387", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984172527ce5370f5d91ab3f45b94c9321", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833bd4762a16772410b867a704cc7883c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f7e2c6e1a29f09a4ea5fabdfbf1f3b7f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9833bd4762a16772410b867a704cc7883c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984a5a72417e4d85670d7fa2ad16accbde", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e980b44f4e97a69161eb51c261307a254d2", "guid": "bfdfe7dc352907fc980b868725387e9826f9fba3190dcb2d9d1fe337d2de3dbe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98380a768c7fc068194150cae13538893a", "guid": "bfdfe7dc352907fc980b868725387e980aca0d8a2850d95654373edf2fc07966", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986954fa85808ae1011b115897ddebfd74", "guid": "bfdfe7dc352907fc980b868725387e98240efac72002aff8baea8a01004de3f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f3370603371d3185b186355c478e0db", "guid": "bfdfe7dc352907fc980b868725387e98845b4fa01907578ded0b902bf7894fea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a0130aaa1df02475fd9fef0251f137fb", "guid": "bfdfe7dc352907fc980b868725387e9867af4fdf5449d8480e6eaecf29094bfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d83cad6fd9b87c4fb25416a1d0a122cf", "guid": "bfdfe7dc352907fc980b868725387e9857eb64f823aa0640c4eb64b7160111c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988053c47a003d916b62bbc71f1e53252a", "guid": "bfdfe7dc352907fc980b868725387e985695b7c60b0a6423e865049e5a655683"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981789202542388cf9093eeda7fbc914a6", "guid": "bfdfe7dc352907fc980b868725387e9844207416a21a7c79e1ef9a4c263e3631"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98298abe706b9687b9ea8a20f5582c1e5b", "guid": "bfdfe7dc352907fc980b868725387e98ef55cbfc1c4cb4e59301460604cd9a5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d0066bdf84397fbde0f7126c0d3806", "guid": "bfdfe7dc352907fc980b868725387e988378692422cebd11c2d070d723c2e1a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896108d1dec38e7c6fe70ebdd9a5a671b", "guid": "bfdfe7dc352907fc980b868725387e98b21d043db4b07ac2df2738d5342d66c2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4e5fafeddc64b79a65163e4f898b3cb", "guid": "bfdfe7dc352907fc980b868725387e98aab30d683e97c95687f192898ec3669c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988811cc2537c04d70b335d6387b0548a1", "guid": "bfdfe7dc352907fc980b868725387e98d6c6ec42ce0f726a0bbfbb4193d92d50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98495a27a371f9d76717c63855c26bd9ce", "guid": "bfdfe7dc352907fc980b868725387e98f10146217501d879e4cfd15fb81fdc6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859ec4c970f45529e4dad5eeac639689c", "guid": "bfdfe7dc352907fc980b868725387e98b9dceb479f169f0eb5d73313847f5c21"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e0d4361d160135b870719aa4ebaf515", "guid": "bfdfe7dc352907fc980b868725387e98f0594c580ff07e7b040c1ebff83a8d0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b59eb9ce229fdc09d6dcd58b999d2dc6", "guid": "bfdfe7dc352907fc980b868725387e98dcb7bab10842df8dcef6eb1685fbab99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e59bf09f75bdaa002ae564fbfdc6dfe", "guid": "bfdfe7dc352907fc980b868725387e9882b0029462ff6cdc296c5c311951dcc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838bc5bb69096dccb77c11b44c1f9e0ca", "guid": "bfdfe7dc352907fc980b868725387e98b8f7145195e2ae209a5c645dae73b53b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988555623e2ca17117838be3e9a265c79c", "guid": "bfdfe7dc352907fc980b868725387e98e711abe6cf6c24634e3d1347b568c8fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b84ec40a73dcc523f67aaeadc188875f", "guid": "bfdfe7dc352907fc980b868725387e9845744d8be90b4fdf6a341d8408d748d2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8acecdd2a76be0b306501215695ecac", "guid": "bfdfe7dc352907fc980b868725387e98a8ed92a5fb4b7b798797713a05df527f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f04507c3a2a23c0708f17a339bbc3f02", "guid": "bfdfe7dc352907fc980b868725387e98eb4bff64376683829b27d48b3ebde8cf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9836b2eab03d90db9cec0fe7c06adf3b87", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fea1a72b471da7ec6349129b92607334", "guid": "bfdfe7dc352907fc980b868725387e9810362d4b6ce22af135e1e3e8aa3be8a0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e85a9f9dada165d4bafe3162994350e5", "guid": "bfdfe7dc352907fc980b868725387e987f3a1ae680a2517108f83e074d3155d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802fa2de47f63f01791db9f9dea8fd90e", "guid": "bfdfe7dc352907fc980b868725387e986688f7b62c0d10fd61d94ee1fe62c576"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fb7f026495cdfc8fd6fd0c6714a7412", "guid": "bfdfe7dc352907fc980b868725387e986f2557bcf18daf00bca8aa76370f42a8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5acf21f85d2008e7adf0fbe479bda27", "guid": "bfdfe7dc352907fc980b868725387e9848eac3246c1ca0ac045c8235d9902911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a68426414f893038d52113970b790f9", "guid": "bfdfe7dc352907fc980b868725387e983720d2b11d9c911fb6f0f06f1511d65a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988d58fa7a0aaf823f65396e4d023d8baf", "guid": "bfdfe7dc352907fc980b868725387e981be5c9defb8c41526254524a9c0bf26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98268562b2549450df0264c7348bbefa1f", "guid": "bfdfe7dc352907fc980b868725387e9867dcaace7331cb3e297665965857cb6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98543e520cee62d6af44b3fe24321108d1", "guid": "bfdfe7dc352907fc980b868725387e9836a9c9b6cee3c67bdc196f610136d026"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b5ba5c79fcc60601d12f66df93aeca2", "guid": "bfdfe7dc352907fc980b868725387e98ef0143bdaad604c686103ec224fd9735"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98667eaf19e651b74ed70432d569488131", "guid": "bfdfe7dc352907fc980b868725387e98812e766a1d94830604e21de09b7b3290"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819564f6861c592d281c7e3576250484d", "guid": "bfdfe7dc352907fc980b868725387e980cadc973410fdb56ea49263b89b7ce41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98592df921005a0d46b959c669c34521e3", "guid": "bfdfe7dc352907fc980b868725387e988bd912c861ae9686012f5fd21cbaa2e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d642cf68e0d2246b108b2a46f7c7e72", "guid": "bfdfe7dc352907fc980b868725387e98aa98315f7f0b542e39016ef37f4e4de0"}], "guid": "bfdfe7dc352907fc980b868725387e98ff8d9a88aee6e93bbf0f545cf0d55aee", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e9815c2823b8daa108c84f4614ae46b1ac7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983d23d9ed1788be9d3c0cde0823465035", "guid": "bfdfe7dc352907fc980b868725387e98015a30d32b520034381667d79066849b"}], "guid": "bfdfe7dc352907fc980b868725387e984f2d3a8e138d669c28b8fb71f8aadba6", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e021448e49df671fd84ac98422daba17", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98d87ce8644be6953f9fd9c91e2e55fc46", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}