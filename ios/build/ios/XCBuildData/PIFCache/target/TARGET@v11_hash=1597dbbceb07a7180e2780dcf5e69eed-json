{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983567f76f18e738a6f514a91a4e9e5dff", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PurchasesHybridCommonUI", "PRODUCT_NAME": "PurchasesHybridCommonUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98933484b367d34b5a59fec6c92d2cbca7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c02ff4b82a2e0a7d2f9b1719ec69fb29", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommonUI", "PRODUCT_NAME": "PurchasesHybridCommonUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988ad11d483bdc4af5d3b6a0eecb30032a", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c02ff4b82a2e0a7d2f9b1719ec69fb29", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommonUI/PurchasesHybridCommonUI.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommonUI", "PRODUCT_NAME": "PurchasesHybridCommonUI", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cb8e246e12673d293550749d13dbc316", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e6284a1788b21e40515aa1d167653dfb", "guid": "bfdfe7dc352907fc980b868725387e98833d83147785d81df0a88fb46ef5887e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8ebf5eb6b03dedaa58aa1d8cafc4448", "guid": "bfdfe7dc352907fc980b868725387e98311cbdd00c76f172a8acf81560333dce", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98d7d2167ab629007a3bb807b9569d1352", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98710b764f8d059c6e5cd02e07da14aa1f", "guid": "bfdfe7dc352907fc980b868725387e98a23a2c9b2e27d6f454f3e77b5f8d2274"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e9496b30b33b3d707de07c88ce8cf7c", "guid": "bfdfe7dc352907fc980b868725387e98c9721fd6e357a67d0ea7ba67ba6f7f5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dc8ec4ba06d68190e0e35b3c3f898ca", "guid": "bfdfe7dc352907fc980b868725387e98fb67314b092823e74a280d54b2e5a408"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c840aae91a55254858cf30ed2efc8fca", "guid": "bfdfe7dc352907fc980b868725387e98de51ebcb3dd18c3f51b2f3c58340b993"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe5d7e7f533ebb54a92c54931924ebbf", "guid": "bfdfe7dc352907fc980b868725387e9847a8bfcb6edcddefbc2e4fc9b0cce9a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989efc833e47301cc4aa04b93ccf180330", "guid": "bfdfe7dc352907fc980b868725387e9882bb28a003722ccb7b373f00178e42f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890b34e7b440e78570fadda6e991064e5", "guid": "bfdfe7dc352907fc980b868725387e9859967a790d55005436e58894f3c39688"}], "guid": "bfdfe7dc352907fc980b868725387e9880ed79d9dbcc01a18579d9ef9751e1fc", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98c90fb872f07714c1215232cbd6b460c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426d8b6dca499458b2004a8d918f77f4", "guid": "bfdfe7dc352907fc980b868725387e98856b367b4107752b23c8a32e7ad3ad83"}], "guid": "bfdfe7dc352907fc980b868725387e985e8e7989ee0997928d2c832642e75782", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fd5d5126fc89b565bde85af4a573e2d4", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b0f638c99ac593829a518d6a6a45d8d0", "name": "PurchasesHybridCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e980b984833b9474c626a7e60483aa639d8", "name": "RevenueCatUI"}], "guid": "bfdfe7dc352907fc980b868725387e980db50e6ab17bd1f018f130e495fd647a", "name": "PurchasesHybridCommonUI", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983db0cd62e061b54d207c69124bcf423d", "name": "PurchasesHybridCommonUI.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}