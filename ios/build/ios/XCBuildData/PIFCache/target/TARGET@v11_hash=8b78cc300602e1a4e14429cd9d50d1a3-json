{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9872ebe51d6c217b0f4862198e3e29ed1c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e7a99df6e6a4873f55b3ad33955c73d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce0a4d5b7ce083458435670a045db06d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98961e7c9baafe5b4e26a5c59132394731", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ce0a4d5b7ce083458435670a045db06d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/AppCheckCore/AppCheckCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/AppCheckCore/AppCheckCore.modulemap", "PRODUCT_MODULE_NAME": "AppCheckCore", "PRODUCT_NAME": "AppCheckCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.5", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5e2f4ec47caf1c35a822cf8398efdf7", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9831f340e58df18026115eb1361a6c2881", "guid": "bfdfe7dc352907fc980b868725387e983a7f56a8bda7f4a32b582f3e3543ec6b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981b902e3c0ee785ed5a66bb5a4f60a0d6", "guid": "bfdfe7dc352907fc980b868725387e98eefa1fbb52d5820fb2f74287e89fa5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98afe7a4820a98ef23474260497baa6ccc", "guid": "bfdfe7dc352907fc980b868725387e98731bf14971647468f98880f5ab2ecebd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989511225c66bef28e35915a39e57dcc85", "guid": "bfdfe7dc352907fc980b868725387e985188a1987f7cf2a34653743fc646f39e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98320613041d06137a435998600e454688", "guid": "bfdfe7dc352907fc980b868725387e988f117cd19bea713411f7d886e3ee57f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5d8d73e9655c5092d9ac4c49aeb1518", "guid": "bfdfe7dc352907fc980b868725387e98f7013b8d08955f03cd9ad217b6575ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c6d0cbada08429b57abdca3780cec89", "guid": "bfdfe7dc352907fc980b868725387e98c3717fce461d8c3c727f6ab1e1ef8dd8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829bad0fb51d6aba85bbcf8825c9dd1ef", "guid": "bfdfe7dc352907fc980b868725387e98971d704d92f697bb1c3431227aeca581"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cac9cabe584c005fad6417a8a5a56da", "guid": "bfdfe7dc352907fc980b868725387e987fb0a97b5514da31c86f2442dd9c5c3f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9a4c8b9e49cb4360dc1e221512590ae", "guid": "bfdfe7dc352907fc980b868725387e98557eecafb08f3d4c91d7a27a04cbc98f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c046128141c8a3c7af649688d89a154", "guid": "bfdfe7dc352907fc980b868725387e9831a692eeee4b67a14159ea381967273a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982fb077f38a5d7ef860e9a233f62e7f7f", "guid": "bfdfe7dc352907fc980b868725387e986a533507042bc1b488b8d411b4e467dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a69df4359fc483fbd89b262b2094d329", "guid": "bfdfe7dc352907fc980b868725387e98ae7871cec43b2a430799debd34abfca4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a76bec9fd0fc7e2330bc8af93f280a28", "guid": "bfdfe7dc352907fc980b868725387e9843848d640b95d66ae7b0609d40822a18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989aa0c5ea46054fd27f9480d7e42c4eb1", "guid": "bfdfe7dc352907fc980b868725387e985df37ff259fbebfcef0385a6ec8da199"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f16bfeb9c44a72b7af5d8e02c10eeb0e", "guid": "bfdfe7dc352907fc980b868725387e98f8b9a4332a9d35d417da9e60a68e1e5f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987994af003aafd12e6f83eaaf80064226", "guid": "bfdfe7dc352907fc980b868725387e987016ef18ef8e69d7bc59d3c64c1062eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a8cb84675c6c9586bf2b846efe28ed1", "guid": "bfdfe7dc352907fc980b868725387e98638fdbf659b7db656daaabefeb0fa1e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d9b64afb9f4e781558988d30736c84d", "guid": "bfdfe7dc352907fc980b868725387e982366610e765f0b5c67b62d5d700c6ccd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988abd283e946b7782b5fc10f4fc158ca1", "guid": "bfdfe7dc352907fc980b868725387e98201a1ca6306b21611dc8f609d62d8e10"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a468f4277c49126e33ff914d441ce71", "guid": "bfdfe7dc352907fc980b868725387e9805d1768fe792537334f391c041c31f35", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98338827c00a6ff179f34d69a2dbd06e98", "guid": "bfdfe7dc352907fc980b868725387e981ca2893936b4e0ab83db00de99e3fe4b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce347e56320fbea6030c23a8f7c3c0fc", "guid": "bfdfe7dc352907fc980b868725387e98df05095938a6e6a95d183f2debb7eeca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdef008e1bba82edbd7489cd1bd0c8a7", "guid": "bfdfe7dc352907fc980b868725387e988f3992c3226d3fffed029e44a32d785f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863cf8a4fa0b163db0f5e9106d564281a", "guid": "bfdfe7dc352907fc980b868725387e98325b799f84feca7cce3ae0c158b3d6d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872879630a86044c0642dccc66bf441ee", "guid": "bfdfe7dc352907fc980b868725387e98d73be45a5c15dbc80c5e29415079a21a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd2ff42ccd3e52ed90d481f1ebee70ec", "guid": "bfdfe7dc352907fc980b868725387e9895e853d89407c6a61308b8ed634f6c83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8c469e087ccb8a7d549ef7097ab9bc", "guid": "bfdfe7dc352907fc980b868725387e983f07250a53c4dcc69dfbcf67ebdc325b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827851b5d0ea5c1d49e9dfa6a147ccdb9", "guid": "bfdfe7dc352907fc980b868725387e987481fc11365e35e19c58f07f6f4b732c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989706e20e2b0da3b2fa451dd1767c25f3", "guid": "bfdfe7dc352907fc980b868725387e985b6c7b86e2e3ddab6f8772c5362a6fba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982030fd39361260a46cc3a8792ee31402", "guid": "bfdfe7dc352907fc980b868725387e988919c6af1499779a90e7598a5e84fabe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985a9154a4a921221b2649e40f999b89ad", "guid": "bfdfe7dc352907fc980b868725387e98d6d0fe9c6b6c404e0d62b0cbcd05dfdf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a605d3c03ad54fe8f2670e769dd4b7a", "guid": "bfdfe7dc352907fc980b868725387e9810d41d30cf3c158f9509077744b70505"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a63145e0401729af08d9812b2521fd5e", "guid": "bfdfe7dc352907fc980b868725387e9865d6cb4f29a80f2f2ce4e60e0090a0bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9816b05b30c958d0b23ba26f8e9c4f5b08", "guid": "bfdfe7dc352907fc980b868725387e98ea323f1a49908e40b0b598e61db59b71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f2c3f2e1964275da88c20e112fbd86e3", "guid": "bfdfe7dc352907fc980b868725387e9866a81d67491e84a3ef0e7b3befdbdaba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982bb9e0acfdc6dfe95642628ab00ab3df", "guid": "bfdfe7dc352907fc980b868725387e98f9a04fb7607f394362f9bff3a632b600", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98917983cbea0837781fbae6cf7a54190a", "guid": "bfdfe7dc352907fc980b868725387e98c1b9cbe39894801763b170085b67c5ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9844ccf96be28795cc24f20b7ff690c650", "guid": "bfdfe7dc352907fc980b868725387e984e87e4b3e6b500cceaa46ede0ec13dfb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f25172be089a4ec84566520e0ec82a8e", "guid": "bfdfe7dc352907fc980b868725387e9804e24ee381ecd94ecdaeb870ed0114ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98239a007056616629e52849cdce4b031e", "guid": "bfdfe7dc352907fc980b868725387e987e8729dc670ba4e8dc2e7ab782a73fb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab50aeadf1c6fa86672631ba82195970", "guid": "bfdfe7dc352907fc980b868725387e980673b86a47f0fe84859c2176137bfbd7"}], "guid": "bfdfe7dc352907fc980b868725387e98dc3f6c017f50c3291e26862a62071ad6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e3ba87c0425c13524168f85c6ecdf666", "guid": "bfdfe7dc352907fc980b868725387e981251f36c01e0bb324ca1cf7a8d9bddb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d61b4430016093b62d88a999b4806232", "guid": "bfdfe7dc352907fc980b868725387e98d8db050f1c7a5fd0ec0a4596c587f92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bba6c9e2c174269551b45218a4043045", "guid": "bfdfe7dc352907fc980b868725387e98251d465f7239177f9eb05186be427f1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c7105da4ca662c3635b814c7153dade", "guid": "bfdfe7dc352907fc980b868725387e98b9bf91278debb41b3833f220f1b435bc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98755031803b7c6d2be7b87aac6863f25e", "guid": "bfdfe7dc352907fc980b868725387e98af769f021239e440439d2f37c2a8b47a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98050da7336abea5c946c1fc0f93635c48", "guid": "bfdfe7dc352907fc980b868725387e98f4410e2eefcc5e3073d1057d8a440acc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985896ae26b056149d7d5b955c3a89d470", "guid": "bfdfe7dc352907fc980b868725387e981c659a32af5c88dd60c9e7e92e503c73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a328ab4f30ef8018aa191b2759262442", "guid": "bfdfe7dc352907fc980b868725387e98385e9f712da19f3fdeab81bd33335d87"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f675cbd19db3e728f5c38aecda9c6274", "guid": "bfdfe7dc352907fc980b868725387e98a44cf29d16204615eb6acc6a177ed90d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985bcd930535f464cd37ec164d51a95a8a", "guid": "bfdfe7dc352907fc980b868725387e981759ea69ef13d9431585b07246de10e5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98def33884faa0396fceb264e4cbc264c6", "guid": "bfdfe7dc352907fc980b868725387e98c7a014a0f2696eb65fdde5d95ff8a377"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812667b22853084be6043aac847777dfd", "guid": "bfdfe7dc352907fc980b868725387e987d3c88b047f31c79453e480da1cee745"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9848b0e5c93b9adbe07118ed5e15c8fbcb", "guid": "bfdfe7dc352907fc980b868725387e98754dd466afb74dc55b83de8dbd18dd4e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98473bf39297c5501b9b7048890edc4937", "guid": "bfdfe7dc352907fc980b868725387e98d346057307b65a0c4cb63b7ce2cfbc65"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde7bcc01c973d020a402c50189f0065", "guid": "bfdfe7dc352907fc980b868725387e9831a5a7cf4456b84e8b2fb59d15681b76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eed19d489148b39459ce91989654fc9c", "guid": "bfdfe7dc352907fc980b868725387e98630b01bae14ac405faf80a562b3c32b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1a910870ae0814a9027dfe2abee02dd", "guid": "bfdfe7dc352907fc980b868725387e98c9f34912719e77ba96475461238e0e02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac10a322eb50d95306384d429bc6683", "guid": "bfdfe7dc352907fc980b868725387e984867af3440057818079d08ea0c4d6195"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4a06bcc3ada1b25eb96d1fbd8b1b84", "guid": "bfdfe7dc352907fc980b868725387e9804dbc6bc4a995fc03805e33bbdc61583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3c2a74d056a1e016c96f3f721d5190a", "guid": "bfdfe7dc352907fc980b868725387e98d92cafd93ccc407f0b67993abcefdce5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985136565a8981ec1fe144f863efadbe3c", "guid": "bfdfe7dc352907fc980b868725387e9820143a2be1e220fac8de61a11087c01f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d4d3bd980b2941605a1cdd0403a9942", "guid": "bfdfe7dc352907fc980b868725387e98f5c29ea9255f8220e21e5404dc5a3f8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984246ff4cfd4c80518a90dadecf0384ba", "guid": "bfdfe7dc352907fc980b868725387e982693cc42ebb05a72e63217341d71bd9e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0f201c821b99f7294f86ab2817f087f", "guid": "bfdfe7dc352907fc980b868725387e985d0530ef6188ebe958e1f0d3f3298d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822826356126d926fb15fac5bec2f8676", "guid": "bfdfe7dc352907fc980b868725387e9829041b1e039eb006c470ff92591bf911"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9865d8eb7bc4b75ba258fe922978bff076", "guid": "bfdfe7dc352907fc980b868725387e98b92b9cf860855796967f283ce7415d09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a0717332fa7f812308e720eccccdc09", "guid": "bfdfe7dc352907fc980b868725387e98debe9957367259f62596ede328df84af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6606995fb6e4a570c3f26cd2f3b7320", "guid": "bfdfe7dc352907fc980b868725387e98ec8f6bff25068400792a0da62301d62a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874ca5dc557e542f1494f58117a7f2c42", "guid": "bfdfe7dc352907fc980b868725387e981808cef69632857ddb48af8a1b5faf76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9811725848283e0f9cd5e663411c601989", "guid": "bfdfe7dc352907fc980b868725387e980f230ea024b72dbc683a1fabead8d6bf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835bd03e4b5b937bee1cb3b481eeac186", "guid": "bfdfe7dc352907fc980b868725387e98deca3a41ff15842151e1be841cc3fff0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856970079902f67f8725230f6c006b35c", "guid": "bfdfe7dc352907fc980b868725387e98f21ff164321f2d57d89d63e9fa8d5d8e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e449de90154d19eb8428431799fa52c3", "guid": "bfdfe7dc352907fc980b868725387e981618ab1789cddc3783305bb624a1280e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845ac59473396d887ed90c1be747b7337", "guid": "bfdfe7dc352907fc980b868725387e98c367e72e8bce33e83d8a76f05c126e9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e65f7c4abe34df76e56e48f5a675f98", "guid": "bfdfe7dc352907fc980b868725387e98c36efdc476e07ebbd7263ace396ae758"}], "guid": "bfdfe7dc352907fc980b868725387e98053600d677a0ee3a6e8294a5f6c29926", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98a7f5c11a3ddc6c6109c703c9daffc90f"}], "guid": "bfdfe7dc352907fc980b868725387e986ef21295949b1adf438aaa172cba7c15", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e987799c6ff721a2f90b939c7d64edb78cc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e985c8c1a45791dbc15ae7565c9ac08e62e", "name": "AppCheckCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}