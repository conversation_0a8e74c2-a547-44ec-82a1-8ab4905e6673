{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cbbfe6511c1a5eb6339e5880966a5a88", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98db964fa7bfe9090ff1a83a365353eada", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a55882a8728d419bf94a5c204c906d33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a77622f6369d5f28bcd23551d417bf36", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98a55882a8728d419bf94a5c204c906d33", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98feff487a488efe4ab6ff3452464539ba", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dd8bc0c29d4db726d16ba8e64480221e", "guid": "bfdfe7dc352907fc980b868725387e98ddbce2ce62061477b6738e63f2030222", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984394b56114f9f8bf0450532aba688c92", "guid": "bfdfe7dc352907fc980b868725387e989579c29b376207d91f721efda290d69e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa5829a397f7bf41f6eac387bfaa612", "guid": "bfdfe7dc352907fc980b868725387e9859064baa27ed74505a490f2ab82be9d3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bff27c21341c7d2105079b450c4bb85e", "guid": "bfdfe7dc352907fc980b868725387e98e7f4fa42d2609f168c3d4deb77793e08", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd06d3db9a73756557c2c2d24b7c1069", "guid": "bfdfe7dc352907fc980b868725387e986232b3256a78391689166b0e4ea6bdf9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889aaa88d269de2e3cb94825c264dbae1", "guid": "bfdfe7dc352907fc980b868725387e9830e3a7d49f4c3c6daef74c5de065644f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d0a9eefcb0555a31f476f405336f7a6", "guid": "bfdfe7dc352907fc980b868725387e9853c025f3f3a0adb56d7cf69804b35a49", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c6e6965b60d3e45e34e2b7247132475", "guid": "bfdfe7dc352907fc980b868725387e98fd86a597fd845880b45ada2b9bd7bb40", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895cc8e3c9e3225935b4b503895daa378", "guid": "bfdfe7dc352907fc980b868725387e984be42b1f6a564e2ab984a6b230a59dc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc1f90548880b728759c0a4bfc8fbac5", "guid": "bfdfe7dc352907fc980b868725387e987c954fa529a545364d378715750c7b83", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f2ac602fcfd761d14e4e87be53df3e9", "guid": "bfdfe7dc352907fc980b868725387e98cb9c367aad939e7a05a7804082c74636", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980932ec24270467d30c391e48bc5bdb5f", "guid": "bfdfe7dc352907fc980b868725387e98ea9f488b6c83f399d63a9dd6d0d51440", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819ce818ca5d6cd94e59161de9cf95723", "guid": "bfdfe7dc352907fc980b868725387e98dee853672a935b908d9d470c982505bd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5bb643fa65e49507c77a7f62820fe2e", "guid": "bfdfe7dc352907fc980b868725387e98322c590a4f8cd81416d0baea078c9450", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984308095565289a185913fce07cf87c55", "guid": "bfdfe7dc352907fc980b868725387e987583862dff9860d9c2ebb7d4f3fea051"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856b9a5c16801c5033281567d86c8da2c", "guid": "bfdfe7dc352907fc980b868725387e98e9b88e3dbefd354bf16615e63d406e0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce55187b47c80db5ab19f4b820343eb2", "guid": "bfdfe7dc352907fc980b868725387e98ac819c6efdded2ea15600c8b95cf4d62", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818212c7b5c6af77909710f4b9e6f7923", "guid": "bfdfe7dc352907fc980b868725387e98036de0c17d191188de3469133f846999", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986527b9df49921cb26a74f07be6e5ab3f", "guid": "bfdfe7dc352907fc980b868725387e98a571c73d7f91280659e39a2676c96d96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d35532ca876fc896706429210cf39d65", "guid": "bfdfe7dc352907fc980b868725387e98a92355c009fb687fe3c73687bbb0841c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98400c96a0335defc2f38fa820bce7a334", "guid": "bfdfe7dc352907fc980b868725387e98b360c79374ecf2f7b8ca89869fd69c99", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c1d5206cf60be939aa874a69993d22f", "guid": "bfdfe7dc352907fc980b868725387e985b391ab242094ff5b0e51afb3cb3d2d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982afd3e462ece69f82b59c54ed63d045e", "guid": "bfdfe7dc352907fc980b868725387e98213c815efb51315b65d031aa6a3d6261"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a926dca8b575399bad8f7b708266795f", "guid": "bfdfe7dc352907fc980b868725387e9834589d271e5ac134d0612bb28dbde45c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df13a6bb5f95d8d68dccb7c9b65f5f31", "guid": "bfdfe7dc352907fc980b868725387e98c13e2cc9dfd91a1e84dd4d81527377e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980bd1640675862b5f8dc79503c59295c8", "guid": "bfdfe7dc352907fc980b868725387e988e36e4b24cabc8857bb1beb02620f106", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980be4669f0b8905e49f0be52f8929c4d0", "guid": "bfdfe7dc352907fc980b868725387e98ba27078678f614ef1c40c3b156beb115", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8a093c8ef0f98b4dd8aa95c1070c229", "guid": "bfdfe7dc352907fc980b868725387e9855f99940032631a99a7e08f1ae574cb3"}], "guid": "bfdfe7dc352907fc980b868725387e988cc180d1a1cf45d5285f72f819f92fd5", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9823459689bbf5fd941723b814169ce951", "guid": "bfdfe7dc352907fc980b868725387e98f2e154211a83959b504253dec5e94607"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d62a068c6bf18654efe3aa5c3a4b73b9", "guid": "bfdfe7dc352907fc980b868725387e98857b03e14c65e03f97ad709474f01bdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98360a53df7489acf06fbfc8bfb68cf1dc", "guid": "bfdfe7dc352907fc980b868725387e98f6ca168dcf5fda7ffb6fd07575e8437f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb2bf50e6f7237198c9f0272c2a6f4fb", "guid": "bfdfe7dc352907fc980b868725387e981c703939dfc818fe4b951b859beebc11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1c2c984b54c75a7eed272da3322abe8", "guid": "bfdfe7dc352907fc980b868725387e981b7591233e6a356b80d00933c8f28076"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886d59a2d51209a0d9d7f4bd7c379c9ad", "guid": "bfdfe7dc352907fc980b868725387e98d47babbdabefdb95d1644911d64ecf63"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d8c1930128185697238d20f1a01472d", "guid": "bfdfe7dc352907fc980b868725387e98781c42e58f6fed6e0b3ca5ae603748f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9883180db84870727c09bc89cd80d650c2", "guid": "bfdfe7dc352907fc980b868725387e98393bc10cdc94d2144464e9f063c62536"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8963c01658b27a499a91c00f7e246b2", "guid": "bfdfe7dc352907fc980b868725387e986b5ccc0325638a605dd6cbf411348864"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879538741d9aa7d4c6ebc644c9fa12567", "guid": "bfdfe7dc352907fc980b868725387e980324c53132ea45721e0e5e7639bcf975"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f56e613a94d61131c5e1a23a3a1e0aad", "guid": "bfdfe7dc352907fc980b868725387e98824f6929ae7aaec2772118242f54ec2c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98282d51bd2a8d0b9f10eb4ad1c8476789", "guid": "bfdfe7dc352907fc980b868725387e98bc4b47e8f7f58567661ed324e40f4005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b597d372c6290d7f29e57e24b5761cd3", "guid": "bfdfe7dc352907fc980b868725387e98af31f0942a9337123b258e6a89b47f2b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efccfff3d34f2d6ecb33154f9e2f4940", "guid": "bfdfe7dc352907fc980b868725387e98cad71a2c669d1ad9b922c75d09a6b0ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9824d6a4f58dee60928ef035fb415866dc", "guid": "bfdfe7dc352907fc980b868725387e981ad48be2031c9e6774a29fe7da14e25a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8f076a136b02fb0b4aa4fb567fe1b8e", "guid": "bfdfe7dc352907fc980b868725387e98b4636df5c0147067840d958318c86ee2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe3d7ca4252a90849eb2243c7261963e", "guid": "bfdfe7dc352907fc980b868725387e98eef703a092d85ee55cc268789f7689ab"}], "guid": "bfdfe7dc352907fc980b868725387e98c9869469b4f1932c0a9ddfbc97f09c14", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98edd17d1854b13bfcd13b577a6167ed81"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cb3659298691235b138b8ed562c3496", "guid": "bfdfe7dc352907fc980b868725387e98687cf2de2a29eec4666359fb27bcb876"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c73172e19a4028fd2795407ba1d5abda", "guid": "bfdfe7dc352907fc980b868725387e9849510afc151be85f873ff3878bd07404"}], "guid": "bfdfe7dc352907fc980b868725387e9802e6bc63a0cd70d7399e116e983070e0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98843ae443fbacac4e666e015efb380a78", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e98e50064f8f2fc7828cb0fb06cc5b15b11", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}