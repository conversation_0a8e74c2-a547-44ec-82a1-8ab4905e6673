{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e983f2f7a097e52b2e191097de8deb24eeb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988c4b6628928e2f298fc5572ebc8ab517", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ba469489b25e75a65b591b566ce2328", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d81036cd69337ec09a44add87075221e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986ba469489b25e75a65b591b566ce2328", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e4136b7a7aa749800424b50e01f9c447", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be3bf7afd4400b7796d19a3e2ce6ff7d", "guid": "bfdfe7dc352907fc980b868725387e98d529363a10b145a949d1b0f26f248545"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5ea42db6ebc4f45215a0d55f0e6e80c", "guid": "bfdfe7dc352907fc980b868725387e98e46b66663af76f4e655d36103c2274e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e1231c28597f89e8901157980304f2ce", "guid": "bfdfe7dc352907fc980b868725387e981369152eb751b316ffb20901f65ed513"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aef469c5cf015232b7bade748dea7730", "guid": "bfdfe7dc352907fc980b868725387e981824a8eaa89488ab06153b2fa17a6efa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b49069c535527738922de7dacefb36d", "guid": "bfdfe7dc352907fc980b868725387e98ef70d9a6846815d4ba0a274915fe9239"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3c95570ed88755c79827401a1252691", "guid": "bfdfe7dc352907fc980b868725387e98e7c3b0c7385601640093dc4a0787e54c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98681ae31c0892c415e233938dd2e1dd7e", "guid": "bfdfe7dc352907fc980b868725387e98e976e5aa05ae7d732f9345f660f88b33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982f2a5542ef79948d2eeb3d68b8333015", "guid": "bfdfe7dc352907fc980b868725387e98cbd14ceef01a71b055876de99bffb3c3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cf32b3f4dc65cb7aff0deb7e3e5168a", "guid": "bfdfe7dc352907fc980b868725387e983c29ab03f25bf0b1451dcd4a24d7fdb2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffd4186bc790d1a6b702330e0c2613c0", "guid": "bfdfe7dc352907fc980b868725387e98923ace22fed01e05fc82c8ab2f6fee73"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9889d36cb80aca16121aa318fa70ef9b79", "guid": "bfdfe7dc352907fc980b868725387e9827c7fe5c0547049bee1fff2f74dd15a6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830a4acc74e605ea7db9cb553c40a1a4a", "guid": "bfdfe7dc352907fc980b868725387e98fa434982517889428503b18d31467fc4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c7fd2339e8bd3b6f39f73ff25aa43d93", "guid": "bfdfe7dc352907fc980b868725387e9850f5301970496434f0ab4043a8947461", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b0f7e43d59495bc8413c134c<PERSON><PERSON>bd", "guid": "bfdfe7dc352907fc980b868725387e9887744db38601be0edd4ee05532722f17"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d5a1657d02d6a02366e97c04404ce35", "guid": "bfdfe7dc352907fc980b868725387e9836d32cd30eee596507ddebfad5628da4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e55670cc4903b44ed91e1e53a5e9c920", "guid": "bfdfe7dc352907fc980b868725387e9826a0b0305c40112fab5f53ccdb70f5aa", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c438f9dba6f340a0bbcaf73ab43236", "guid": "bfdfe7dc352907fc980b868725387e989dd51f1be4ee1e7d5656321b2b102166"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da69852962befbd1774537ed3082782e", "guid": "bfdfe7dc352907fc980b868725387e9805dbf4d77d4e4d508c6cbda1d447e7ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874f1e135689603536fedc9bfbb0168d2", "guid": "bfdfe7dc352907fc980b868725387e984138dfc4bae6ab76e56de83319e2363a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895127f4c1a274f7e07d7d90b553bb69b", "guid": "bfdfe7dc352907fc980b868725387e986717e1c4bd95ca30a39aa30647700378"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f4713e075f82c68b08938ef7aa5c1b0", "guid": "bfdfe7dc352907fc980b868725387e98a372397093b8bf66bfef2e3ed2384ada"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ade51a5035a20af2482f6251dd49223", "guid": "bfdfe7dc352907fc980b868725387e983b10539b2c5c9fb628cabbca052281a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3acdae3d0de4d0924c626e621b76303", "guid": "bfdfe7dc352907fc980b868725387e981f4766556ebf3a574768ebd34f4c7cec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e3173650440af8264b95013fadc6be6", "guid": "bfdfe7dc352907fc980b868725387e9881a196ab0073efb52791f1f0321d8c99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c338c60b071fd0af9cc30b468b1096f7", "guid": "bfdfe7dc352907fc980b868725387e982828e7a3ae4cf5137cde081f1f8f3cab"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d124d107d490f6df5423fe59e2994cc6", "guid": "bfdfe7dc352907fc980b868725387e98fb02e78f097f686e26ec2e092d1cb481"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887bef6cc2fb1120fe1422347afa1718c", "guid": "bfdfe7dc352907fc980b868725387e981e7b14da151c3f2efeaa8cf4ef3f3687"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a071dfee934087299cddbd6ecc4490", "guid": "bfdfe7dc352907fc980b868725387e98fa2d4df03a2df28ca253d10c84e06f15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adee23c755c7b88a6e48bc2e85d98dda", "guid": "bfdfe7dc352907fc980b868725387e98e885d2986c5b000ff47860dbe99bddde"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f92832cd5679cb0d61426ab33f6eae39", "guid": "bfdfe7dc352907fc980b868725387e9846bf5089cdb3b6e637f74ea996e3a210"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f025d8849d1a41af174f3dbfbfeae35f", "guid": "bfdfe7dc352907fc980b868725387e981095baffb0daa5eb8535c345ee40aa3c"}], "guid": "bfdfe7dc352907fc980b868725387e98b3de06bd62d94ece0ac0699339924e78", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984b5c2cd742dea1d6124f0ff0276d5c84", "guid": "bfdfe7dc352907fc980b868725387e98c56c700ab16c507142e44d8d5a495840"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b74aacba9119aff14f53886c0177be7c", "guid": "bfdfe7dc352907fc980b868725387e987a43b457e62da98433183d11571d8937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5f2af009f0932fba348a7b09d6dec9d", "guid": "bfdfe7dc352907fc980b868725387e988ca26d4c0a73d15e0d12a25652e2c3b4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fad437b7220a99b43c5a5f96653755c", "guid": "bfdfe7dc352907fc980b868725387e985129f8e985b55d2c2f4575c8a6bedbcb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6620b64743ce066adc95caef76e5c56", "guid": "bfdfe7dc352907fc980b868725387e982a9cde7216430cbd8bb81ea036cd8705"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c5b828489297237610b8ed2df799bcc", "guid": "bfdfe7dc352907fc980b868725387e98f5cd664255fb9794387687e7bb32a176"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895eccc182e225b7d907876714e4e0a07", "guid": "bfdfe7dc352907fc980b868725387e9809f18b5ed2273671b54680a71b22d2b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f5644c665e86ed2ff922d2274328faa", "guid": "bfdfe7dc352907fc980b868725387e983314f228ca7f79eaacd3980ffc76768f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f76fe6871117f5d025fa42e302d66a1", "guid": "bfdfe7dc352907fc980b868725387e98021b7637b1e8e0476463563dd5588cfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825a32efa38350e01cd78374a083658e3", "guid": "bfdfe7dc352907fc980b868725387e98908009412db9c4be10ea46fa75cb4ecd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987adad0f759cf3de7f8a230171b253772", "guid": "bfdfe7dc352907fc980b868725387e98a3f4c092ac0c3f6d298681fd5207d90c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf8656cf23174feae1f84d1b2b6a5d3", "guid": "bfdfe7dc352907fc980b868725387e98f664faba207e1da8c084c8e5f00ad2ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986fa3b76acfcba31360b7a17db5834fd3", "guid": "bfdfe7dc352907fc980b868725387e9822b7ecf91c6fe2963215f6b2ec42f2ce"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c2548f1a509040538024ffdf37f9353", "guid": "bfdfe7dc352907fc980b868725387e98b3ee5680970af0dd8da1e69bdb597a49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98956db00f9978d7850052599cceb486e1", "guid": "bfdfe7dc352907fc980b868725387e988870db278f0cd7b3aba1bad95b09b8fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982dfdcb94ede1391d79fb886654114e54", "guid": "bfdfe7dc352907fc980b868725387e981b6279c460f0c8e7ac308fe7e41de1c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6df2e97c0b9bc076fe1d3a18cef3b8c", "guid": "bfdfe7dc352907fc980b868725387e98d474ef5d138c21bbc4e4bb4eedf2b2f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbc50f1132cd9a5dcc0e5a8bc1e499c5", "guid": "bfdfe7dc352907fc980b868725387e9873295931ba3ccba1cf04c83a91db7198"}], "guid": "bfdfe7dc352907fc980b868725387e98b372ca8f9d208f50bd9b3fcf0b02baa6", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e9829ca9ff16de3fb6f9cdf775ee0cc3d82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cb3659298691235b138b8ed562c3496", "guid": "bfdfe7dc352907fc980b868725387e98b2cda9c8ed303675a612f825d6f19813"}], "guid": "bfdfe7dc352907fc980b868725387e982fac2321336aef0b88b137b69ab159b8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981b12d9ead1ec57d960b1e07e3fd0c144", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e983e8ef419417eaeb1ae18310f809e7d9e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}