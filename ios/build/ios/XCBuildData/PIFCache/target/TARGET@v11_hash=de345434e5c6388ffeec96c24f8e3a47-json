{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98555cd4065fb914ac47dc7d1155f6183d", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98a3545314f418759fe46b65e7a7deb3c8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98247009b2e1577e3ab044411e49894ece", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98f35fbcccb0c6100f7ae507cd4fc9bf74", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98247009b2e1577e3ab044411e49894ece", "buildSettings": {"CODE_SIGNING_ALLOWED": "NO", "CODE_SIGNING_IDENTITY": "-", "CODE_SIGNING_REQUIRED": "NO", "CONFIGURATION_BUILD_DIR": "$(BUILD_DIR)/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)/DKImagePickerController", "EXPANDED_CODE_SIGN_IDENTITY": "-", "IBSC_MODULE": "DKImagePickerController", "INFOPLIST_FILE": "Target Support Files/DKImagePickerController/ResourceBundle-DK<PERSON>magePickerController-DKImagePickerController-Info.plist", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "PRODUCT_NAME": "DKImagePickerController", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "TARGETED_DEVICE_FAMILY": "1,2", "WRAPPER_EXTENSION": "bundle"}, "guid": "bfdfe7dc352907fc980b868725387e98b1c1d1e6d6dc170d56f345f6dc5bc1bf", "name": "Release"}], "buildPhases": [{"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e988c7569aec4e8070e76835ea8253f7918", "type": "com.apple.buildphase.sources"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e985898a936da498f0e7898be69f08eaba0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e983300409c1772d8ebb782c32a3f6d212f", "guid": "bfdfe7dc352907fc980b868725387e9886e74ec693054b65c54691bf88e3f303"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858854fddf1b11d7693b7d1a34af731ab", "guid": "bfdfe7dc352907fc980b868725387e98ad819e9097387714ba58c4894a18bf40"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98908279827a65734c3053b5e4687fa331", "guid": "bfdfe7dc352907fc980b868725387e98d8ae2d8b1ae79245f74fc0094c9a4ddb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98754e7b2a85d1b218db8635bd3860b136", "guid": "bfdfe7dc352907fc980b868725387e98a89cee85b9d40d222f8dc769869ddf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5efd40e70c7045811c5f6a3fa4a0097", "guid": "bfdfe7dc352907fc980b868725387e9838513e91d71ade63bfc681900d9df070"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980792106e281626eaf28b3f5cd11e783c", "guid": "bfdfe7dc352907fc980b868725387e986034bd742afa08bef4f67199ee5d2c68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5960fd6cbc6a829d182af8db25abed6", "guid": "bfdfe7dc352907fc980b868725387e98ebaeafa5eb170e8e0f2cd1ce4910725a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6dc5fe5c448df8a89dd9587e5439e4e", "guid": "bfdfe7dc352907fc980b868725387e985d9a99b62818c499062b8265de95e8fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff546cd9fe139720a52315964d8b4819", "guid": "bfdfe7dc352907fc980b868725387e98668e8283506d97525edb8d6ac2c4b7e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9833df20a047cf8f1670a8562ba1cd675b", "guid": "bfdfe7dc352907fc980b868725387e98fbabe156b52f42382cf7778f7f1b6b9c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800ec4bdfc6ea046c679075cd1e161a80", "guid": "bfdfe7dc352907fc980b868725387e9818b8bb9319df0d179b3659308544ce3a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e1301da06f8ed82497f38754dbe4266", "guid": "bfdfe7dc352907fc980b868725387e984a5751502fa422139c0882e70a129c32"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae687a2ac9aecd341701362bc176364a", "guid": "bfdfe7dc352907fc980b868725387e98e919c2b4459f58cc1ab08046ee0e2243"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a3792d27b0f563d34db0e2bd372984fa", "guid": "bfdfe7dc352907fc980b868725387e980878745666bc22a3a94a497a24f5c92c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fccda1ff3babc1475ea3c43ed02154e3", "guid": "bfdfe7dc352907fc980b868725387e9863180c6acd00b6827567570f427689ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e30b0579d61254af66f4ce96e12285a", "guid": "bfdfe7dc352907fc980b868725387e980612cd2a9e51af47f267078c3e88df4d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98803aaf8deb69d713f55ba78b05c01e26", "guid": "bfdfe7dc352907fc980b868725387e98216d7b321e49c55a08183ce24ceaa772"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8347a5c80cd054e9451d4cc43504327", "guid": "bfdfe7dc352907fc980b868725387e986a0b4a9cb9947e4cb5e813572cff5c1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98358cb501fe1a9a4068ab726f74db33d1", "guid": "bfdfe7dc352907fc980b868725387e98580a08c5413a9a8e88c0c45e79f804b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852fc1e814ac3df2960f1e857774480e4", "guid": "bfdfe7dc352907fc980b868725387e986d7bc40d638d6e7e2bb96db68ebcb1c8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9872eb957516a39b587e3027c06f41d8d4", "guid": "bfdfe7dc352907fc980b868725387e981e71d60a1cd12d35dbacc62761ac8838"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984607119411b1628e73150660f1fb7ad8", "guid": "bfdfe7dc352907fc980b868725387e98509715179724ff2f2f9b923760ffdd71"}], "guid": "bfdfe7dc352907fc980b868725387e982535d6999645ec00362e55e3b3b69983", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e9898fccba7a2febdedb43dddbf2e949fc3", "name": "DKImagePickerController-DKImagePickerController", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ab5e1f747dfe477b655528b07584898d", "name": "DKImagePickerController.bundle", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.bundle", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 0}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 0}], "type": "standard"}