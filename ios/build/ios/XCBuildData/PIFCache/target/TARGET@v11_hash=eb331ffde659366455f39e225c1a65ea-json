{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9838b517f81c2b10f67621372c74eafb6c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984936d7cb8feef48513f6131e211caece", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988949a3ad01b14cd8145b5c4a0c63e1ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98beaaf634ac9c45a28ea8b53ae8cea921", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988949a3ad01b14cd8145b5c4a0c63e1ac", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/Development/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9879f34f1e012c285b844492b857411475", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9893ada9e75669598303e027ccd82254c6", "guid": "bfdfe7dc352907fc980b868725387e985176eb7aa89517dd79b622378e05c296", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe72de236b322046baeb59e8e0c0b1db", "guid": "bfdfe7dc352907fc980b868725387e98af69fb08e313617716d0d228cf215245", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98862abc0cee071c1757371d63a20e988f", "guid": "bfdfe7dc352907fc980b868725387e985e2e93614b4b998292a11ff2a2c13b6d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98561f3048dcc340a380a6253ab6f0c5ca", "guid": "bfdfe7dc352907fc980b868725387e98f39594556e495895558fa1a08b655ba5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5b1607793b4090459999a21f0ccd7d1", "guid": "bfdfe7dc352907fc980b868725387e9892d2936f43a59179d3e2fc2a4021e53e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a77ab1453bfdc7f25927527e4e9dd9f", "guid": "bfdfe7dc352907fc980b868725387e98de9bba16cdb7cae0aadac8b6746bb22f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f326548dd377eca25e2c8610aa78d3d", "guid": "bfdfe7dc352907fc980b868725387e986f2fd5217ee9ef9b9f743d7150a25c60", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98680ad6251c36f4d3a2689cd04ad7de15", "guid": "bfdfe7dc352907fc980b868725387e9838280bfcd8b736867eaa4077fbc0d61c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c6f01ac4e68846e9a4415536f7e0411", "guid": "bfdfe7dc352907fc980b868725387e985212bf823b2dbcc093385db1f1b30dec", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98572fbbe63f31e66351c7c559c9c0bead", "guid": "bfdfe7dc352907fc980b868725387e983b3e9d753ef701196ebaaa81a1e77d66", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfa7988de38d421b3de2e22386b36f78", "guid": "bfdfe7dc352907fc980b868725387e985cf8cb83875e6694983c229dac8bc9fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98011b6ab2d25c8c11020d5083acebc10f", "guid": "bfdfe7dc352907fc980b868725387e988145d23612ab237ea91016460d73945a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5a9fda716622f6285f559011087eaf8", "guid": "bfdfe7dc352907fc980b868725387e982690b7c043bffc9f7499a3599fc5db55", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c25d23f0da45ed6f6b89fcc139c517d1", "guid": "bfdfe7dc352907fc980b868725387e984bcf2821c445a13f1ca5ddd1387c63d6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843f1f2d379f9d52766feac1a9e0e7e32", "guid": "bfdfe7dc352907fc980b868725387e9814e771116f0efcc61da8fc335aa4ca3b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981cec1b160d50f0356c9bfa3b2ccce654", "guid": "bfdfe7dc352907fc980b868725387e9896786428e6a8d6c7862d939cae65f773", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c154190effd5fc5cc4f660d1078aa958", "guid": "bfdfe7dc352907fc980b868725387e98790199ae3165d72360ab21230766b727", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9859ab3ec029f964cc4041f98492a33760", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987cb7fe533c37282b68193027a18c304c", "guid": "bfdfe7dc352907fc980b868725387e98ac5c52be3190abee156605bfd48912ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ec1013aba75741f88bcf1856be308fb", "guid": "bfdfe7dc352907fc980b868725387e98e1de61ab1ff97cc94cd06a5371923da8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fd2059da8ca99a011b6fc4d80edb8d6", "guid": "bfdfe7dc352907fc980b868725387e98d0242bdacab0261eec7e248947d90b7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98530e909894a207cb2e8bce94f3606f4a", "guid": "bfdfe7dc352907fc980b868725387e982d942064c23276142327490fa33537b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d070d9a01e1e385cfe1d629f8492d847", "guid": "bfdfe7dc352907fc980b868725387e98d9f834483786fc0c92f7593bcd6dd83c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af9af61ca334dde0020e9e84cade0fc4", "guid": "bfdfe7dc352907fc980b868725387e987f77bace7e1c1bfd0b3f575d26dfb06f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d23c11fde2c5880d5538a550e598ff08", "guid": "bfdfe7dc352907fc980b868725387e9815ed68575bdb30e9bf33141f83bd83b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982beefca601b2bf72dc6ca32c44a25b9c", "guid": "bfdfe7dc352907fc980b868725387e98590b8f41b31928b0a4926ce46a04415a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826cc7bd7a2c37822c94fa0951d982cdf", "guid": "bfdfe7dc352907fc980b868725387e98f6d629ffc18995e49d3d77a3aef0dcb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98934e5563c0b08fe3e1c34cba4fb19c26", "guid": "bfdfe7dc352907fc980b868725387e98af9f015ea26609a22120bcae2d16071a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9813e87070930e8a8d6695c4d2c7f5939c", "guid": "bfdfe7dc352907fc980b868725387e989c0bfab68da1fedec51cf36689157069"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98422ee3564537a4565d6c4de61ca3888a", "guid": "bfdfe7dc352907fc980b868725387e98c5820ac9b4d0a4a76d9404b84c0ec2b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988cdee145a883a1c2f51ebf3180277ac7", "guid": "bfdfe7dc352907fc980b868725387e98df9697c7b74a7cf4593f9d66eda4c228"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856454b09dcc0ec363ca2fd5a7bb91938", "guid": "bfdfe7dc352907fc980b868725387e98df10a50fc47d08262ba22e5741f99c0e"}], "guid": "bfdfe7dc352907fc980b868725387e989d91e5abbad637a65716b7fef9466ccd", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e9847d3b3fdbf8c6c03a84819a79f7ae5aa"}], "guid": "bfdfe7dc352907fc980b868725387e988ec00323e57392a3c283b014e048e33b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c4b351c20ac090098b0dcaf5fd3c18e2", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98053f546dc39b1661db608be4eb007f0a", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}