{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986b4155bd2ed8b9012a7eabcff518f128", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98390268a13964658b43ca0b5337cc58d7", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae007c1895e5af1bb47ab68741b43421", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98055586bc1a2a7e16e205290219ee1373", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ae007c1895e5af1bb47ab68741b43421", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9882fb122317f22c252075c06d94c7cb8d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f792c85e09415cd41e77ff122bd29a3b", "guid": "bfdfe7dc352907fc980b868725387e98e888dfeb7396d9f66a05b2774a1c9c8e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981e742886774993494f2f0d7920e8e322", "guid": "bfdfe7dc352907fc980b868725387e98ebd0a16df760240be8951ca1a69faae8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834c0471e003fa6aff06ff05b6dced4d8", "guid": "bfdfe7dc352907fc980b868725387e98e47982a9593bc6ea2af7d74ad76c0acd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b0c5449d37f8e425bb875663a77cce", "guid": "bfdfe7dc352907fc980b868725387e981a208ec1beb8e5fe4b6b33babb7fcba2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ddcab16a872d3939d019240a7b85c4d4", "guid": "bfdfe7dc352907fc980b868725387e9850e5ed479b1dd4fcfa17e4704621a6ff", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851c6d10e9ce8514d5138608a8605d3ce", "guid": "bfdfe7dc352907fc980b868725387e98ac0ff9c5272b9a4de3816879d44e0ff3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989de6b396335d8af19c1490400ca42782", "guid": "bfdfe7dc352907fc980b868725387e985ca516786e5acefe71446347d3d0abfc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981c6d1a41b36a27ad186db8a5ddda5cbc", "guid": "bfdfe7dc352907fc980b868725387e9855986c2020b195aeb428e69792d685c6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f76059837b69db2331ed2c20c88f2271", "guid": "bfdfe7dc352907fc980b868725387e9880bc1ea623214da05ea3db8c2fde88f8", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bdfa983680bcf4f8c59d959a86f7d6c8", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98adfeb65d371b73d878df6e94df106e18", "guid": "bfdfe7dc352907fc980b868725387e98b4671a81ee9a7ce6094b63ce5e9195a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ee0ef3a4f443b6aa61bc1742ab6562c8", "guid": "bfdfe7dc352907fc980b868725387e980280230bd09af2a47e8d0b9956a2d0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d5fcaed4bc69ae1f534db1e4d239989", "guid": "bfdfe7dc352907fc980b868725387e9838cfeef6407341dad3bb220e83ced965"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984dbebdb0f2c3fd4e0c024eed774143a2", "guid": "bfdfe7dc352907fc980b868725387e980e4de2c99b187c72f2a0f1ef4680696a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9c5a65234fb03469c94f5eb73ad93c", "guid": "bfdfe7dc352907fc980b868725387e980b5df97a9e767e72fc7368aa8209136f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1183a97c1a66b7b3110e8159eee1fa9", "guid": "bfdfe7dc352907fc980b868725387e987559f3b2a5560e6dcee28374ca291fb9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0ed3dd836e0f2b6960863ac0a61f122", "guid": "bfdfe7dc352907fc980b868725387e988a9a41583019ae4d8c60e916b5523d22"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a197eb6daf358e9ae3aa7577e09133c", "guid": "bfdfe7dc352907fc980b868725387e98d2d2bd5232f4bd30bd262e173720f644"}], "guid": "bfdfe7dc352907fc980b868725387e9880ec7b0ea4c6df9d30462f99471458c8", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e989b72d27b83e760754c7a9a90c4b8d3ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987cb3659298691235b138b8ed562c3496", "guid": "bfdfe7dc352907fc980b868725387e98f7250ddb896ceefba3ca1e99df31a763"}], "guid": "bfdfe7dc352907fc980b868725387e98a6cee4f09a80e229f136cd0ac5edbca5", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b67841baa432726687cacdb60e434a29", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}, {"guid": "bfdfe7dc352907fc980b868725387e98e35111f5b059ac88c924fd7f65b875e4", "targetReference": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46"}], "guid": "bfdfe7dc352907fc980b868725387e98fb93538a83667a0f2ae268af40086104", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98d9a0c31ef553ffd1644ea2f5fc087d46", "name": "GTMSessionFetcher-GTMSessionFetcher_Full_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}