{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c4bdae10d31a6a8ca32280e30562dc5b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e986fd1e6d701cee2019c4ffcc8be24662e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842d31a33e9a2f94e324daad78ce04f5d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987fbbcd9f52ffc8d7270fd0d309918d48", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9842d31a33e9a2f94e324daad78ce04f5d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseAppCheck/FirebaseAppCheck.modulemap", "PRODUCT_MODULE_NAME": "FirebaseAppCheck", "PRODUCT_NAME": "FirebaseAppCheck", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980f18034db5e36d2954d13efe24565cae", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9859abe97ea5c3597d463487659ef82b19", "guid": "bfdfe7dc352907fc980b868725387e9871d91a42be601b71d307300b8355ee1f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854a14762a8d97806937f5fecaeac4cf5", "guid": "bfdfe7dc352907fc980b868725387e983b665e94a4cfaa5b6a0aca002e7e59fb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98928a40e9f92974532f47c50f509b519f", "guid": "bfdfe7dc352907fc980b868725387e9806e4e0f07e830dcf1c064d4fde7372c0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a57bbd3e6b58f9220b1f54f486053efc", "guid": "bfdfe7dc352907fc980b868725387e98396b98354e97c6b1358117f08a4e306a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d375c754c44ec854c52575ea65584ae3", "guid": "bfdfe7dc352907fc980b868725387e987ac25a11fa40f178db5d50bff061db77", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bc3a9ed5ee8087d63ef148c019d1b9aa", "guid": "bfdfe7dc352907fc980b868725387e98f3b1973278086302771042df4e00ea41", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f1ead86b6a86d46e6906be98ab51d0", "guid": "bfdfe7dc352907fc980b868725387e98756f5b397ad053077de8f46d7da8fab2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ac895719fa4f86865b90ba91bfe8f4a", "guid": "bfdfe7dc352907fc980b868725387e98b1c7b574f15f7fc9aa11534e7d2b7562", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98533e84859058a135a2357e5ac544c8fa", "guid": "bfdfe7dc352907fc980b868725387e9834540f115b38beb81f79aaec362acee4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ee24808a28eab5644b4771df36327a2", "guid": "bfdfe7dc352907fc980b868725387e987738e43885705e2ca3d3f952a852d1e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875cc24025f2d92baaf07580fd9c393bd", "guid": "bfdfe7dc352907fc980b868725387e98039ec9cf2443d2dfcaa6bfd00745a082", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf19daaa3eac854abde6215fe39dc28a", "guid": "bfdfe7dc352907fc980b868725387e980b651aa249a332add6784e1a51326269", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980af2dd55acaec99c74a9a2d3f97249de", "guid": "bfdfe7dc352907fc980b868725387e988badb0d31d2706b6e4f9747d22135a0f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fc024f53043411db9d5e1b1bc2e4f0d8", "guid": "bfdfe7dc352907fc980b868725387e983155fd9f6a3b8bca94c3c79ab7d6f172", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a040beed2dd5470777d828b3c755107", "guid": "bfdfe7dc352907fc980b868725387e98900d260dc13755009dc5e9a87b094ce7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad6bd0e0e26e58a77264f68b04fcd167", "guid": "bfdfe7dc352907fc980b868725387e9828b6a38764c56d071ba48fdbb255f533"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98abc2332b1c9a396111c83ee063a3301e", "guid": "bfdfe7dc352907fc980b868725387e98f05d93bb93fdddbb0e4e7929359c57b7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d48ff65828f2114cf71d23e4a859a401", "guid": "bfdfe7dc352907fc980b868725387e983a4581eb617a5ad7d83892d698d594b6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e2f4a070364912345fbeab094b4fab8", "guid": "bfdfe7dc352907fc980b868725387e9849ccab35060d3b04350d5b2ec96a6be4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98befa3e4f57c1872484e475570ef2a1e3", "guid": "bfdfe7dc352907fc980b868725387e98ee4dfe3e5170a4d901e0ea6c5751579f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c603dba162b28489ab8dc9468d247931", "guid": "bfdfe7dc352907fc980b868725387e9885d6d8d78881bf3885d75b32e70b1277"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9f10d17bd20b4732439b68143976605", "guid": "bfdfe7dc352907fc980b868725387e98764cfede42317a5d2c6a5172733c7e97", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c08f346fb28e8a6c990d9182dc2c9caa", "guid": "bfdfe7dc352907fc980b868725387e98ce2868d00d05d2d2211051232eb2df1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ca8d8d68ae7def8a17b03baba1d0296", "guid": "bfdfe7dc352907fc980b868725387e98cd6526b0c21c31085bd5878a26ea1a52", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838325ac9de8fd7978328f4cfc09d5525", "guid": "bfdfe7dc352907fc980b868725387e9889255affc0e671e70bb08e4e4f3f6e7c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d5d5de292aa78c838f007448912e6f4", "guid": "bfdfe7dc352907fc980b868725387e9815dca42c43fccc27a7b13ec4db07705a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b71e32cc3dac53d4f33351bb0bd0e69", "guid": "bfdfe7dc352907fc980b868725387e9879c2596fd3a27cc64d264f803ac04a3d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e597eaa2317387db1cb9b123fc4a326e", "guid": "bfdfe7dc352907fc980b868725387e98f42c7113b7322f4073dcac9b6e96d7d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985614ee9de55490ff642875b3285c7fdf", "guid": "bfdfe7dc352907fc980b868725387e9812f35353a4585fb67a3e91a122e4961f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5037e06da4cc4f5fb82306d0f8722a5", "guid": "bfdfe7dc352907fc980b868725387e98ede8ff609a807a6a6f7f3501653a1530"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3e270a2b37c73d50ad48c93555d4587", "guid": "bfdfe7dc352907fc980b868725387e981f72ce5bfe0f0920fcf3a20705e8519e"}], "guid": "bfdfe7dc352907fc980b868725387e989a8bdd7db64c936feeb9fdccde76a124", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3c0bde5b6202216742298490da6bf9f", "guid": "bfdfe7dc352907fc980b868725387e98f2d8328aa3716bcf58165cb6aabdd9eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ca259fe2d4008f07f1cb1f4e67c5e73", "guid": "bfdfe7dc352907fc980b868725387e980a6842fc579249ca27117522fcb32520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bd52513b0212be374603f03578bd509", "guid": "bfdfe7dc352907fc980b868725387e98c5bcc8cfdebaa2ea4c37d0e8cf801bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fded065ac51c9567adfac72eb9c9e68b", "guid": "bfdfe7dc352907fc980b868725387e980d69a15c5090c41cfe80991e6b959490"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e71242dd10509eee29fc56e7de775b33", "guid": "bfdfe7dc352907fc980b868725387e987c40da3cb9a8bee11591bb9fcfbf9747"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b36958b79115b4d189341b3d93325425", "guid": "bfdfe7dc352907fc980b868725387e981e211ce9b5a82c38a89df7432af5ae11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983473044e98c19bbc95f61f07cfe01082", "guid": "bfdfe7dc352907fc980b868725387e983e0ead0ddd47065d3ee3316a6a456558"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c909d1c2fa3a6fb86674d953cebd19fa", "guid": "bfdfe7dc352907fc980b868725387e98a10b53c251fead24e2bc80ffabaca7e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98507dc382e04a5f370dd5144d72e7b73f", "guid": "bfdfe7dc352907fc980b868725387e98908b25b6de0d398e2db2ab88c7de1b95"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879b4e1dbd6328bd90356973eff6b6fda", "guid": "bfdfe7dc352907fc980b868725387e981532a1ac14391b4b492680fe682b0957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c92b9bc8cf2eb60e860ad3bdb8fa1e0", "guid": "bfdfe7dc352907fc980b868725387e9818ac9bb25ea11222d5e6d5d5016eeec6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987455c67526ce2a9011bf36f5f3943f90", "guid": "bfdfe7dc352907fc980b868725387e98aace04b8dc34a35748654fa1fa5d33a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe89ccd621742d4312986f59bc5087bd", "guid": "bfdfe7dc352907fc980b868725387e980435a41cdea476bf7b11f4bfcd411ec4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdd1a18d8c1aa7beef67e5268730f177", "guid": "bfdfe7dc352907fc980b868725387e984695a5858a5c3c95172aebe1fe52346f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5a94ef5a29b6006ac2796068193cf4f", "guid": "bfdfe7dc352907fc980b868725387e98fad322e08034ceb08d597725afeba077"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a5c887cdb768c9d208e9cdb7a4ea3cb4", "guid": "bfdfe7dc352907fc980b868725387e98b739033624abbc444cdbd9c09bdf3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0108abcc3931d0752a33dfbc28a26ca", "guid": "bfdfe7dc352907fc980b868725387e9840a011651d32a8a7d65ff37efb37da99"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818f02e483e5335dd1ea4594b3325177d", "guid": "bfdfe7dc352907fc980b868725387e98a2eb62cb148ada087de53721959663a9"}], "guid": "bfdfe7dc352907fc980b868725387e980f6ad4bb86e2126a469e3678db6ab533", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e989ad225b4241890eee92fd5876f740106"}], "guid": "bfdfe7dc352907fc980b868725387e980c126ec4e291ef8d9fbbbb38989292f4", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9808b47889de662cd4865776eda02c0c2b", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98cd8162b601eb6c17e4d86eec112a388c", "name": "AppCheckCore"}, {"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98ecaebc2b66f6675fbaa388164aa6c8dd", "name": "FirebaseAppCheck", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9857de7acecfe5aa305e96dd28add8de37", "name": "FirebaseAppCheck.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}