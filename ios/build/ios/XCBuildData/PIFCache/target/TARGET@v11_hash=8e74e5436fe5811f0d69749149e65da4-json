{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9893ff35c3a0706b80edce8aee84966070", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98435e0ea9a8b03db6f91355d257f4417f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895625674f623b38625dc5fa422148e6e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98c54a8df7acf7972b55d061e1f2117be4", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895625674f623b38625dc5fa422148e6e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PurchasesHybridCommon/PurchasesHybridCommon.modulemap", "PRODUCT_MODULE_NAME": "PurchasesHybridCommon", "PRODUCT_NAME": "PurchasesHybridCommon", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.7", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98874bcb09d1c2d8e90796de0badf6f009", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a3ba5e221d06a3eb6112570f8684ca92", "guid": "bfdfe7dc352907fc980b868725387e986a1712e5ab574fcb9898ae94a6f6985e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b61ec3b67cb73f9f9645e079fe932fc", "guid": "bfdfe7dc352907fc980b868725387e98465a8884da7800813ea40b7119c6b050", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98f53851bad6b1d8fe0061df8980a728d6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988f95b715262ff2851998e0a97276218f", "guid": "bfdfe7dc352907fc980b868725387e98f05257beafdf64d2ce2f51e537d4d265"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9842910b1ee086db358561fb9efee0177a", "guid": "bfdfe7dc352907fc980b868725387e98ba29e5864394a2cba6e7c40ee81d2e23"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d887f7d8b0e81d65fc965de44decd05", "guid": "bfdfe7dc352907fc980b868725387e9864b2e906742e8170a827ee6c6ed8d201"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bcd562c8f2b7edd99c8b38d10428a906", "guid": "bfdfe7dc352907fc980b868725387e987a2862ed41a87f0dc46647e06346dc7a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9812c7de408971ca87bf3a1683b5ab3c51", "guid": "bfdfe7dc352907fc980b868725387e98c764a2194550c5bd32b97a400da76072"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98186154aefec6ac45f38e68aa483e79df", "guid": "bfdfe7dc352907fc980b868725387e98076b425ef874ec4454b3bbf21a9ba5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c391b961faf7cf8ac6888eb6153f1b0a", "guid": "bfdfe7dc352907fc980b868725387e9860a3bb3ce9074c881fa7b774e7846c1a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d65e2e6cb9a3d1f2ff77d60b84fb855", "guid": "bfdfe7dc352907fc980b868725387e9838769bd73116d4dbf96d1af01c5bdcc1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a22d9a2582909e557d2310dfafd956c7", "guid": "bfdfe7dc352907fc980b868725387e98f6ad72d859e84a253213976ba7dbfe16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ba9eeea1678aa3310e9bfb0569e4815", "guid": "bfdfe7dc352907fc980b868725387e98cce702c4e18026c7dba8c490ed3e6b28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9839d23b96243f06c12662d47499042206", "guid": "bfdfe7dc352907fc980b868725387e98d11131e3889e48f834759a3f2bbcefbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984742f6dbc0e8b08fb08ada05b1904a1c", "guid": "bfdfe7dc352907fc980b868725387e985a05c07bbe49a909de9bf5b4e10e4fdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ab875d5574a87b7398b5e320f3cadc5", "guid": "bfdfe7dc352907fc980b868725387e9886f854dc13f01d91b433f52e12baf36a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989416e34a6d7e0615ff1245f79fbeb5e5", "guid": "bfdfe7dc352907fc980b868725387e984ef3533798584db8a3bdd8369da6c6b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864905b67d832074eeab3eb4dfbd8666b", "guid": "bfdfe7dc352907fc980b868725387e98da4cee3911ebdb3e20c41bda701687c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fddf3a6eb3ceecbe673318d1613c980", "guid": "bfdfe7dc352907fc980b868725387e982d0446f8d22e9d7623ff029d71d76675"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98411c02e733625a6c0b8df9bdefbac35f", "guid": "bfdfe7dc352907fc980b868725387e98b000bfcb3578f0d8dc9b9632318cf6e7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f39f764fa54cc1d2f13fed4196d2950b", "guid": "bfdfe7dc352907fc980b868725387e98ee74d18214f266b23b77e42078cf53f7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ccac31eae0ba240acdb6b112fa9629bd", "guid": "bfdfe7dc352907fc980b868725387e980fe6608f1dde41fd31869f46ee69cf89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98201b883aa21e8bf1b24e4bd979e37c11", "guid": "bfdfe7dc352907fc980b868725387e98052e44f3859a7d2620b73c162855e308"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec28d24137223e53be8bd2dd82b8b72d", "guid": "bfdfe7dc352907fc980b868725387e989aa6a3be3eea968830b656bb451a7d14"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db3a4618d0b576d8de467dffa41e580a", "guid": "bfdfe7dc352907fc980b868725387e98a682689bfa1d925508da895c01a18ea7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb3d4fa0cc2a4bcf0aee96780dd71868", "guid": "bfdfe7dc352907fc980b868725387e98926f2f9808edc63330b97d3bc8452b89"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9856a1c6d0fa77f348608e9c6a9d096afb", "guid": "bfdfe7dc352907fc980b868725387e9869dce9c4e99523636a161ccc2acaeb36"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c258fa43331ea255615be74faf0e254", "guid": "bfdfe7dc352907fc980b868725387e98d1b7df5fd98ee20603c14e6640bde5aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c92c925db87a7844883be7040e39c7e7", "guid": "bfdfe7dc352907fc980b868725387e98c7773ebfd5c32e9441a86f4cdd34ccc5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae29aa8d0989ea61971a57e7dfda5ec6", "guid": "bfdfe7dc352907fc980b868725387e983489f43e1a248754d5c56943d3a6ef91"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b642dcf34d017ad64b3fa4c7b8114c5", "guid": "bfdfe7dc352907fc980b868725387e9856766d09e2db2903dc06ad5dd1b763f5"}], "guid": "bfdfe7dc352907fc980b868725387e98d52b529d8b513b0dc69932753c9a661b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e986c5a2fbe9348844b2759df8bd2d2e3fc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98162606f01a9caa7d11ea36d219fba588", "guid": "bfdfe7dc352907fc980b868725387e98f71df51b178b7e7f4b7c3868802682f5"}], "guid": "bfdfe7dc352907fc980b868725387e9885c08393e1208f815d731144353c05bb", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9871cd9f492b1050d5410d38e0e21de50d", "targetReference": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a"}], "guid": "bfdfe7dc352907fc980b868725387e98baab191a7ba1493163c1677b6188bb8e", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9821a12af8510f5a5a4842f587aedc172a", "name": "PurchasesHybridCommon-PurchasesHybridCommon"}, {"guid": "bfdfe7dc352907fc980b868725387e9833260f06b6ffe5cdf6831b2907a4b8de", "name": "RevenueCat"}], "guid": "bfdfe7dc352907fc980b868725387e98b0f638c99ac593829a518d6a6a45d8d0", "name": "PurchasesHybridCommon", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988760891fc732fcd8db57a04e95d983d2", "name": "PurchasesHybridCommon.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}