{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982f5f691636233269c8b7bc26f89d6b75", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f3a54b9aea088a1b66c6a562011936d1", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320cd0847a4693043e534774d964b45a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984749db58ea7fa202e79bb2e885eabefa", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98320cd0847a4693043e534774d964b45a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseABTesting/FirebaseABTesting.modulemap", "PRODUCT_MODULE_NAME": "FirebaseABTesting", "PRODUCT_NAME": "FirebaseABTesting", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980e96c6775af48d021f0b47061f608ce3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98f8e38c49f707bb8d637089ae5cc6e726", "guid": "bfdfe7dc352907fc980b868725387e98f55aee071a72b86609d6ceb4ce3ab9d0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988374bd6642297d912924cc349a04bf9c", "guid": "bfdfe7dc352907fc980b868725387e98fe430310c00c943bef69b91593f55394"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d70bcd2926323d75b88f3900a997f084", "guid": "bfdfe7dc352907fc980b868725387e9889add301146c7851d58dd290fa551025"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b1b00bead2008ec6797bda19ee1109d", "guid": "bfdfe7dc352907fc980b868725387e98c8d03ac6c165bd78373710b73b8272e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98866e4db9691d0451a5c68c015090b582", "guid": "bfdfe7dc352907fc980b868725387e9820838336ade0080830c8647dbddcd312"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981be70ad0d1c7b67a7642db01c753b0c1", "guid": "bfdfe7dc352907fc980b868725387e989220101d7eb1d174a02571019e02fd6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981a5050e8b27aec65bca43a7dceaa75d7", "guid": "bfdfe7dc352907fc980b868725387e9870b18c0f9b1446bf0b1465497b2569c3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847704dc850ec2108c621cd94c19c8e97", "guid": "bfdfe7dc352907fc980b868725387e981a60db32b99ab3ca40235d767777c26e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830565b6270b43ae3e355aafbe3b50ce4", "guid": "bfdfe7dc352907fc980b868725387e9818716609c392fc1a54100fd49d0762d6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98194b763e40286ca3894aa882876ada0e", "guid": "bfdfe7dc352907fc980b868725387e98f5d19dd053122f646a8130f555c655c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980d0762d6994b753f936bda0a8048d159", "guid": "bfdfe7dc352907fc980b868725387e98680446b38460c60a1f0c87c8f0655eba", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b651d9d1405246125fa3dd5b0afb30", "guid": "bfdfe7dc352907fc980b868725387e9896752d9266443a2b09f3f68fdce8847c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986533818c8c3d6d10c9b811be645de077", "guid": "bfdfe7dc352907fc980b868725387e9856c7ccfffd77e4cee0f693d166184822"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821c7bfd2490e938e7005c8fc4a7ad004", "guid": "bfdfe7dc352907fc980b868725387e9829930f7e1dac9f9a946b5ea31917c16d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ea9f36467b678b04cda3bdfcf3462f7", "guid": "bfdfe7dc352907fc980b868725387e98b2f0da75cce58ea18c3b4bfb042221d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9867434860cc6ce95084d00d4fb1c3f859", "guid": "bfdfe7dc352907fc980b868725387e98e5b4de85d03ab8fa057632babb5d1097"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989eeda3a46ea3c06aa3c0122d767c9b82", "guid": "bfdfe7dc352907fc980b868725387e98499d5471a8065b2ffab4216350c5c042"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98586641d59efcead285875ae1254c53ee", "guid": "bfdfe7dc352907fc980b868725387e982aa14a819ab730e875dc60a7a1f0161d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8cf331aba96c386916604c2e6e18668", "guid": "bfdfe7dc352907fc980b868725387e9814dd9e7ed17e497c6fc2df9ee057bd0f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2238c1d97801b437c68ac213a006e92", "guid": "bfdfe7dc352907fc980b868725387e98d13b386bfb89518a71c7cd0464df0142"}], "guid": "bfdfe7dc352907fc980b868725387e98bdcbc398a299c67f9bed7ddd1b5006d9", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c3eefd705177f32917c88520004311fe", "guid": "bfdfe7dc352907fc980b868725387e9829189617204659c6be286d9668a1f0ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ebb3a415bd029fb753e2bad824a679d4", "guid": "bfdfe7dc352907fc980b868725387e98a2febd41a891c9dbd63b34bb1975423a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988dde7c032e11819f8692c6455f93840b", "guid": "bfdfe7dc352907fc980b868725387e981272a1bfe76471aff74af7769939624d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982814dc3488777871d96dde8a14171eff", "guid": "bfdfe7dc352907fc980b868725387e98328057a0dace22682d7bae24f79deabf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986d4b88894f549d5515d345ae27f09e40", "guid": "bfdfe7dc352907fc980b868725387e98bfbbaebd05de0814025587043c289fa7"}], "guid": "bfdfe7dc352907fc980b868725387e984e75fb0231159d2ae12478fe6a4aef14", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e981ea2bd43171fb1f7a8c7cdb3abc90301"}], "guid": "bfdfe7dc352907fc980b868725387e98941f19e532909628f947c2b1eacd62e3", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98aeee8e4bd80c0cd0e5923ecd0f516ebb", "targetReference": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443"}], "guid": "bfdfe7dc352907fc980b868725387e98bf20b91962bb2064b712e09cccee4dbc", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98b9cce05bc25808e2e3952904a4034443", "name": "FirebaseABTesting-FirebaseABTesting_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}], "guid": "bfdfe7dc352907fc980b868725387e984d1b80eb520d7ec9828b3cb4e14dcb65", "name": "FirebaseABTesting", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98388ecc0b6beee3823c42c78ba6025714", "name": "FirebaseABTesting.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}