{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b8317b56fb7155b48b77f39a4a692c39", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d99ab8377df8f96212d9f95cba3ca740", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b326ea89ee54afca6abca9f8e9d4612b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e980c2e634d47691e18d78d0f2f6c8ed58f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b326ea89ee54afca6abca9f8e9d4612b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GCC_PREFIX_HEADER": "Target Support Files/RecaptchaInterop/RecaptchaInterop-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "11.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/RecaptchaInterop/RecaptchaInterop.modulemap", "PRODUCT_MODULE_NAME": "RecaptchaInterop", "PRODUCT_NAME": "RecaptchaInterop", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ddac29cbc87df09d4710981cd837331b", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98a19c6d9172949fb36dd3eeff9e371fd7", "guid": "bfdfe7dc352907fc980b868725387e98393963993565678a86200abd9eea1903", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c57311287006361e29199ebd8f25809", "guid": "bfdfe7dc352907fc980b868725387e98a53e114263bdf8e828d90862da57a524", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d9098a2cde4d8b62b4a2d6834e6c57f", "guid": "bfdfe7dc352907fc980b868725387e98f8a5094b31d74c5ca3fa595c1e90e2f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b585bddd382f5195191c44afb9e85173", "guid": "bfdfe7dc352907fc980b868725387e98740e635cb193e7590ed27988bf0ce5b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf034c0d1c22eb2c138ac71ce3f43e86", "guid": "bfdfe7dc352907fc980b868725387e987574012e98b187f9e7f3b7d866ba7e2f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98602583150b3c88696b7535565c26d38f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9872a51348632a2363a8e65f7901b04f29", "guid": "bfdfe7dc352907fc980b868725387e98954c7da9bb4cd2a7e68d8d8de1b0d538"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143f6ecb45a3a98e5919160996e4d87c", "guid": "bfdfe7dc352907fc980b868725387e988bf1d85bdf749483384e31c5f8b8514e"}], "guid": "bfdfe7dc352907fc980b868725387e98d63880d4d9e85082b794166feecec9af", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e98ee1b7c38d9cb80446e1c2b5261709cbd"}], "guid": "bfdfe7dc352907fc980b868725387e98cec6554d59ba68d11f69b5ba9eb16a5c", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e9860c62f5b0bb945dd781b120cdb353998", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [], "guid": "bfdfe7dc352907fc980b868725387e98da29652de71b686743df2bd56decf7ef", "name": "RecaptchaInterop", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988c2a56cd963a48a63ab689fe60f47236", "name": "RecaptchaInterop.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}