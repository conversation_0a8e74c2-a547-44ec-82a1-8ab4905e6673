{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982b13c82dd763197bba588e30fb4f547a", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98397f3b28db379dee488c18d8126563fa", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d81ee284e67be562f8f967d6b9299da", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9828a30000f6a897d5117d2c9edb604143", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e984d81ee284e67be562f8f967d6b9299da", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e988eca73e17c5c4c511da3dcbf3140cb09", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98518410a38a4b43aec9db1df813daaa31", "guid": "bfdfe7dc352907fc980b868725387e980f9bb3d96b5563cfa4ba32fb9efbae00", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c05e69eb4f1cf9850b26746664ed1af", "guid": "bfdfe7dc352907fc980b868725387e98a8e7c9af2e68b09317f16fa52329d0df", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983c1b392ca4129c41989db549d42c42e4", "guid": "bfdfe7dc352907fc980b868725387e9834727efe623ce486642bfe91640f7b7e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821918d4ce157bf744b3e0257d81bfae7", "guid": "bfdfe7dc352907fc980b868725387e98aeee473d66e09b71ca8b836f7e343a96", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98871be52e792cda416b3bc7573a8367ea", "guid": "bfdfe7dc352907fc980b868725387e98e575e5c1f2d567c2991c47a1ad138889", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982444667ef3f10664882a5698f887fbbd", "guid": "bfdfe7dc352907fc980b868725387e98bef3e1dbbbb26b3e1e134c2b1803196e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851e0e0a264ae76dffda44b9c437ca630", "guid": "bfdfe7dc352907fc980b868725387e98f05f588cbb4123775e04b26346e3fba6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9893e78d7e7851d237e5edbdf3de82b3a1", "guid": "bfdfe7dc352907fc980b868725387e982aa63efaf31ac1fc327ceda2af4dcae5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e3fc58396745bb0f171a26e5a00b885", "guid": "bfdfe7dc352907fc980b868725387e98663c724cc3785961b9aa0305a987959d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b08525df2f72b6b213aa114e049af0e3", "guid": "bfdfe7dc352907fc980b868725387e988dae31ac6e461e88cb80a626cb656481", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98303fe08c34d9a9877ff33ad88db47fac", "guid": "bfdfe7dc352907fc980b868725387e983d9366bba70c48e30a62b67d04715f57", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983767ca7515b52e1778fdc06ed6d056a9", "guid": "bfdfe7dc352907fc980b868725387e9842bedeae259729912d3bddba0dd06bd7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822c65184544ec6f9f674a74fc953b7ac", "guid": "bfdfe7dc352907fc980b868725387e98dbfc3df21f2ef2a0317ada22a2745093", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8bbef1c78bf88b46d643faaf43b3222", "guid": "bfdfe7dc352907fc980b868725387e986553063f1c34e7e23315e730c2dbb60e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989112b05b8dca6a93a1074db48134190f", "guid": "bfdfe7dc352907fc980b868725387e981a32bae3e1dc90a0de7ece67e6f7a4b7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982688b9e32d3b0093ba48ca40abc67c27", "guid": "bfdfe7dc352907fc980b868725387e9832ecc3cc04d4ce98e2580d4581a61960", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98633cce446a69367a84ce1728ac390df7", "guid": "bfdfe7dc352907fc980b868725387e982eccb03e1957e49e460245b86bfa2064", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894567bb124de7ec705a0ce09c2d265c7", "guid": "bfdfe7dc352907fc980b868725387e9805bb3f5699c40103fd3fe52cee0ef63f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b22f157e1415f7975dfdac0eba050149", "guid": "bfdfe7dc352907fc980b868725387e98f0667bf4a3c894dd4e9ac2bfd9ae2211", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840002a9803fb08975160d3d1353141eb", "guid": "bfdfe7dc352907fc980b868725387e98adc009474203c3a97155de344a6884da", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897c67c6048e59571a52bf62a6a64f313", "guid": "bfdfe7dc352907fc980b868725387e98acabcbceb173c76f37b9c916104a670d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9809da4b02535ff523166deb50ec2324ab", "guid": "bfdfe7dc352907fc980b868725387e9831dfc62f886ef49b0c91b582e17fbed9", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9850172d4fd86e86200ecec31b791e8e46", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98be5d137015a0a2dfbd632307419fdb4f", "guid": "bfdfe7dc352907fc980b868725387e980d89c4554db462f92e6e96f2e30653f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892755a71e19e0f9dda7e9235d4486856", "guid": "bfdfe7dc352907fc980b868725387e98ff37186773e06e970e959f9e1f11f9f8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98391c0ffa4bd1021ed0eb90596dc97a69", "guid": "bfdfe7dc352907fc980b868725387e9824373693fec88894f09ce6d814b2d0d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9845a00949b77891a03b98100503117f03", "guid": "bfdfe7dc352907fc980b868725387e98e27f1ead86a835718f68e043aa507900"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801e217a7c0aff1fcaab57ba1061b1ee7", "guid": "bfdfe7dc352907fc980b868725387e9867f16e40ef061be87e698ce5be7f3eb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4ab26289e3710a5aa04081b0e7605f", "guid": "bfdfe7dc352907fc980b868725387e9899d9a22a294943f8844fa4448ae5dc7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e8b0d5da550c5c04e22999597214c0eb", "guid": "bfdfe7dc352907fc980b868725387e982c029502015ac88df98ea01dd17b323e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2fce3c4f242ba237a0f6f15d0de3b16", "guid": "bfdfe7dc352907fc980b868725387e98bc2d3c5e66dfd2c286e18d5b9a26f28a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988123709862980a9665314f986480a04d", "guid": "bfdfe7dc352907fc980b868725387e9851ff87a4435353b398db9f121e99c28c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987eadab3289eb14491a2e26a0e0d53947", "guid": "bfdfe7dc352907fc980b868725387e986374001033c65cb9ae1e596ca967197e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1dae43b002ffb10869769d2c871bb75", "guid": "bfdfe7dc352907fc980b868725387e98331d7639ed7000299d68e8153aeb49f5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb4d0221d1c21524fa8e2963fd70e8b7", "guid": "bfdfe7dc352907fc980b868725387e98c97ede13623e8aeb33898db923bfaa7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc667cabaa8205c4dcddc8e259314b34", "guid": "bfdfe7dc352907fc980b868725387e987f2c0158d922b7148492a8f9c26fb3f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98049e2ca74830a634aac9d33d0aebcd3d", "guid": "bfdfe7dc352907fc980b868725387e98988f5560efcf71781f85b2b7fb1d747b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d5ad54a114d3a05ce77643877bc82369", "guid": "bfdfe7dc352907fc980b868725387e98845e25a8aaf804e0321d132d047915b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d44b3ae8955a7bbd6cf47fabcafa8619", "guid": "bfdfe7dc352907fc980b868725387e98ba772d0a163c99801e4ec703f3fb5ffc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980910e85ced8738e5d0d64412e61aec99", "guid": "bfdfe7dc352907fc980b868725387e981fb5403f225a8437de9d3683b3e8c3da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831d3ed37af75156a413fea2a3f9777a3", "guid": "bfdfe7dc352907fc980b868725387e988faff5cdd8fbb5edcfb146204db34775"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b8ebe911e40de2deab4585113314c90c", "guid": "bfdfe7dc352907fc980b868725387e987f63518df6e28aa7cb1c7ec9aa62d903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e70639981d3b387e25d745605b73962a", "guid": "bfdfe7dc352907fc980b868725387e9854d51584447b21bc6594e16dff003b86"}], "guid": "bfdfe7dc352907fc980b868725387e98e59275e296a03f86340d79503966629e", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ac470025e90ba866a83f1fca4026942e", "guid": "bfdfe7dc352907fc980b868725387e9871d6fd81db5b440f82eb5777f95cb0f2"}], "guid": "bfdfe7dc352907fc980b868725387e98e984b1e6ddc706ffd90a79a68b53781d", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98c82dd2b9acb7dfcfb379f360860f2142", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e9822b0d9dc136979c8f5be7d4ba17be0bd", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}