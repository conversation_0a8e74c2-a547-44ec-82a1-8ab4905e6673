<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>RoamR</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>RoamR</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>GMSApiKey</key>
	<string>AIzaSyAZhNUKz89RuhNd_36TWJFm-bhCSLmFI5Q</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Your current location is needed to be attached to this itinerary</string>
	<key>NSCameraUsageDescription</key>
	<string>This app needs camera access to take photos for your trip itineraries</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>This app needs photo library access to add photos to your trip itineraries</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>This app needs microphone access for video recording</string>

	<!-- Apple Sign-In Configuration -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>auth</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.pankaj6apr.roamr</string>
				<string>com.googleusercontent.apps.597644255226-7q9ss9h9fcsik4avt6ehgbr39vhpu4fi</string>
			</array>
		</dict>
	</array>
	<key>com.apple.developer.applesignin</key>
	<array>
		<string>Default</string>
	</array>
	<key>GIDClientID</key>
	<string>597644255226-7q9ss9h9fcsik4avt6ehgbr39vhpu4fi.apps.googleusercontent.com</string>
	<key>NSUserNotificationsUsageDescription</key>
	<string>This app needs to send you notifications to remind you to plan your trips and itineraries.</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>NSCriticalAlertUsageDescription</key>
	<string>This app would like to send important notifications about your trip reminders.</string>
</dict>
</plist>
