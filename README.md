# Roamr

A Flutter-based trip planning and management application.

## Features

- Trip planning and management
- Offline support
- Multi-language support
- Dark/Light theme
- Firebase Analytics integration
- Crashlytics for crash reporting
- Performance monitoring

## Firebase Setup

### Prerequisites

1. Create a new project in the [Firebase Console](https://console.firebase.google.com/)
2. Add Android and iOS apps to your Firebase project
3. Download the configuration files:
   - For Android: `google-services.json` (place in `android/app/`)
   - For iOS: `GoogleService-Info.plist` (place in `ios/Runner/`)

### Firebase Analytics

Firebase Analytics is already integrated into the project. To use it in your code:

```dart
import 'package:roamr/firebase/analytics_service.dart';

// Log an event
AnalyticsService().logEvent(
  name: 'button_click',
  parameters: {'button_name': 'submit'},
);

// Log screen view
AnalyticsService().logScreenView(screenName: 'home_screen');

// Set user properties
AnalyticsService().setUserProperty(
  name: 'account_type',
  value: 'premium',
);
```

### Crashlytics

Crashlytics is set up to automatically capture crashes and non-fatal errors.

### Performance Monitoring

Performance monitoring is enabled by default and will automatically track app startup time, HTTP requests, and more.

## Development

### Getting Started

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Set up Firebase as described above
4. Run the app with `flutter run`

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.
