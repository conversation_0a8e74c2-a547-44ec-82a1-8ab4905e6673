# Supabase Edge Functions for RoamR

This directory contains Edge Functions for the RoamR app.

## API Keys Function

The `api-keys` function securely stores and retrieves API keys for the RoamR app. It requires authentication to access the keys.

### Deployment

To deploy the Edge Function, follow these steps:

1. Make sure you have the Supabase CLI installed:
   ```bash
   brew install supabase/tap/supabase
   ```

2. Login to your Supabase account:
   ```bash
   supabase login
   ```

3. Link your project:
   ```bash
   supabase link --project-ref <your-project-ref>
   ```
   You can find your project reference in the Supabase dashboard URL: `https://app.supabase.com/project/<your-project-ref>`

4. Deploy the function:
   ```bash
   supabase functions deploy api-keys --no-verify-jwt
   ```

5. Set the function to require authentication:
   - Go to your Supabase dashboard
   - Navigate to Edge Functions
   - Find the `api-keys` function
   - Click on the settings icon
   - Enable "JWT verification"

### Usage

To use the function in your Flutter app, use the `ApiKeyService` class:

```dart
final apiKeyService = ApiKeyService();
final googleMapsApiKey = await apiKeyService.getGoogleMapsApiKey();
final revenueCatApiKey = await apiKeyService.getRevenueCatApiKey();
```

The service will automatically handle authentication and caching of API keys.

### Security Considerations

- The Edge Function requires authentication, so only authenticated users can access the API keys.
- API keys are not stored in the client-side code, reducing the risk of exposure.
- The function can be extended to implement rate limiting, logging, or additional security measures.
