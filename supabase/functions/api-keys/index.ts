// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

// Define the API keys structure
interface ApiKeys {
  google_maps: string;
  revenue_cat_ios: string;
  revenue_cat_android: string;
}

// Create a secure API key store
const API_KEYS: ApiKeys = {
  google_maps: "AIzaSyAZhNUKz89RuhNd_36TWJFm-bhCSLmFI5Q",
  revenue_cat_ios: "appl_aOrZMbCyicwJLqbwkVMIpcZQaIH",
  revenue_cat_android: "appl_aOrZMbCyicwJLqbwkVMIpcZQaIH"
};

serve(async (req) => {
  // CORS headers
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Content-Type': 'application/json'
  };

  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers });
  }

  try {
    // Create a Supabase client with the Deno runtime
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function.
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    );

    // Get the user from the request
    const {
      data: { user },
    } = await supabaseClient.auth.getUser();

    // If no user is found, return an error
    if (!user) {
      return new Response(
        JSON.stringify({ error: 'Unauthorized' }),
        { headers, status: 401 }
      );
    }

    // Parse the request URL to get the key type
    const url = new URL(req.url);
    const keyType = url.searchParams.get('key');

    // If no key type is provided, return all keys
    if (!keyType) {
      return new Response(
        JSON.stringify({ keys: API_KEYS }),
        { headers, status: 200 }
      );
    }

    // If the key type is not valid, return an error
    if (!Object.keys(API_KEYS).includes(keyType)) {
      return new Response(
        JSON.stringify({ error: `Invalid key type: ${keyType}` }),
        { headers, status: 400 }
      );
    }

    // Return the requested key
    return new Response(
      JSON.stringify({ key: API_KEYS[keyType as keyof ApiKeys] }),
      { headers, status: 200 }
    );
  } catch (error) {
    // Return any errors
    return new Response(
      JSON.stringify({ error: error.message }),
      { headers, status: 500 }
    );
  }
})

/* To invoke:
  1. Go to your Supabase project dashboard
  2. Navigate to Edge Functions
  3. Deploy this function
  4. Invoke using authenticated client:

     const { data, error } = await supabase.functions.invoke('api-keys', {
       queryParams: { key: 'google_maps' }
     })
*/
