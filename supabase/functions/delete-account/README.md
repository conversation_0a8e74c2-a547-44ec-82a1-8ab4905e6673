# Delete Account Edge Function

This Edge Function provides a secure way to completely delete a user's account from Supabase Auth, allowing them to re-register with the same email if they wish.

## How It Works

1. The function is called from the client app when a user wants to delete their account
2. It uses the user's JWT to identify them
3. It then uses the Supabase admin API with the SERVICE_ROLE key to delete the user from auth.users
4. This completely removes the user's auth record, allowing them to re-register with the same email

## Deployment

To deploy this function to your Supabase project:

1. Make sure you have the Supabase CLI installed:
   ```
   npm install -g supabase
   ```

2. Login to your Supabase account:
   ```
   supabase login
   ```

3. Deploy the function:
   ```
   supabase functions deploy delete-account --project-ref YOUR_PROJECT_REF
   ```

   Replace `YOUR_PROJECT_REF` with your Supabase project reference ID, which you can find in your Supabase dashboard.

## Security Considerations

- This function uses the SERVICE_ROLE key, which has admin privileges
- The key is stored securely as an environment variable on the Supabase server
- The function verifies the user's identity using their JWT before performing any operations
- Only the authenticated user can delete their own account

## Testing

You can test this function using the Supabase CLI:

```
supabase functions serve delete-account
```

Then, in another terminal:

```
curl -i --location --request POST 'http://localhost:54321/functions/v1/delete-account' \
  --header 'Authorization: Bearer YOUR_JWT_TOKEN' \
  --header 'Content-Type: application/json'
```

Replace `YOUR_JWT_TOKEN` with a valid JWT token for a user in your Supabase project.
