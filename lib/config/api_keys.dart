import 'dart:io';
import 'package:flutter/foundation.dart';

/// Contains all the API keys and configuration for the app
class ApiKeys {
  // Platform detection helpers
  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get isWeb => kIsWeb;
  // RevenueCat API Keys
  static const String revenueCatIOS = 'appl_aOrZMbCyicwJLqbwkVMIpcZQaIH';
  static const String revenueCatAndroid = 'goog_lIedlvlEGZvKIZAYdiDsiPteldn';

  // Google Maps API Key
  static const String googleMaps = 'AIzaSyAZhNUKz89RuhNd_36TWJFm-bhCSLmFI5Q';

  // Google OAuth
  static const String googleIosClientId = '597644255226-7q9ss9h9fcsik4avt6ehgbr39vhpu4fi.apps.googleusercontent.com';
  static const String googleServerClientId = '597644255226-9ths0isfd9ru5icphj1tegurfdmgd0rg.apps.googleusercontent.com';
  static const String googleReversedClientId = 'com.googleusercontent.apps.597644255226-7q9ss9h9fcsik4avt6ehgbr39vhpu4fi';

  // Supabase Credentials
  static const String supabaseUrl = 'https://hzfdzfukbwhyviyryoey.supabase.co';
  static const String supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh6ZmR6ZnVrYndoeXZpeXJ5b2V5Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NDY0NDgsImV4cCI6MjA1ODIyMjQ0OH0.A7pViVDji7Ux0uN2llC8Oea-NT7ePZuddj3obD8AH7s';
  
  // OAuth Configuration
  static const String oauthCallbackScheme = 'roamr';
  static const String oauthCallbackHost = 'login-callback';
  
  // Apple OAuth Configuration
  static const String appleOAuthPath = 'auth/v1/authorize';
  static const String appleTokenPath = 'auth/v1/token';
  
  /// Returns the OAuth callback URL for the current platform
  static String getOAuthCallbackUrl() {
    if (kIsWeb) {
      return '$supabaseUrl/auth/v1/callback';
    } else {
      return '$oauthCallbackScheme://$oauthCallbackHost';
    }
  }
  
  // Apple Sign-In Configuration
  // iOS: Use the Bundle ID (e.g., 'com.example.roamr')
  // Android: Use the package name (e.g., 'com.example.roamr')
  // Web: Use the Services ID (e.g., 'com.example.roamr.web')
  static const String appleClientId = 'com.pankaj6apr.roamr';
  static const String appleWebClientId = 'com.pankaj6apr.roamr.web';
  
  // Apple Services ID (from Apple Developer Portal)
  static const String appleServicesId = 'com.pankaj6apr.roamr.service';
  
  // Apple Team ID (from Apple Developer Account)
  static const String appleTeamId = 'NZ9KMQCV29';
  
  // Apple Key ID (from Apple Developer Account, for JWT)
  static const String appleKeyId = '7H829JNBF5';
  
  // Apple Private Key (from Apple Developer Account, for JWT)
  // Note: In production, store this securely and don't commit it to version control
  static const String applePrivateKey = '''
******************************************************************************************************************************************************************************************************************************************************************''';
  
  /// Returns the OAuth redirect URL for the current platform
  static String getOAuthRedirectUrl() {
    return getOAuthCallbackUrl();
  }
  
  /// Returns the appropriate client ID based on the platform
  static String getAppleClientId() {
    if (isWeb) {
      return appleWebClientId;
    } else if (isIOS) {
      return appleClientId;
    } else if (isAndroid) {
      return '$appleClientId';
    }
    return appleClientId; // Default
  }
  
  /// Returns the redirect URI for Apple Sign-In
  static String getAppleRedirectUri() {
    if (isWeb) {
      return '$supabaseUrl/auth/v1/callback';
    } else if (isIOS) {
      return 'https://$supabaseUrl/auth/v1/callback';
    } else if (isAndroid) {
      return 'https://$supabaseUrl/auth/v1/callback';
    }
    return 'https://$supabaseUrl/auth/v1/callback'; // Default
  }
}
