import 'package:equatable/equatable.dart';
import 'package:roamr/data/trip_database.dart';

class Trip extends Equatable {
  final String id;
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String? city;
  final String? countryCode;
  final String? countryName;

  const Trip({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.city,
    this.countryCode,
    this.countryName,
  });

  @override
  List<Object?> get props => [id, title, startDate, endDate, city, countryCode, countryName];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'city': city,
      'countryCode': countryCode,
      'countryName': countryName,
    };
  }

  static Trip fromJson(Map<String, dynamic> json) {
    return Trip(
      id: json['id'],
      title: json['title'],
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      city: json['city'],
      countryCode: json['countryCode'],
      countryName: json['countryName'],
    );
  }

  static Trip fromTripItem(TripItem tripItem) {
    return Trip(
      id: tripItem.id,
      title: tripItem.title,
      startDate: tripItem.startDate,
      endDate: tripItem.endDate,
      city: tripItem.city,
      countryCode: tripItem.countryCode,
      countryName: tripItem.countryName,
    );
  }
}
