import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/trip_itinerary.dart';

enum Category {
  all,
  other,
  sightseeing,
  restaurant,
  flight,
  entertainment,
  accommodation,  
  transportation,
  note,
  activity,
  carRental,
  shopping,
  parking,
  movie,;

  String toJson() => name;
  static Category fromJson(String json) => values.byName(json);
}

extension CategoryX on Category {
  String getName(BuildContext context) {
    final localizations = AppLocalizations.of(context);
    switch (this) {
      case Category.all:
        return localizations?.all ?? 'All';
      case Category.entertainment:
        return localizations!.entertainment;
      case Category.restaurant:
        return localizations!.restaurant;
      case Category.other:
        return localizations!.other;
      case Category.accommodation:
        return localizations!.accommodation;
      case Category.transportation:
        return localizations!.transportation;
      case Category.sightseeing:
        return localizations!.sightseeing;
      case Category.shopping:
        return localizations!.shopping;
      case Category.activity:
        return localizations!.activity;
      case Category.parking:
        return localizations!.parking;
      case Category.note:
        return localizations!.note;
      case Category.movie:
        return localizations!.movie;
      case Category.flight:
        return localizations!.flight;
      case Category.carRental:
        return localizations!.carRental;
    }
  }

  static Category fromString(String category) {
    switch (category) {
      case 'all':
        return Category.all;
      case 'entertainment':
        return Category.entertainment;
      case 'restaurant':
        return Category.restaurant;
      case 'other':
        return Category.other;
      case 'accommodation':
        return Category.accommodation;
      case 'transportation':
        return Category.transportation;
      case 'sightseeing':
        return Category.sightseeing;
      case 'shopping':
        return Category.shopping;
      case 'activity':
        return Category.activity;
      case 'parking':
        return Category.parking;
      case 'note':
        return Category.note;
      case 'movie':
        return Category.movie;
      case 'flight':
        return Category.flight;
      case 'carRental':
        return Category.carRental;
      default:
        return Category.all;
    }
  }

  bool apply(TripItinerary? expense) => switch (this) {
    Category.all => true,
    Category.entertainment => expense?.category == Category.entertainment,
    Category.restaurant => expense?.category == Category.restaurant,
    Category.other => expense?.category == Category.other,
    Category.accommodation => expense?.category == Category.accommodation,
    Category.transportation => expense?.category == Category.transportation,
    Category.sightseeing => expense?.category == Category.sightseeing,
    Category.shopping => expense?.category == Category.shopping,
    Category.activity => expense?.category == Category.activity,
    Category.parking => expense?.category == Category.parking,
    Category.note => expense?.category == Category.note,
    Category.movie => expense?.category == Category.movie,
    Category.carRental => expense?.category == Category.carRental,
    Category.flight => expense?.category == Category.flight,
  };

  Iterable<TripItinerary?> applyAll(Iterable<TripItinerary?> expenses) {
    return expenses.where(apply);
  }
}
