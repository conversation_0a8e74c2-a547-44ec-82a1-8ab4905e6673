import 'package:roamr/l10n/app_localizations.dart';

/// Enum representing achievement types
enum AchievementType {
  destination,
  expense,
  frequency,
  duration,
  flight,
}

/// Model class for achievements
class Achievement {
  final String id;
  final String title;
  final String description;
  final AchievementType type;
  final bool isUnlocked;
  final int progress;
  final int level;
  final int targetValue;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    this.isUnlocked = false,
    this.progress = 0,
    required this.level,
    required this.targetValue,
  });

  /// Get localized title
  String getLocalizedTitle(AppLocalizations localizations) {
    final titleKey = "${id}_title";

    // Use reflection to get the localized string
    try {
      // This is a dynamic approach to access the localization properties
      final property = localizations.toString().contains(titleKey)
          ? titleKey
          : null;

      if (property != null) {
        // Use reflection to get the property value
        final value = (localizations as dynamic)[property];
        if (value != null && value is String) {
          return value;
        }
      }
    } catch (_) {
      // Fallback to hardcoded title if any error occurs
    }

    return title;
  }

  /// Get localized description
  String getLocalizedDescription(AppLocalizations localizations) {
    final descKey = "${id}_desc";

    // Use reflection to get the localized string
    try {
      // This is a dynamic approach to access the localization properties
      final property = localizations.toString().contains(descKey)
          ? descKey
          : null;

      if (property != null) {
        // Use reflection to get the property value
        final value = (localizations as dynamic)[property];
        if (value != null && value is String) {
          return value;
        }
      }
    } catch (_) {
      // Fallback to hardcoded description if any error occurs
    }

    return description;
  }

  /// Create a copy of this achievement with the given fields replaced
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    AchievementType? type,
    bool? isUnlocked,
    int? progress,
    int? level,
    int? targetValue,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      progress: progress ?? this.progress,
      level: level ?? this.level,
      targetValue: targetValue ?? this.targetValue,
    );
  }

  /// Convert achievement to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'isUnlocked': isUnlocked,
      'progress': progress,
    };
  }

  /// Create achievement from JSON
  factory Achievement.fromJson(Achievement baseAchievement, Map<String, dynamic> json) {
    return baseAchievement.copyWith(
      isUnlocked: json['isUnlocked'] ?? false,
      progress: json['progress'] ?? 0,
    );
  }
}

/// Predefined achievements
class Achievements {
  /// Destination-based achievements
  static Achievement noviceTraveller = Achievement(
    id: 'novice_traveller',
    title: 'Novice Traveller',
    description: 'Visit 3 or more different countries',
    type: AchievementType.destination,
    level: 1,
    targetValue: 3,
  );

  static Achievement worldTraveller = Achievement(
    id: 'world_traveller',
    title: 'World Traveller',
    description: 'Visit 10 or more different countries',
    type: AchievementType.destination,
    level: 2,
    targetValue: 10,
  );

  static Achievement globetrotter = Achievement(
    id: 'globetrotter',
    title: 'Globetrotter',
    description: 'Visit 20 or more different countries',
    type: AchievementType.destination,
    level: 3,
    targetValue: 20,
  );

  static Achievement continentalExplorer = Achievement(
    id: 'continental_explorer',
    title: 'Continental Explorer',
    description: 'Visit 2 or more continents and at least 20 countries',
    type: AchievementType.destination,
    level: 4,
    targetValue: 2,
  );

  static Achievement continentalCollector = Achievement(
    id: 'continental_collector',
    title: 'Continental Collector',
    description: 'Visit 4 or more continents and at least 25 countries',
    type: AchievementType.destination,
    level: 5,
    targetValue: 4,
  );

  static Achievement worldConqueror = Achievement(
    id: 'world_conqueror',
    title: 'World Conqueror',
    description: 'Visit all 7 continents and at least 30 countries',
    type: AchievementType.destination,
    level: 6,
    targetValue: 7,
  );

  /// Expense-based achievements
  static Achievement budgetTracker = Achievement(
    id: 'budget_tracker',
    title: 'Budget Tracker',
    description: 'Track \$1,000+ in total expenses',
    type: AchievementType.expense,
    level: 1,
    targetValue: 1000,
  );

  static Achievement expenseManager = Achievement(
    id: 'expense_manager',
    title: 'Expense Manager',
    description: 'Track \$5,000+ in total expenses',
    type: AchievementType.expense,
    level: 2,
    targetValue: 5000,
  );

  static Achievement financialVoyager = Achievement(
    id: 'financial_voyager',
    title: 'Financial Voyager',
    description: 'Track \$10,000+ in total expenses',
    type: AchievementType.expense,
    level: 3,
    targetValue: 10000,
  );

  static Achievement luxuryTraveller = Achievement(
    id: 'luxury_traveller',
    title: 'Luxury Traveller',
    description: 'Track \$20,000+ in total expenses',
    type: AchievementType.expense,
    level: 4,
    targetValue: 20000,
  );

  /// Trip frequency achievements
  static Achievement travelBeginner = Achievement(
    id: 'travel_beginner',
    title: 'Travel Beginner',
    description: 'Complete 3 or more trips',
    type: AchievementType.frequency,
    level: 1,
    targetValue: 3,
  );

  static Achievement travelEnthusiast = Achievement(
    id: 'travel_enthusiast',
    title: 'Travel Enthusiast',
    description: 'Complete 10 or more trips',
    type: AchievementType.frequency,
    level: 2,
    targetValue: 10,
  );

  static Achievement travelAddict = Achievement(
    id: 'travel_addict',
    title: 'Travel Addict',
    description: 'Complete 20 or more trips',
    type: AchievementType.frequency,
    level: 3,
    targetValue: 20,
  );

  /// Duration-based achievements
  static Achievement dayTripper = Achievement(
    id: 'day_tripper',
    title: 'Day Tripper',
    description: 'Complete a trip of at least 1 day',
    type: AchievementType.duration,
    level: 1,
    targetValue: 1,
  );

  static Achievement weekendWanderer = Achievement(
    id: 'weekend_wanderer',
    title: 'Weekend Wanderer',
    description: 'Complete a trip of at least 3 days',
    type: AchievementType.duration,
    level: 2,
    targetValue: 3,
  );

  static Achievement vacationVoyager = Achievement(
    id: 'vacation_voyager',
    title: 'Vacation Voyager',
    description: 'Complete a trip of at least 7 days',
    type: AchievementType.duration,
    level: 3,
    targetValue: 7,
  );

  static Achievement extendedExplorer = Achievement(
    id: 'extended_explorer',
    title: 'Extended Explorer',
    description: 'Complete a trip of at least 14 days',
    type: AchievementType.duration,
    level: 4,
    targetValue: 14,
  );

  static Achievement longTermTraveler = Achievement(
    id: 'long_term_traveler',
    title: 'Long-term Traveler',
    description: 'Complete a trip of at least 30 days',
    type: AchievementType.duration,
    level: 5,
    targetValue: 30,
  );

  static Achievement nomadicAdventurer = Achievement(
    id: 'nomadic_adventurer',
    title: 'Nomadic Adventurer',
    description: 'Complete a trip of at least 60 days',
    type: AchievementType.duration,
    level: 6,
    targetValue: 60,
  );

  /// Flight-based achievements
  static Achievement firstFlight = Achievement(
    id: 'first_flight',
    title: 'Flight Beginner',
    description: 'Record 5 or more flights',
    type: AchievementType.flight,
    level: 1,
    targetValue: 5,
  );

  static Achievement frequentFlyer = Achievement(
    id: 'frequent_flyer',
    title: 'Frequent Flyer',
    description: 'Record 15 or more flights',
    type: AchievementType.flight,
    level: 2,
    targetValue: 15,
  );

  static Achievement aviationEnthusiast = Achievement(
    id: 'aviation_enthusiast',
    title: 'Aviation Enthusiast',
    description: 'Record 30 or more flights',
    type: AchievementType.flight,
    level: 3,
    targetValue: 30,
  );

  /// Get all predefined achievements
  static List<Achievement> getAllAchievements() {
    return [
      // Destination achievements
      noviceTraveller,
      worldTraveller,
      globetrotter,
      continentalExplorer,
      continentalCollector,
      worldConqueror,

      // Expense achievements
      budgetTracker,
      expenseManager,
      financialVoyager,
      luxuryTraveller,

      // Frequency achievements
      travelBeginner,
      travelEnthusiast,
      travelAddict,

      // Duration achievements
      dayTripper,
      weekendWanderer,
      vacationVoyager,
      extendedExplorer,
      longTermTraveler,
      nomadicAdventurer,

      // Flight achievements
      firstFlight,
      frequentFlyer,
      aviationEnthusiast,
    ];
  }

  /// Get destination-based achievements
  static List<Achievement> getDestinationAchievements() {
    return [
      noviceTraveller,
      worldTraveller,
      globetrotter,
      continentalExplorer,
      continentalCollector,
      worldConqueror,
    ];
  }

  /// Get expense-based achievements
  static List<Achievement> getExpenseAchievements() {
    return [
      budgetTracker,
      expenseManager,
      financialVoyager,
      luxuryTraveller,
    ];
  }

  /// Get frequency-based achievements
  static List<Achievement> getFrequencyAchievements() {
    return [
      travelBeginner,
      travelEnthusiast,
      travelAddict,
    ];
  }

  /// Get duration-based achievements
  static List<Achievement> getDurationAchievements() {
    return [
      dayTripper,
      weekendWanderer,
      vacationVoyager,
      extendedExplorer,
      longTermTraveler,
      nomadicAdventurer,
    ];
  }

  /// Get flight-based achievements
  static List<Achievement> getFlightAchievements() {
    return [
      firstFlight,
      frequentFlyer,
      aviationEnthusiast,
    ];
  }
}
