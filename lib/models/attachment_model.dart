import 'dart:io';
import 'package:path/path.dart' as p;

enum AttachmentType {
  photo,
  document,
  // We can add more types in the future (audio, video, etc.)
}

class AttachmentModel {
  final String id;
  final String itineraryId;
  final AttachmentType type;
  final String filePath; // This is a relative path
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? appDocumentsPath; // Optional app documents path

  AttachmentModel({
    required this.id,
    required this.itineraryId,
    required this.type,
    required this.filePath,
    this.createdAt,
    this.updatedAt,
    this.appDocumentsPath,
  });

  // Get the absolute file path
  String get absoluteFilePath {
    if (appDocumentsPath != null) {
      return p.join(appDocumentsPath!, filePath);
    }
    return filePath;
  }

  // Get the file object
  File get file => File(absoluteFilePath);

  // Check if the file exists
  bool get exists => file.existsSync();

  // Create a copy with updated fields
  AttachmentModel copyWith({
    String? id,
    String? itineraryId,
    AttachmentType? type,
    String? filePath,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? appDocumentsPath,
  }) {
    return AttachmentModel(
      id: id ?? this.id,
      itineraryId: itineraryId ?? this.itineraryId,
      type: type ?? this.type,
      filePath: filePath ?? this.filePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      appDocumentsPath: appDocumentsPath ?? this.appDocumentsPath,
    );
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'itineraryId': itineraryId,
      'type': type.toString().split('.').last,
      'filePath': filePath,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  // Create from JSON
  factory AttachmentModel.fromJson(Map<String, dynamic> json, {String? appDocumentsPath}) {
    return AttachmentModel(
      id: json['id'],
      itineraryId: json['itineraryId'],
      type: _stringToType(json['type']),
      filePath: json['filePath'],
      createdAt: json['createdAt'] != null ? DateTime.fromMillisecondsSinceEpoch(json['createdAt']) : null,
      updatedAt: json['updatedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(json['updatedAt']) : null,
      appDocumentsPath: appDocumentsPath,
    );
  }

  // Helper method to convert string to AttachmentType enum
  static AttachmentType _stringToType(String type) {
    switch (type) {
      case 'photo':
        return AttachmentType.photo;
      case 'document':
        return AttachmentType.document;
      default:
        return AttachmentType.photo; // Default to photo
    }
  }
}
