import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/models/category.dart';

class TripItinerary extends Equatable {
  final String id;
  final String title;
  final double? amount;
  final DateTime date;
  final Category category;
  final LatLng? location;
  final String? locationText;
  final String? airlineName;
  final String? flightNumber;
  final DateTime? checkoutDate;
  final String? description;
  final int? rank;

  const TripItinerary({
    required this.id,
    required this.title,
    required this.amount,
    required this.date,
    required this.category,
    this.location,
    this.locationText,
    this.airlineName,
    this.flightNumber,
    this.checkoutDate,
    this.description,
    this.rank,
  });

  @override
  List<Object?> get props => [
    id,
    title,
    amount,
    date,
    category,
    location,
    locationText,
    airlineName,
    flightNumber,
    checkoutDate,
    description,
    rank,
  ];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'amount': amount,
      'date': date.microsecondsSinceEpoch,
      'category': category.toString(),
      'location': location != null ? {'lat': location!.latitude, 'lng': location!.longitude} : null,
      'locationText': locationText,
      'airlineName': airlineName,
      'flightNumber': flightNumber,
      'checkoutDate': checkoutDate?.microsecondsSinceEpoch,
      'description': description,
      'rank': rank,
    };
  }

  factory TripItinerary.fromJson(Map<String, dynamic> json) {
    return TripItinerary(
      id: json['id'],
      title: json['title'],
      amount: double.tryParse(json['amount'].toString()) ?? 0.0,
      date: DateTime.fromMicrosecondsSinceEpoch(json['date']),
      category: Category.fromJson(json['category']),
      location: json['location'] != null ? LatLng(json['location']['lat'] as double, json['location']['lng'] as double) : null,
      locationText: json['locationText'],
      airlineName: json['airlineName'],
      flightNumber: json['flightNumber'],
      checkoutDate: json['checkoutDate'] != null
          ? DateTime.fromMicrosecondsSinceEpoch(json['checkoutDate'])
          : null,
      description: json['description'],
      rank: json['rank'] != null ? int.parse(json['rank'].toString()) : null,
    );
  }

  TripItinerary copyWith({
    String? id,
    String? title,
    double? amount,
    DateTime? date,
    Category? category,
    LatLng? location,
    String? locationText,
    String? airlineName,
    String? flightNumber,
    DateTime? checkoutDate,
    String? description,
    int? rank,
  }) {
    return TripItinerary(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      location: location,
      locationText: locationText ?? this.locationText,
      airlineName: airlineName ?? this.airlineName,
      flightNumber: flightNumber ?? this.flightNumber,
      checkoutDate: checkoutDate ?? this.checkoutDate,
      description: description ?? this.description,
      rank: rank ?? this.rank,
    );
  }
}
