class UserUsage {
  final int tripsCreated;
  final int attachmentsCreated;
  final int itinerariesCreated;

  UserUsage({
    required this.tripsCreated,
    required this.attachmentsCreated,
    required this.itinerariesCreated,
  });

  factory UserUsage.fromJson(Map<String, dynamic> json) {
    return UserUsage(
      tripsCreated: json['trips_created'] ?? 0,
      attachmentsCreated: json['attachments_created'] ?? 0,
      itinerariesCreated: json['itineraries_created'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() => {
    'trips_created': tripsCreated,
    'attachments_created': attachmentsCreated,
    'itineraries_created': itinerariesCreated,
  };
} 