import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_de.dart';
import 'app_localizations_en.dart';
import 'app_localizations_es.dart';
import 'app_localizations_fr.dart';
import 'app_localizations_hi.dart';
import 'app_localizations_th.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('de'),
    Locale('en'),
    Locale('es'),
    Locale('fr'),
    Locale('hi'),
    Locale('th')
  ];

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @language_desc.
  ///
  /// In en, this message translates to:
  /// **'Change your preferred language'**
  String get language_desc;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @grocery.
  ///
  /// In en, this message translates to:
  /// **'Grocery'**
  String get grocery;

  /// No description provided for @food.
  ///
  /// In en, this message translates to:
  /// **'Food'**
  String get food;

  /// No description provided for @work.
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get work;

  /// No description provided for @entertainment.
  ///
  /// In en, this message translates to:
  /// **'Entertainment'**
  String get entertainment;

  /// No description provided for @traveling.
  ///
  /// In en, this message translates to:
  /// **'Traveling'**
  String get traveling;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @total_expenses.
  ///
  /// In en, this message translates to:
  /// **'Total Expenses'**
  String get total_expenses;

  /// No description provided for @select_catagory.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get select_catagory;

  /// No description provided for @category.
  ///
  /// In en, this message translates to:
  /// **'Category'**
  String get category;

  /// No description provided for @your_trips.
  ///
  /// In en, this message translates to:
  /// **'Your Trips'**
  String get your_trips;

  /// No description provided for @no_trips.
  ///
  /// In en, this message translates to:
  /// **'No trips added yet.'**
  String get no_trips;

  /// No description provided for @add_trip.
  ///
  /// In en, this message translates to:
  /// **'Add Trip'**
  String get add_trip;

  /// No description provided for @edit_trip.
  ///
  /// In en, this message translates to:
  /// **'Edit Trip'**
  String get edit_trip;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @enter_title.
  ///
  /// In en, this message translates to:
  /// **'Enter trip name'**
  String get enter_title;

  /// No description provided for @date_range.
  ///
  /// In en, this message translates to:
  /// **'When are you going?'**
  String get date_range;

  /// No description provided for @select_date_range.
  ///
  /// In en, this message translates to:
  /// **'Please select a date range'**
  String get select_date_range;

  /// No description provided for @country.
  ///
  /// In en, this message translates to:
  /// **'Country'**
  String get country;

  /// No description provided for @select_country.
  ///
  /// In en, this message translates to:
  /// **'Select a country'**
  String get select_country;

  /// No description provided for @country_hint.
  ///
  /// In en, this message translates to:
  /// **'Where are you going?'**
  String get country_hint;

  /// No description provided for @city.
  ///
  /// In en, this message translates to:
  /// **'City'**
  String get city;

  /// No description provided for @city_hint.
  ///
  /// In en, this message translates to:
  /// **'Enter a city'**
  String get city_hint;

  /// No description provided for @select_city.
  ///
  /// In en, this message translates to:
  /// **'Select a city'**
  String get select_city;

  /// No description provided for @failed_to_load_trips.
  ///
  /// In en, this message translates to:
  /// **'Failed to load trips'**
  String get failed_to_load_trips;

  /// No description provided for @past_trips.
  ///
  /// In en, this message translates to:
  /// **'Past Trips'**
  String get past_trips;

  /// No description provided for @trip_name.
  ///
  /// In en, this message translates to:
  /// **'Trip Name'**
  String get trip_name;

  /// No description provided for @trip_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Japan Trip 2025'**
  String get trip_name_hint;

  /// No description provided for @amount.
  ///
  /// In en, this message translates to:
  /// **'Amount'**
  String get amount;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @departure_date.
  ///
  /// In en, this message translates to:
  /// **'Departure Date'**
  String get departure_date;

  /// No description provided for @edit_itinerary.
  ///
  /// In en, this message translates to:
  /// **'Edit Itinerary'**
  String get edit_itinerary;

  /// No description provided for @add_itinerary.
  ///
  /// In en, this message translates to:
  /// **'Add Itinerary'**
  String get add_itinerary;

  /// No description provided for @itinerary_details.
  ///
  /// In en, this message translates to:
  /// **'Itinerary Details'**
  String get itinerary_details;

  /// No description provided for @location.
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// No description provided for @location_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. 123 Main St, City, Country'**
  String get location_hint;

  /// No description provided for @pick_location.
  ///
  /// In en, this message translates to:
  /// **'Pick a location'**
  String get pick_location;

  /// No description provided for @update_location.
  ///
  /// In en, this message translates to:
  /// **'Update location'**
  String get update_location;

  /// No description provided for @itinerary_name.
  ///
  /// In en, this message translates to:
  /// **'What is this visit about?'**
  String get itinerary_name;

  /// No description provided for @itinerary_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Day 1: Arrival and City Tour'**
  String get itinerary_name_hint;

  /// No description provided for @confirmDeletion.
  ///
  /// In en, this message translates to:
  /// **'Confirm Deletion'**
  String get confirmDeletion;

  /// No description provided for @confirmDeletionMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete this item?'**
  String get confirmDeletionMessage;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @restaurant.
  ///
  /// In en, this message translates to:
  /// **'Restaurant'**
  String get restaurant;

  /// No description provided for @accommodation.
  ///
  /// In en, this message translates to:
  /// **'Accommodation'**
  String get accommodation;

  /// No description provided for @transportation.
  ///
  /// In en, this message translates to:
  /// **'Transportation'**
  String get transportation;

  /// No description provided for @sightseeing.
  ///
  /// In en, this message translates to:
  /// **'Sightseeing'**
  String get sightseeing;

  /// No description provided for @shopping.
  ///
  /// In en, this message translates to:
  /// **'Shopping'**
  String get shopping;

  /// No description provided for @activity.
  ///
  /// In en, this message translates to:
  /// **'Activity'**
  String get activity;

  /// No description provided for @parking.
  ///
  /// In en, this message translates to:
  /// **'Parking'**
  String get parking;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note'**
  String get note;

  /// No description provided for @movie.
  ///
  /// In en, this message translates to:
  /// **'Movie'**
  String get movie;

  /// No description provided for @flight.
  ///
  /// In en, this message translates to:
  /// **'Flight'**
  String get flight;

  /// No description provided for @carRental.
  ///
  /// In en, this message translates to:
  /// **'Car Rental'**
  String get carRental;

  /// No description provided for @flight_number.
  ///
  /// In en, this message translates to:
  /// **'Flight Number'**
  String get flight_number;

  /// No description provided for @flight_number_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. AA123'**
  String get flight_number_hint;

  /// No description provided for @airline_name.
  ///
  /// In en, this message translates to:
  /// **'Airline Name'**
  String get airline_name;

  /// No description provided for @airline_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. American Airlines'**
  String get airline_name_hint;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @trips.
  ///
  /// In en, this message translates to:
  /// **'Trips'**
  String get trips;

  /// No description provided for @saved.
  ///
  /// In en, this message translates to:
  /// **'Bookmarks'**
  String get saved;

  /// No description provided for @accommodations.
  ///
  /// In en, this message translates to:
  /// **'Accommodations'**
  String get accommodations;

  /// No description provided for @pro.
  ///
  /// In en, this message translates to:
  /// **'Coming soon'**
  String get pro;

  /// No description provided for @account.
  ///
  /// In en, this message translates to:
  /// **'Account'**
  String get account;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @favorites.
  ///
  /// In en, this message translates to:
  /// **'Bookmarks'**
  String get favorites;

  /// No description provided for @unlock_premium.
  ///
  /// In en, this message translates to:
  /// **'RoamR Premium'**
  String get unlock_premium;

  /// No description provided for @premium_subtitle.
  ///
  /// In en, this message translates to:
  /// **'Take your travel planning to the next level'**
  String get premium_subtitle;

  /// No description provided for @coming_soon.
  ///
  /// In en, this message translates to:
  /// **'Coming Soon'**
  String get coming_soon;

  /// No description provided for @ai_trip_planner.
  ///
  /// In en, this message translates to:
  /// **'AI Trip Planner'**
  String get ai_trip_planner;

  /// No description provided for @ai_trip_planner_desc.
  ///
  /// In en, this message translates to:
  /// **'Get personalized trip suggestions and itineraries powered by AI'**
  String get ai_trip_planner_desc;

  /// No description provided for @trip_gallery.
  ///
  /// In en, this message translates to:
  /// **'Trip Gallery'**
  String get trip_gallery;

  /// No description provided for @trip_gallery_desc.
  ///
  /// In en, this message translates to:
  /// **'Attach and organize photos, documents, and memories for each trip'**
  String get trip_gallery_desc;

  /// No description provided for @receipt_scanner.
  ///
  /// In en, this message translates to:
  /// **'Receipt Scanner'**
  String get receipt_scanner;

  /// No description provided for @receipt_scanner_desc.
  ///
  /// In en, this message translates to:
  /// **'Automatically track expenses by scanning your bills and receipts'**
  String get receipt_scanner_desc;

  /// No description provided for @interactive_maps.
  ///
  /// In en, this message translates to:
  /// **'Interactive Maps'**
  String get interactive_maps;

  /// No description provided for @interactive_maps_desc.
  ///
  /// In en, this message translates to:
  /// **'Visualize your entire trip route with interactive maps and navigation'**
  String get interactive_maps_desc;

  /// No description provided for @trip_sharing.
  ///
  /// In en, this message translates to:
  /// **'Trip Sharing'**
  String get trip_sharing;

  /// No description provided for @trip_sharing_desc.
  ///
  /// In en, this message translates to:
  /// **'Collaborate and plan trips with friends and family'**
  String get trip_sharing_desc;

  /// No description provided for @guest_user.
  ///
  /// In en, this message translates to:
  /// **'Sign in to your account'**
  String get guest_user;

  /// No description provided for @account_settings.
  ///
  /// In en, this message translates to:
  /// **'Account Settings'**
  String get account_settings;

  /// No description provided for @edit_profile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get edit_profile;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @profile_updated_successfully.
  ///
  /// In en, this message translates to:
  /// **'Profile updated successfully'**
  String get profile_updated_successfully;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @sign_out_confirmation.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to sign out?'**
  String get sign_out_confirmation;

  /// No description provided for @please_enter_your_name.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get please_enter_your_name;

  /// No description provided for @failed_to_load_profile.
  ///
  /// In en, this message translates to:
  /// **'Failed to load profile data'**
  String get failed_to_load_profile;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @notifications_desc.
  ///
  /// In en, this message translates to:
  /// **'Manage your notification preferences'**
  String get notifications_desc;

  /// No description provided for @preferences.
  ///
  /// In en, this message translates to:
  /// **'Preferences'**
  String get preferences;

  /// No description provided for @theme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// No description provided for @light.
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// No description provided for @currency.
  ///
  /// In en, this message translates to:
  /// **'Currency'**
  String get currency;

  /// No description provided for @currency_desc.
  ///
  /// In en, this message translates to:
  /// **'Set your preferred currency'**
  String get currency_desc;

  /// No description provided for @about.
  ///
  /// In en, this message translates to:
  /// **'About'**
  String get about;

  /// No description provided for @about_app.
  ///
  /// In en, this message translates to:
  /// **'About RoamR'**
  String get about_app;

  /// No description provided for @rate_app.
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rate_app;

  /// No description provided for @privacy_policy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacy_policy;

  /// No description provided for @sign_in.
  ///
  /// In en, this message translates to:
  /// **'Sign In'**
  String get sign_in;

  /// No description provided for @sign_out.
  ///
  /// In en, this message translates to:
  /// **'Sign Out'**
  String get sign_out;

  /// No description provided for @register.
  ///
  /// In en, this message translates to:
  /// **'Register'**
  String get register;

  /// No description provided for @create_account.
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get create_account;

  /// No description provided for @continue_with_google.
  ///
  /// In en, this message translates to:
  /// **'Continue with Google'**
  String get continue_with_google;

  /// No description provided for @continue_with_apple.
  ///
  /// In en, this message translates to:
  /// **'Continue with Apple'**
  String get continue_with_apple;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'OR'**
  String get or;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @confirm_password.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirm_password;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @dont_have_account.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dont_have_account;

  /// No description provided for @already_have_account.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get already_have_account;

  /// No description provided for @signed_out_successfully.
  ///
  /// In en, this message translates to:
  /// **'Signed out successfully'**
  String get signed_out_successfully;

  /// No description provided for @date_range_example.
  ///
  /// In en, this message translates to:
  /// **'e.g. 25 Jan - 16 Feb'**
  String get date_range_example;

  /// No description provided for @add_place.
  ///
  /// In en, this message translates to:
  /// **'Add places you want to visit'**
  String get add_place;

  /// No description provided for @restaurant_name.
  ///
  /// In en, this message translates to:
  /// **'Restaurant Name'**
  String get restaurant_name;

  /// No description provided for @restaurant_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Sushi Palace'**
  String get restaurant_name_hint;

  /// No description provided for @accommodation_name.
  ///
  /// In en, this message translates to:
  /// **'Accommodation Name'**
  String get accommodation_name;

  /// No description provided for @accommodation_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Hilton Hotel'**
  String get accommodation_name_hint;

  /// No description provided for @transportation_name.
  ///
  /// In en, this message translates to:
  /// **'Transportation Name'**
  String get transportation_name;

  /// No description provided for @transportation_name_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Train to Tokyo'**
  String get transportation_name_hint;

  /// No description provided for @sightseeing_name.
  ///
  /// In en, this message translates to:
  /// **'Place Name'**
  String get sightseeing_name;

  /// No description provided for @sightseeing_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Tokyo Tower'**
  String get sightseeing_hint;

  /// No description provided for @shopping_name.
  ///
  /// In en, this message translates to:
  /// **'Store Name'**
  String get shopping_name;

  /// No description provided for @shopping_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Shibuya Mall'**
  String get shopping_hint;

  /// No description provided for @activity_name.
  ///
  /// In en, this message translates to:
  /// **'Activity Name'**
  String get activity_name;

  /// No description provided for @activity_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Cooking Class'**
  String get activity_hint;

  /// No description provided for @parking_name.
  ///
  /// In en, this message translates to:
  /// **'Parking Location'**
  String get parking_name;

  /// No description provided for @parking_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Central Parking'**
  String get parking_hint;

  /// No description provided for @note_name.
  ///
  /// In en, this message translates to:
  /// **'Note Title'**
  String get note_name;

  /// No description provided for @note_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Important Reminders'**
  String get note_hint;

  /// No description provided for @movie_name.
  ///
  /// In en, this message translates to:
  /// **'Movie/Show Name'**
  String get movie_name;

  /// No description provided for @movie_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Local Theater Show'**
  String get movie_hint;

  /// No description provided for @flight_title.
  ///
  /// In en, this message translates to:
  /// **'Flight Details'**
  String get flight_title;

  /// No description provided for @flight_title_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Tokyo to Osaka Flight'**
  String get flight_title_hint;

  /// No description provided for @car_rental_name.
  ///
  /// In en, this message translates to:
  /// **'Car Rental Details'**
  String get car_rental_name;

  /// No description provided for @car_rental_hint.
  ///
  /// In en, this message translates to:
  /// **'e.g. Toyota Camry - Hertz'**
  String get car_rental_hint;

  /// No description provided for @amount_optional.
  ///
  /// In en, this message translates to:
  /// **'Amount (if applicable)'**
  String get amount_optional;

  /// No description provided for @checkin_date.
  ///
  /// In en, this message translates to:
  /// **'Check-in Date'**
  String get checkin_date;

  /// No description provided for @checkout_date.
  ///
  /// In en, this message translates to:
  /// **'Checkout Date'**
  String get checkout_date;

  /// No description provided for @stay_dates.
  ///
  /// In en, this message translates to:
  /// **'Stay Dates'**
  String get stay_dates;

  /// No description provided for @select_checkin_checkout.
  ///
  /// In en, this message translates to:
  /// **'Select check-in and check-out dates'**
  String get select_checkin_checkout;

  /// No description provided for @start_date.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get start_date;

  /// No description provided for @end_date.
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get end_date;

  /// No description provided for @description.
  ///
  /// In en, this message translates to:
  /// **'Description'**
  String get description;

  /// No description provided for @description_hint.
  ///
  /// In en, this message translates to:
  /// **'Add any additional notes or details'**
  String get description_hint;

  /// No description provided for @about_desc.
  ///
  /// In en, this message translates to:
  /// **'Learn more about the app'**
  String get about_desc;

  /// No description provided for @system.
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get system;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @dark.
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get dark;

  /// No description provided for @date_required.
  ///
  /// In en, this message translates to:
  /// **'Date is required'**
  String get date_required;

  /// No description provided for @date_within_trip.
  ///
  /// In en, this message translates to:
  /// **'Date must be within trip dates'**
  String get date_within_trip;

  /// No description provided for @no_favorites.
  ///
  /// In en, this message translates to:
  /// **'No bookmarks yet'**
  String get no_favorites;

  /// No description provided for @add_favorites_hint.
  ///
  /// In en, this message translates to:
  /// **'Your bookmarked itineraries will appear here'**
  String get add_favorites_hint;

  /// No description provided for @premium_title.
  ///
  /// In en, this message translates to:
  /// **'RoamR Premium'**
  String get premium_title;

  /// No description provided for @premium.
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get premium;

  /// No description provided for @premium_features.
  ///
  /// In en, this message translates to:
  /// **'Unlock Premium Features'**
  String get premium_features;

  /// No description provided for @unlimited_trip_planning.
  ///
  /// In en, this message translates to:
  /// **'Unlimited Trip Planning'**
  String get unlimited_trip_planning;

  /// No description provided for @unlimited_trip_planning_desc.
  ///
  /// In en, this message translates to:
  /// **'Create and manage unlimited trips'**
  String get unlimited_trip_planning_desc;

  /// No description provided for @interactive_map.
  ///
  /// In en, this message translates to:
  /// **'Interactive Trip Maps'**
  String get interactive_map;

  /// No description provided for @interactive_map_desc.
  ///
  /// In en, this message translates to:
  /// **'View your entire itinerary on interactive maps with navigation'**
  String get interactive_map_desc;

  /// No description provided for @best_value.
  ///
  /// In en, this message translates to:
  /// **'BEST VALUE'**
  String get best_value;

  /// No description provided for @yearly.
  ///
  /// In en, this message translates to:
  /// **'Yearly'**
  String get yearly;

  /// No description provided for @monthly.
  ///
  /// In en, this message translates to:
  /// **'Monthly'**
  String get monthly;

  /// No description provided for @per_year.
  ///
  /// In en, this message translates to:
  /// **'per year'**
  String get per_year;

  /// No description provided for @per_month.
  ///
  /// In en, this message translates to:
  /// **'per month'**
  String get per_month;

  /// No description provided for @save_fifty.
  ///
  /// In en, this message translates to:
  /// **'Save 50%'**
  String get save_fifty;

  /// No description provided for @restore_purchases.
  ///
  /// In en, this message translates to:
  /// **'Restore Purchases'**
  String get restore_purchases;

  /// No description provided for @subscription_details.
  ///
  /// In en, this message translates to:
  /// **'Subscription Details'**
  String get subscription_details;

  /// No description provided for @subscription_status.
  ///
  /// In en, this message translates to:
  /// **'Status:'**
  String get subscription_status;

  /// No description provided for @subscription_plan.
  ///
  /// In en, this message translates to:
  /// **'Plan:'**
  String get subscription_plan;

  /// No description provided for @subscription_purchased.
  ///
  /// In en, this message translates to:
  /// **'Purchased:'**
  String get subscription_purchased;

  /// No description provided for @subscription_expires.
  ///
  /// In en, this message translates to:
  /// **'Expires:'**
  String get subscription_expires;

  /// No description provided for @subscription_renews.
  ///
  /// In en, this message translates to:
  /// **'Renews:'**
  String get subscription_renews;

  /// No description provided for @subscription_error.
  ///
  /// In en, this message translates to:
  /// **'Could not load subscription details'**
  String get subscription_error;

  /// No description provided for @loading_premium_options.
  ///
  /// In en, this message translates to:
  /// **'Loading premium options...'**
  String get loading_premium_options;

  /// No description provided for @premium_features_unlocked.
  ///
  /// In en, this message translates to:
  /// **'Premium features unlocked!'**
  String get premium_features_unlocked;

  /// No description provided for @premium_user_access.
  ///
  /// In en, this message translates to:
  /// **'You are a premium user with access to all features'**
  String get premium_user_access;

  /// No description provided for @no_locations.
  ///
  /// In en, this message translates to:
  /// **'No itineraries with location data'**
  String get no_locations;

  /// No description provided for @map_not_available.
  ///
  /// In en, this message translates to:
  /// **'Map Not Available'**
  String get map_not_available;

  /// No description provided for @add_location_details.
  ///
  /// In en, this message translates to:
  /// **'There are no locations to display on the map. Add location details to your itineraries first.'**
  String get add_location_details;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @location_permission_denied.
  ///
  /// In en, this message translates to:
  /// **'Location permission denied'**
  String get location_permission_denied;

  /// No description provided for @location_permission_permanently_denied.
  ///
  /// In en, this message translates to:
  /// **'Location permission permanently denied'**
  String get location_permission_permanently_denied;

  /// No description provided for @location_error.
  ///
  /// In en, this message translates to:
  /// **'Error getting location'**
  String get location_error;

  /// No description provided for @delete_account.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get delete_account;

  /// No description provided for @delete_account_confirmation.
  ///
  /// In en, this message translates to:
  /// **'Delete Account?'**
  String get delete_account_confirmation;

  /// No description provided for @delete_account_message.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.'**
  String get delete_account_message;

  /// No description provided for @account_deleted_successfully.
  ///
  /// In en, this message translates to:
  /// **'Account deleted successfully'**
  String get account_deleted_successfully;

  /// No description provided for @premium_login_message.
  ///
  /// In en, this message translates to:
  /// **'Sign in to access premium features'**
  String get premium_login_message;

  /// No description provided for @share_app.
  ///
  /// In en, this message translates to:
  /// **'Share App'**
  String get share_app;

  /// No description provided for @terms_of_service.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get terms_of_service;

  /// No description provided for @send_feedback.
  ///
  /// In en, this message translates to:
  /// **'Send Feedback'**
  String get send_feedback;

  /// No description provided for @statistics.
  ///
  /// In en, this message translates to:
  /// **'Statistics'**
  String get statistics;

  /// No description provided for @select_trip.
  ///
  /// In en, this message translates to:
  /// **'Select Trip'**
  String get select_trip;

  /// No description provided for @select_trip_to_view_statistics.
  ///
  /// In en, this message translates to:
  /// **'Select a trip to view statistics'**
  String get select_trip_to_view_statistics;

  /// No description provided for @no_expenses_to_display.
  ///
  /// In en, this message translates to:
  /// **'No expenses to display'**
  String get no_expenses_to_display;

  /// No description provided for @expense_over_time.
  ///
  /// In en, this message translates to:
  /// **'Expense Over Time'**
  String get expense_over_time;

  /// No description provided for @expenses_by_category.
  ///
  /// In en, this message translates to:
  /// **'Expenses By Category'**
  String get expenses_by_category;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @category_distribution.
  ///
  /// In en, this message translates to:
  /// **'Category Distribution'**
  String get category_distribution;

  /// No description provided for @remove_from_favorites.
  ///
  /// In en, this message translates to:
  /// **'Remove from Bookmarks'**
  String get remove_from_favorites;

  /// No description provided for @remove.
  ///
  /// In en, this message translates to:
  /// **'Remove'**
  String get remove;

  /// No description provided for @remove_from_favorites_confirmation.
  ///
  /// In en, this message translates to:
  /// **'Remove from Bookmarks?'**
  String get remove_from_favorites_confirmation;

  /// No description provided for @remove_from_favorites_message.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to remove this itinerary from your bookmarks?'**
  String get remove_from_favorites_message;

  /// No description provided for @pie_chart.
  ///
  /// In en, this message translates to:
  /// **'Pie Chart'**
  String get pie_chart;

  /// No description provided for @bar_chart.
  ///
  /// In en, this message translates to:
  /// **'Bar Chart'**
  String get bar_chart;

  /// No description provided for @trip_summary.
  ///
  /// In en, this message translates to:
  /// **'Travel Summary'**
  String get trip_summary;

  /// No description provided for @total_trips.
  ///
  /// In en, this message translates to:
  /// **'Total Trips'**
  String get total_trips;

  /// No description provided for @total_places_visited.
  ///
  /// In en, this message translates to:
  /// **'Places Visited'**
  String get total_places_visited;

  /// No description provided for @avg_places_per_trip.
  ///
  /// In en, this message translates to:
  /// **'Avg Places/Trip'**
  String get avg_places_per_trip;

  /// No description provided for @total_days_traveled.
  ///
  /// In en, this message translates to:
  /// **'Days Traveled'**
  String get total_days_traveled;

  /// No description provided for @shortest_trip.
  ///
  /// In en, this message translates to:
  /// **'Shortest Trip'**
  String get shortest_trip;

  /// No description provided for @longest_trip.
  ///
  /// In en, this message translates to:
  /// **'Longest Trip'**
  String get longest_trip;

  /// No description provided for @day.
  ///
  /// In en, this message translates to:
  /// **'day'**
  String get day;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @flights.
  ///
  /// In en, this message translates to:
  /// **'Flights'**
  String get flights;

  /// No description provided for @most_frequent.
  ///
  /// In en, this message translates to:
  /// **'Most Frequent Activity'**
  String get most_frequent;

  /// No description provided for @most_activities_day.
  ///
  /// In en, this message translates to:
  /// **'Most Activities/Day'**
  String get most_activities_day;

  /// No description provided for @avg_activities_day.
  ///
  /// In en, this message translates to:
  /// **'Avg Activities/Day'**
  String get avg_activities_day;

  /// No description provided for @avg_spend_per_trip.
  ///
  /// In en, this message translates to:
  /// **'Avg Spend/Trip'**
  String get avg_spend_per_trip;

  /// No description provided for @most_expensive_trip.
  ///
  /// In en, this message translates to:
  /// **'Most Expensive Trip'**
  String get most_expensive_trip;

  /// No description provided for @least_expensive_trip.
  ///
  /// In en, this message translates to:
  /// **'Least Expensive Trip'**
  String get least_expensive_trip;

  /// No description provided for @top_expense_category.
  ///
  /// In en, this message translates to:
  /// **'Most Expensive Category'**
  String get top_expense_category;

  /// No description provided for @achievements.
  ///
  /// In en, this message translates to:
  /// **'Achievements'**
  String get achievements;

  /// No description provided for @achievements_description.
  ///
  /// In en, this message translates to:
  /// **'Track your travel milestones and unlock achievements as you explore the world!'**
  String get achievements_description;

  /// No description provided for @destination_achievements.
  ///
  /// In en, this message translates to:
  /// **'Destination Achievements'**
  String get destination_achievements;

  /// No description provided for @expense_achievements.
  ///
  /// In en, this message translates to:
  /// **'Expense Achievements'**
  String get expense_achievements;

  /// No description provided for @frequency_achievements.
  ///
  /// In en, this message translates to:
  /// **'Trip Frequency Achievements'**
  String get frequency_achievements;

  /// No description provided for @duration_achievements.
  ///
  /// In en, this message translates to:
  /// **'Trip Duration Achievements'**
  String get duration_achievements;

  /// No description provided for @flight_achievements.
  ///
  /// In en, this message translates to:
  /// **'Flight Achievements'**
  String get flight_achievements;

  /// No description provided for @novice_traveller_title.
  ///
  /// In en, this message translates to:
  /// **'Novice Traveller'**
  String get novice_traveller_title;

  /// No description provided for @novice_traveller_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit 3 or more different countries'**
  String get novice_traveller_desc;

  /// No description provided for @world_traveller_title.
  ///
  /// In en, this message translates to:
  /// **'World Traveller'**
  String get world_traveller_title;

  /// No description provided for @world_traveller_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit 10 or more different countries'**
  String get world_traveller_desc;

  /// No description provided for @globetrotter_title.
  ///
  /// In en, this message translates to:
  /// **'Globetrotter'**
  String get globetrotter_title;

  /// No description provided for @globetrotter_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit 20 or more different countries'**
  String get globetrotter_desc;

  /// No description provided for @continental_explorer_title.
  ///
  /// In en, this message translates to:
  /// **'Continental Explorer'**
  String get continental_explorer_title;

  /// No description provided for @continental_explorer_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit 2 or more continents and at least 20 countries'**
  String get continental_explorer_desc;

  /// No description provided for @continental_collector_title.
  ///
  /// In en, this message translates to:
  /// **'Continental Collector'**
  String get continental_collector_title;

  /// No description provided for @continental_collector_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit 4 or more continents and at least 25 countries'**
  String get continental_collector_desc;

  /// No description provided for @world_conqueror_title.
  ///
  /// In en, this message translates to:
  /// **'World Conqueror'**
  String get world_conqueror_title;

  /// No description provided for @world_conqueror_desc.
  ///
  /// In en, this message translates to:
  /// **'Visit all 7 continents and at least 30 countries'**
  String get world_conqueror_desc;

  /// No description provided for @budget_tracker_title.
  ///
  /// In en, this message translates to:
  /// **'Budget Tracker'**
  String get budget_tracker_title;

  /// No description provided for @budget_tracker_desc.
  ///
  /// In en, this message translates to:
  /// **'Track \$1,000+ in total expenses'**
  String get budget_tracker_desc;

  /// No description provided for @expense_manager_title.
  ///
  /// In en, this message translates to:
  /// **'Expense Manager'**
  String get expense_manager_title;

  /// No description provided for @expense_manager_desc.
  ///
  /// In en, this message translates to:
  /// **'Track \$5,000+ in total expenses'**
  String get expense_manager_desc;

  /// No description provided for @financial_voyager_title.
  ///
  /// In en, this message translates to:
  /// **'Financial Voyager'**
  String get financial_voyager_title;

  /// No description provided for @financial_voyager_desc.
  ///
  /// In en, this message translates to:
  /// **'Track \$10,000+ in total expenses'**
  String get financial_voyager_desc;

  /// No description provided for @luxury_traveller_title.
  ///
  /// In en, this message translates to:
  /// **'Luxury Traveller'**
  String get luxury_traveller_title;

  /// No description provided for @luxury_traveller_desc.
  ///
  /// In en, this message translates to:
  /// **'Track \$20,000+ in total expenses'**
  String get luxury_traveller_desc;

  /// No description provided for @travel_beginner_title.
  ///
  /// In en, this message translates to:
  /// **'Travel Beginner'**
  String get travel_beginner_title;

  /// No description provided for @travel_beginner_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete 3 or more trips'**
  String get travel_beginner_desc;

  /// No description provided for @travel_enthusiast_title.
  ///
  /// In en, this message translates to:
  /// **'Travel Enthusiast'**
  String get travel_enthusiast_title;

  /// No description provided for @travel_enthusiast_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete 10 or more trips'**
  String get travel_enthusiast_desc;

  /// No description provided for @travel_addict_title.
  ///
  /// In en, this message translates to:
  /// **'Travel Addict'**
  String get travel_addict_title;

  /// No description provided for @travel_addict_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete 20 or more trips'**
  String get travel_addict_desc;

  /// No description provided for @day_tripper_title.
  ///
  /// In en, this message translates to:
  /// **'Day Tripper'**
  String get day_tripper_title;

  /// No description provided for @day_tripper_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 1 day'**
  String get day_tripper_desc;

  /// No description provided for @weekend_wanderer_title.
  ///
  /// In en, this message translates to:
  /// **'Weekend Wanderer'**
  String get weekend_wanderer_title;

  /// No description provided for @weekend_wanderer_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 3 days'**
  String get weekend_wanderer_desc;

  /// No description provided for @vacation_voyager_title.
  ///
  /// In en, this message translates to:
  /// **'Vacation Voyager'**
  String get vacation_voyager_title;

  /// No description provided for @vacation_voyager_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 7 days'**
  String get vacation_voyager_desc;

  /// No description provided for @extended_explorer_title.
  ///
  /// In en, this message translates to:
  /// **'Extended Explorer'**
  String get extended_explorer_title;

  /// No description provided for @extended_explorer_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 14 days'**
  String get extended_explorer_desc;

  /// No description provided for @long_term_traveler_title.
  ///
  /// In en, this message translates to:
  /// **'Long-term Traveler'**
  String get long_term_traveler_title;

  /// No description provided for @long_term_traveler_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 30 days'**
  String get long_term_traveler_desc;

  /// No description provided for @nomadic_adventurer_title.
  ///
  /// In en, this message translates to:
  /// **'Nomadic Adventurer'**
  String get nomadic_adventurer_title;

  /// No description provided for @nomadic_adventurer_desc.
  ///
  /// In en, this message translates to:
  /// **'Complete a trip of at least 60 days'**
  String get nomadic_adventurer_desc;

  /// No description provided for @first_flight_title.
  ///
  /// In en, this message translates to:
  /// **'Flight Beginner'**
  String get first_flight_title;

  /// No description provided for @first_flight_desc.
  ///
  /// In en, this message translates to:
  /// **'Record 5 or more flights'**
  String get first_flight_desc;

  /// No description provided for @frequent_flyer_title.
  ///
  /// In en, this message translates to:
  /// **'Frequent Flyer'**
  String get frequent_flyer_title;

  /// No description provided for @frequent_flyer_desc.
  ///
  /// In en, this message translates to:
  /// **'Record 15 or more flights'**
  String get frequent_flyer_desc;

  /// No description provided for @aviation_enthusiast_title.
  ///
  /// In en, this message translates to:
  /// **'Aviation Enthusiast'**
  String get aviation_enthusiast_title;

  /// No description provided for @aviation_enthusiast_desc.
  ///
  /// In en, this message translates to:
  /// **'Record 30 or more flights'**
  String get aviation_enthusiast_desc;

  /// No description provided for @progress.
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;

  /// No description provided for @complete_trips_for_achievements.
  ///
  /// In en, this message translates to:
  /// **'Complete trips to unlock achievements!'**
  String get complete_trips_for_achievements;

  /// No description provided for @view_all.
  ///
  /// In en, this message translates to:
  /// **'View All'**
  String get view_all;

  /// No description provided for @app_tagline.
  ///
  /// In en, this message translates to:
  /// **'Your travel companion'**
  String get app_tagline;

  /// No description provided for @website.
  ///
  /// In en, this message translates to:
  /// **'Website'**
  String get website;

  /// No description provided for @facebook.
  ///
  /// In en, this message translates to:
  /// **'Facebook'**
  String get facebook;

  /// No description provided for @twitter.
  ///
  /// In en, this message translates to:
  /// **'Twitter/X'**
  String get twitter;

  /// No description provided for @instagram.
  ///
  /// In en, this message translates to:
  /// **'Instagram'**
  String get instagram;

  /// No description provided for @feedback_email_subject.
  ///
  /// In en, this message translates to:
  /// **'RoamR Feedback'**
  String get feedback_email_subject;

  /// No description provided for @feedback_email_body.
  ///
  /// In en, this message translates to:
  /// **'I would like to provide feedback about RoamR:'**
  String get feedback_email_body;

  /// No description provided for @share_app_message_prefix.
  ///
  /// In en, this message translates to:
  /// **'Check out RoamR, a travel planning app: '**
  String get share_app_message_prefix;

  /// No description provided for @share_app_subject.
  ///
  /// In en, this message translates to:
  /// **'RoamR - Your travel companion'**
  String get share_app_subject;

  /// No description provided for @could_not_launch_url.
  ///
  /// In en, this message translates to:
  /// **'Could not launch {url}'**
  String could_not_launch_url(Object url);

  /// No description provided for @could_not_share_app.
  ///
  /// In en, this message translates to:
  /// **'Could not share app'**
  String get could_not_share_app;

  /// No description provided for @could_not_launch_email.
  ///
  /// In en, this message translates to:
  /// **'Could not launch email client'**
  String get could_not_launch_email;

  /// No description provided for @copyright.
  ///
  /// In en, this message translates to:
  /// **'© {year} RoamR'**
  String copyright(Object year);

  /// No description provided for @photos.
  ///
  /// In en, this message translates to:
  /// **'Photos'**
  String get photos;

  /// No description provided for @no_photos_added.
  ///
  /// In en, this message translates to:
  /// **'No photos added yet'**
  String get no_photos_added;

  /// No description provided for @gallery.
  ///
  /// In en, this message translates to:
  /// **'Gallery'**
  String get gallery;

  /// No description provided for @camera.
  ///
  /// In en, this message translates to:
  /// **'Camera'**
  String get camera;

  /// No description provided for @error_loading_image.
  ///
  /// In en, this message translates to:
  /// **'Error loading image'**
  String get error_loading_image;

  /// No description provided for @unsupported_attachment_type.
  ///
  /// In en, this message translates to:
  /// **'Unsupported attachment type'**
  String get unsupported_attachment_type;

  /// No description provided for @file_not_found.
  ///
  /// In en, this message translates to:
  /// **'File not found'**
  String get file_not_found;

  /// No description provided for @see_more.
  ///
  /// In en, this message translates to:
  /// **'See More'**
  String get see_more;

  /// No description provided for @select_category.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get select_category;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @search_activities_and_places.
  ///
  /// In en, this message translates to:
  /// **'Search activities and places'**
  String get search_activities_and_places;

  /// No description provided for @no_results_found.
  ///
  /// In en, this message translates to:
  /// **'No results found'**
  String get no_results_found;

  /// No description provided for @travel.
  ///
  /// In en, this message translates to:
  /// **'Travel'**
  String get travel;

  /// No description provided for @food_drink.
  ///
  /// In en, this message translates to:
  /// **'Food & Drink'**
  String get food_drink;

  /// No description provided for @art_fun.
  ///
  /// In en, this message translates to:
  /// **'Art & Fun'**
  String get art_fun;

  /// No description provided for @search_results.
  ///
  /// In en, this message translates to:
  /// **'Search Results'**
  String get search_results;

  /// No description provided for @trip_photos.
  ///
  /// In en, this message translates to:
  /// **'Trip Photos'**
  String get trip_photos;

  /// No description provided for @error_loading_photos.
  ///
  /// In en, this message translates to:
  /// **'Error loading photos'**
  String get error_loading_photos;

  /// No description provided for @no_photos_for_trip.
  ///
  /// In en, this message translates to:
  /// **'No photos for this trip'**
  String get no_photos_for_trip;

  /// No description provided for @no_audio_for_trip.
  ///
  /// In en, this message translates to:
  /// **'No audio recordings for this trip'**
  String get no_audio_for_trip;

  /// No description provided for @no_documents_for_trip.
  ///
  /// In en, this message translates to:
  /// **'No documents for this trip'**
  String get no_documents_for_trip;

  /// No description provided for @no_attachments_for_trip.
  ///
  /// In en, this message translates to:
  /// **'No attachments for this trip'**
  String get no_attachments_for_trip;

  /// No description provided for @audio.
  ///
  /// In en, this message translates to:
  /// **'Audio'**
  String get audio;

  /// No description provided for @documents.
  ///
  /// In en, this message translates to:
  /// **'Documents'**
  String get documents;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @opening_file.
  ///
  /// In en, this message translates to:
  /// **'Opening file'**
  String get opening_file;

  /// No description provided for @select_document.
  ///
  /// In en, this message translates to:
  /// **'Select Document'**
  String get select_document;

  /// No description provided for @error_opening_file.
  ///
  /// In en, this message translates to:
  /// **'Error opening file'**
  String get error_opening_file;

  /// No description provided for @no_content_available.
  ///
  /// In en, this message translates to:
  /// **'No content available to display'**
  String get no_content_available;

  /// No description provided for @error_loading_document.
  ///
  /// In en, this message translates to:
  /// **'Error loading document'**
  String get error_loading_document;

  /// No description provided for @size.
  ///
  /// In en, this message translates to:
  /// **'Size'**
  String get size;

  /// No description provided for @modified.
  ///
  /// In en, this message translates to:
  /// **'Modified'**
  String get modified;

  /// No description provided for @word_document.
  ///
  /// In en, this message translates to:
  /// **'Word document content cannot be displayed directly'**
  String get word_document;

  /// No description provided for @excel_spreadsheet.
  ///
  /// In en, this message translates to:
  /// **'Excel spreadsheet content cannot be displayed directly'**
  String get excel_spreadsheet;

  /// No description provided for @file_type_not_supported.
  ///
  /// In en, this message translates to:
  /// **'This file type cannot be displayed directly'**
  String get file_type_not_supported;

  /// No description provided for @file_available_at.
  ///
  /// In en, this message translates to:
  /// **'The file is available at'**
  String get file_available_at;

  /// No description provided for @attachments.
  ///
  /// In en, this message translates to:
  /// **'Attachments'**
  String get attachments;

  /// No description provided for @add_attachment.
  ///
  /// In en, this message translates to:
  /// **'Add Attachment'**
  String get add_attachment;

  /// No description provided for @choose_attachment_type.
  ///
  /// In en, this message translates to:
  /// **'Choose Attachment Type'**
  String get choose_attachment_type;

  /// No description provided for @voucher_code.
  ///
  /// In en, this message translates to:
  /// **'Voucher Code'**
  String get voucher_code;

  /// No description provided for @enter_voucher_code.
  ///
  /// In en, this message translates to:
  /// **'Enter Voucher Code'**
  String get enter_voucher_code;

  /// No description provided for @enter_voucher_code_hint.
  ///
  /// In en, this message translates to:
  /// **'Enter your voucher code'**
  String get enter_voucher_code_hint;

  /// No description provided for @please_enter_voucher_code.
  ///
  /// In en, this message translates to:
  /// **'Please enter a voucher code'**
  String get please_enter_voucher_code;

  /// No description provided for @voucher_code_applied.
  ///
  /// In en, this message translates to:
  /// **'Voucher code applied successfully!'**
  String get voucher_code_applied;

  /// No description provided for @invalid_voucher_code.
  ///
  /// In en, this message translates to:
  /// **'Invalid voucher code. Please try again.'**
  String get invalid_voucher_code;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @night.
  ///
  /// In en, this message translates to:
  /// **'night'**
  String get night;

  /// No description provided for @nights.
  ///
  /// In en, this message translates to:
  /// **'nights'**
  String get nights;

  /// No description provided for @verify_email.
  ///
  /// In en, this message translates to:
  /// **'Verify Email'**
  String get verify_email;

  /// No description provided for @verification_code_sent.
  ///
  /// In en, this message translates to:
  /// **'A verification code has been sent to'**
  String get verification_code_sent;

  /// No description provided for @verification_code.
  ///
  /// In en, this message translates to:
  /// **'Verification Code'**
  String get verification_code;

  /// No description provided for @please_enter_verification_code.
  ///
  /// In en, this message translates to:
  /// **'Please enter the verification code'**
  String get please_enter_verification_code;

  /// No description provided for @verification_code_too_short.
  ///
  /// In en, this message translates to:
  /// **'Verification code is too short'**
  String get verification_code_too_short;

  /// No description provided for @verify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verify;

  /// No description provided for @resend_code.
  ///
  /// In en, this message translates to:
  /// **'Resend Code'**
  String get resend_code;

  /// No description provided for @please_wait_before_resend.
  ///
  /// In en, this message translates to:
  /// **'Please wait {seconds} seconds before requesting a new code'**
  String please_wait_before_resend(Object seconds);

  /// No description provided for @please_enter_name.
  ///
  /// In en, this message translates to:
  /// **'Please enter your name'**
  String get please_enter_name;

  /// No description provided for @please_enter_email.
  ///
  /// In en, this message translates to:
  /// **'Please enter your email'**
  String get please_enter_email;

  /// No description provided for @please_enter_valid_email.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid email address'**
  String get please_enter_valid_email;

  /// No description provided for @verification_code_login.
  ///
  /// In en, this message translates to:
  /// **'We will send a verification code to your email'**
  String get verification_code_login;

  /// No description provided for @verification_code_register.
  ///
  /// In en, this message translates to:
  /// **'We will send a verification code to your email to complete registration'**
  String get verification_code_register;

  /// No description provided for @continue_with_email.
  ///
  /// In en, this message translates to:
  /// **'Continue with Email'**
  String get continue_with_email;

  /// No description provided for @register_with_email.
  ///
  /// In en, this message translates to:
  /// **'Register with Email'**
  String get register_with_email;

  /// No description provided for @error_invalid_email.
  ///
  /// In en, this message translates to:
  /// **'The email address is badly formatted.'**
  String get error_invalid_email;

  /// No description provided for @error_user_disabled.
  ///
  /// In en, this message translates to:
  /// **'This user account has been disabled.'**
  String get error_user_disabled;

  /// No description provided for @error_user_not_found.
  ///
  /// In en, this message translates to:
  /// **'This account does not exist or has been deleted.'**
  String get error_user_not_found;

  /// No description provided for @error_invalid_credentials.
  ///
  /// In en, this message translates to:
  /// **'Wrong email or password provided.'**
  String get error_invalid_credentials;

  /// No description provided for @error_email_exists.
  ///
  /// In en, this message translates to:
  /// **'The email address is already in use by another account.'**
  String get error_email_exists;

  /// No description provided for @error_weak_password.
  ///
  /// In en, this message translates to:
  /// **'The password is too weak.'**
  String get error_weak_password;

  /// No description provided for @error_email_not_registered.
  ///
  /// In en, this message translates to:
  /// **'This email is not registered. Please sign up first.'**
  String get error_email_not_registered;

  /// No description provided for @error_otp_disabled.
  ///
  /// In en, this message translates to:
  /// **'OTP verification is disabled. Please try another method.'**
  String get error_otp_disabled;

  /// No description provided for @error_unknown.
  ///
  /// In en, this message translates to:
  /// **'An unknown error occurred. Please try again later.'**
  String get error_unknown;

  /// No description provided for @account_information.
  ///
  /// In en, this message translates to:
  /// **'Account Information'**
  String get account_information;

  /// No description provided for @account_actions.
  ///
  /// In en, this message translates to:
  /// **'Account Actions'**
  String get account_actions;

  /// No description provided for @enter_your_name.
  ///
  /// In en, this message translates to:
  /// **'Enter your name'**
  String get enter_your_name;

  /// No description provided for @onboarding_welcome_title.
  ///
  /// In en, this message translates to:
  /// **'Welcome to RoamR'**
  String get onboarding_welcome_title;

  /// No description provided for @onboarding_welcome_subtitle.
  ///
  /// In en, this message translates to:
  /// **'Your personal travel companion'**
  String get onboarding_welcome_subtitle;

  /// No description provided for @onboarding_welcome_button.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get onboarding_welcome_button;

  /// No description provided for @onboarding_select_language.
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get onboarding_select_language;

  /// No description provided for @onboarding_features_title.
  ///
  /// In en, this message translates to:
  /// **'Features'**
  String get onboarding_features_title;

  /// No description provided for @onboarding_feature_maps.
  ///
  /// In en, this message translates to:
  /// **'Interactive Maps'**
  String get onboarding_feature_maps;

  /// No description provided for @onboarding_feature_calendar.
  ///
  /// In en, this message translates to:
  /// **'Trip Calendar'**
  String get onboarding_feature_calendar;

  /// No description provided for @onboarding_feature_social.
  ///
  /// In en, this message translates to:
  /// **'Social Sharing'**
  String get onboarding_feature_social;

  /// No description provided for @onboarding_feature_personalize.
  ///
  /// In en, this message translates to:
  /// **'Personalized Experience'**
  String get onboarding_feature_personalize;

  /// No description provided for @onboarding_features_button.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get onboarding_features_button;

  /// No description provided for @onboarding_theme_title.
  ///
  /// In en, this message translates to:
  /// **'Choose Theme'**
  String get onboarding_theme_title;

  /// No description provided for @onboarding_theme_subtitle.
  ///
  /// In en, this message translates to:
  /// **'Select your preferred theme'**
  String get onboarding_theme_subtitle;

  /// No description provided for @onboarding_theme_button.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get onboarding_theme_button;

  /// No description provided for @features_title.
  ///
  /// In en, this message translates to:
  /// **'Plan Your Perfect Trip'**
  String get features_title;

  /// No description provided for @features_subtitle.
  ///
  /// In en, this message translates to:
  /// **'Everything you need to organize your travel'**
  String get features_subtitle;

  /// No description provided for @location_search.
  ///
  /// In en, this message translates to:
  /// **'Smart Location Search'**
  String get location_search;

  /// No description provided for @location_search_desc.
  ///
  /// In en, this message translates to:
  /// **'Find and add places to your itinerary with ease'**
  String get location_search_desc;

  /// No description provided for @itinerary_map.
  ///
  /// In en, this message translates to:
  /// **'Interactive Itinerary Map'**
  String get itinerary_map;

  /// No description provided for @itinerary_map_desc.
  ///
  /// In en, this message translates to:
  /// **'Visualize your entire trip route on an interactive map'**
  String get itinerary_map_desc;

  /// No description provided for @expense_tracking.
  ///
  /// In en, this message translates to:
  /// **'Expense Tracking'**
  String get expense_tracking;

  /// No description provided for @expense_tracking_desc.
  ///
  /// In en, this message translates to:
  /// **'Track and categorize all your travel expenses'**
  String get expense_tracking_desc;

  /// No description provided for @travel_analytics.
  ///
  /// In en, this message translates to:
  /// **'Travel Analytics'**
  String get travel_analytics;

  /// No description provided for @travel_analytics_desc.
  ///
  /// In en, this message translates to:
  /// **'View insights and charts of your travel patterns'**
  String get travel_analytics_desc;

  /// No description provided for @trip_attachments.
  ///
  /// In en, this message translates to:
  /// **'Trip Attachments'**
  String get trip_attachments;

  /// No description provided for @trip_attachments_desc.
  ///
  /// In en, this message translates to:
  /// **'Attach photos, documents, and memories to your trips'**
  String get trip_attachments_desc;

  /// No description provided for @comprehensive_planning.
  ///
  /// In en, this message translates to:
  /// **'Comprehensive Planning'**
  String get comprehensive_planning;

  /// No description provided for @comprehensive_planning_desc.
  ///
  /// In en, this message translates to:
  /// **'Plan flights, hotels, activities, and more with detailed options'**
  String get comprehensive_planning_desc;

  /// No description provided for @no_attachments_added_yet.
  ///
  /// In en, this message translates to:
  /// **'No attachments added yet'**
  String get no_attachments_added_yet;

  /// No description provided for @usage_left.
  ///
  /// In en, this message translates to:
  /// **'left'**
  String get usage_left;

  /// No description provided for @usage_details_title.
  ///
  /// In en, this message translates to:
  /// **'Usage Details'**
  String get usage_details_title;

  /// No description provided for @unlimited.
  ///
  /// In en, this message translates to:
  /// **'Unlimited'**
  String get unlimited;

  /// No description provided for @used.
  ///
  /// In en, this message translates to:
  /// **'Used'**
  String get used;

  /// No description provided for @left.
  ///
  /// In en, this message translates to:
  /// **'left'**
  String get left;

  /// No description provided for @premium_required_message.
  ///
  /// In en, this message translates to:
  /// **'You have reached the free limit. Upgrade to premium to create more trips.'**
  String get premium_required_message;

  /// No description provided for @upgrade.
  ///
  /// In en, this message translates to:
  /// **'Upgrade'**
  String get upgrade;

  /// No description provided for @itineraries.
  ///
  /// In en, this message translates to:
  /// **'Itineraries'**
  String get itineraries;

  /// No description provided for @notification_title_no_trips.
  ///
  /// In en, this message translates to:
  /// **'Start your adventure!'**
  String get notification_title_no_trips;

  /// No description provided for @notification_body_no_trips.
  ///
  /// In en, this message translates to:
  /// **'Create your first trip and start planning your journey.'**
  String get notification_body_no_trips;

  /// No description provided for @notification_title_no_itinerary.
  ///
  /// In en, this message translates to:
  /// **'Plan your itinerary!'**
  String get notification_title_no_itinerary;

  /// No description provided for @notification_body_no_itinerary.
  ///
  /// In en, this message translates to:
  /// **'Add an itinerary to your trip \"{tripName}\".'**
  String notification_body_no_itinerary(Object tripName);

  /// No description provided for @notification_title_no_places.
  ///
  /// In en, this message translates to:
  /// **'Add places to visit!'**
  String get notification_title_no_places;

  /// No description provided for @notification_body_no_places.
  ///
  /// In en, this message translates to:
  /// **'Enhance your trip \"{tripName}\" by adding places to visit.'**
  String notification_body_no_places(Object tripName);

  /// No description provided for @notification_title_keep_planning.
  ///
  /// In en, this message translates to:
  /// **'Keep planning your trip!'**
  String get notification_title_keep_planning;

  /// No description provided for @notification_body_keep_planning.
  ///
  /// In en, this message translates to:
  /// **'Continue building your perfect itinerary for \"{tripName}\".'**
  String notification_body_keep_planning(Object tripName);

  /// No description provided for @ai_chat_title.
  ///
  /// In en, this message translates to:
  /// **'AI Chat'**
  String get ai_chat_title;

  /// No description provided for @ai_chat_input_hint.
  ///
  /// In en, this message translates to:
  /// **'Ask me about your travel plans...'**
  String get ai_chat_input_hint;

  /// No description provided for @ai_chat_loading.
  ///
  /// In en, this message translates to:
  /// **'RoamR AI is thinking...'**
  String get ai_chat_loading;

  /// No description provided for @ai_chat_welcome.
  ///
  /// In en, this message translates to:
  /// **'Hi! I\'m RoamR AI, your travel assistant. How can I help you plan your next adventure?'**
  String get ai_chat_welcome;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['de', 'en', 'es', 'fr', 'hi', 'th'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'de': return AppLocalizationsDe();
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
    case 'fr': return AppLocalizationsFr();
    case 'hi': return AppLocalizationsHi();
    case 'th': return AppLocalizationsTh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
