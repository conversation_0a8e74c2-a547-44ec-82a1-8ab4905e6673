// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get language => 'Langue';

  @override
  String get language_desc => 'Changez votre langue préférée';

  @override
  String get all => 'Tous';

  @override
  String get grocery => 'Épicerie';

  @override
  String get food => 'Nourriture';

  @override
  String get work => 'Travail';

  @override
  String get entertainment => 'Divertissement';

  @override
  String get traveling => 'Voyage';

  @override
  String get other => 'Autre';

  @override
  String get total_expenses => 'Dépenses totales';

  @override
  String get select_catagory => 'Sélectionner une catégorie';

  @override
  String get category => 'Catégorie';

  @override
  String get your_trips => 'Vos voyages';

  @override
  String get no_trips => 'Aucun voyage ajouté pour le moment.';

  @override
  String get add_trip => 'Ajouter un voyage';

  @override
  String get edit_trip => 'Modifier le voyage';

  @override
  String get title => 'Titre';

  @override
  String get enter_title => 'Entrez le nom du voyage';

  @override
  String get date_range => 'Quand partez-vous ?';

  @override
  String get select_date_range => 'Veuillez sélectionner une période';

  @override
  String get country => 'Pays';

  @override
  String get select_country => 'Sélectionnez un pays';

  @override
  String get country_hint => 'Où allez-vous ?';

  @override
  String get city => 'Ville';

  @override
  String get city_hint => 'Entrez une ville';

  @override
  String get select_city => 'Sélectionnez une ville';

  @override
  String get failed_to_load_trips => 'Échec du chargement des voyages';

  @override
  String get past_trips => 'Voyages passés';

  @override
  String get trip_name => 'Nom du voyage';

  @override
  String get trip_name_hint => 'ex. Vacances d\'été 2023';

  @override
  String get amount => 'Montant';

  @override
  String get date => 'Date';

  @override
  String get departure_date => 'Date de départ';

  @override
  String get edit_itinerary => 'Modifier l\'itinéraire';

  @override
  String get add_itinerary => 'Ajouter un itinéraire';

  @override
  String get itinerary_details => 'Détails de l\'itinéraire';

  @override
  String get location => 'Emplacement';

  @override
  String get location_hint => 'Ex: 123 Rue Principale, Ville, Pays';

  @override
  String get pick_location => 'Choisir un emplacement';

  @override
  String get update_location => 'Mettre à jour l\'emplacement';

  @override
  String get itinerary_name => 'De quoi s\'agit-il?';

  @override
  String get itinerary_name_hint => 'ex. Jour 1 : Arrivée et visite de la ville';

  @override
  String get confirmDeletion => 'Confirmer la suppression';

  @override
  String get confirmDeletionMessage => 'Êtes-vous sûr de vouloir supprimer cet élément ?';

  @override
  String get cancel => 'Annuler';

  @override
  String get delete => 'Supprimer';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get accommodation => 'Hébergement';

  @override
  String get transportation => 'Transport';

  @override
  String get sightseeing => 'Visite touristique';

  @override
  String get shopping => 'Shopping';

  @override
  String get activity => 'Activité';

  @override
  String get parking => 'Stationnement';

  @override
  String get note => 'Note';

  @override
  String get movie => 'Film';

  @override
  String get flight => 'Vol';

  @override
  String get carRental => 'Location de voiture';

  @override
  String get flight_number => 'Numéro de vol';

  @override
  String get flight_number_hint => 'ex. AF123';

  @override
  String get airline_name => 'Nom de la compagnie aérienne';

  @override
  String get airline_name_hint => 'ex. Air France';

  @override
  String get home => 'Accueil';

  @override
  String get trips => 'Voyages';

  @override
  String get saved => 'Favoris';

  @override
  String get accommodations => 'Hébergements';

  @override
  String get pro => 'Bientôt disponible';

  @override
  String get account => 'Compte';

  @override
  String get settings => 'Paramètres';

  @override
  String get favorites => 'Favoris';

  @override
  String get unlock_premium => 'RoamR Premium';

  @override
  String get premium_subtitle => 'Amenez votre planification de voyage au niveau supérieur';

  @override
  String get coming_soon => 'Bientôt disponible';

  @override
  String get ai_trip_planner => 'Planificateur de voyage IA';

  @override
  String get ai_trip_planner_desc => 'Obtenez des suggestions de voyage personnalisées et des itinéraires alimentés par l\'IA';

  @override
  String get trip_gallery => 'Galerie de voyage';

  @override
  String get trip_gallery_desc => 'Joignez et organisez des photos, des documents et des souvenirs pour chaque voyage';

  @override
  String get receipt_scanner => 'Scanner de reçus';

  @override
  String get receipt_scanner_desc => 'Suivez automatiquement les dépenses en scannant vos factures et reçus';

  @override
  String get interactive_maps => 'Cartes interactives';

  @override
  String get interactive_maps_desc => 'Visualisez tout votre itinéraire avec des cartes interactives et la navigation';

  @override
  String get trip_sharing => 'Partage de voyage';

  @override
  String get trip_sharing_desc => 'Collaborez et planifiez des voyages avec des amis et la famille';

  @override
  String get guest_user => 'Connectez-vous à votre compte';

  @override
  String get account_settings => 'Paramètres du compte';

  @override
  String get edit_profile => 'Modifier le profil';

  @override
  String get profile => 'Profil';

  @override
  String get profile_updated_successfully => 'Profil mis à jour avec succès';

  @override
  String get save => 'Enregistrer';

  @override
  String get sign_out_confirmation => 'Êtes-vous sûr de vouloir vous déconnecter ?';

  @override
  String get please_enter_your_name => 'Veuillez entrer votre nom';

  @override
  String get failed_to_load_profile => 'Échec du chargement des données de profil';

  @override
  String get notifications => 'Notifications';

  @override
  String get notifications_desc => 'Gérez vos préférences de notification';

  @override
  String get preferences => 'Préférences';

  @override
  String get theme => 'Thème';

  @override
  String get light => 'Clair';

  @override
  String get currency => 'Devise';

  @override
  String get currency_desc => 'Définissez votre devise préférée';

  @override
  String get about => 'À propos';

  @override
  String get about_app => 'À propos de RoamR';

  @override
  String get rate_app => 'Évaluer l\'application';

  @override
  String get privacy_policy => 'Politique de confidentialité';

  @override
  String get sign_in => 'Se connecter';

  @override
  String get sign_out => 'Se déconnecter';

  @override
  String get register => 'S\'inscrire';

  @override
  String get create_account => 'Créer un compte';

  @override
  String get continue_with_google => 'Continuer avec Google';

  @override
  String get continue_with_apple => 'Continuer avec Apple';

  @override
  String get or => 'ou';

  @override
  String get email => 'E-mail';

  @override
  String get password => 'Mot de passe';

  @override
  String get confirm_password => 'Confirmer le mot de passe';

  @override
  String get name => 'Nom';

  @override
  String get dont_have_account => 'Vous n\'avez pas de compte ?';

  @override
  String get already_have_account => 'Vous avez déjà un compte ?';

  @override
  String get signed_out_successfully => 'Déconnexion réussie';

  @override
  String get date_range_example => 'ex. 25 jan - 16 fév';

  @override
  String get add_place => 'Ajouter des lieux que vous souhaitez visiter';

  @override
  String get restaurant_name => 'Nom du restaurant';

  @override
  String get restaurant_name_hint => 'ex. Palais de Sushi';

  @override
  String get accommodation_name => 'Nom de l\'hébergement';

  @override
  String get accommodation_name_hint => 'ex. Hôtel Hilton';

  @override
  String get transportation_name => 'Nom du transport';

  @override
  String get transportation_name_hint => 'ex. Train pour Paris';

  @override
  String get sightseeing_name => 'Nom du lieu';

  @override
  String get sightseeing_hint => 'ex. Tour Eiffel';

  @override
  String get shopping_name => 'Nom du magasin';

  @override
  String get shopping_hint => 'ex. Galeries Lafayette';

  @override
  String get activity_name => 'Nom de l\'activité';

  @override
  String get activity_hint => 'ex. Cours de cuisine';

  @override
  String get parking_name => 'Emplacement de stationnement';

  @override
  String get parking_hint => 'ex. Parking central';

  @override
  String get note_name => 'Titre de la note';

  @override
  String get note_hint => 'ex. Rappels importants';

  @override
  String get movie_name => 'Nom du film/spectacle';

  @override
  String get movie_hint => 'ex. Spectacle de théâtre local';

  @override
  String get flight_title => 'Détails du vol';

  @override
  String get flight_title_hint => 'ex. Vol Paris-Nice';

  @override
  String get car_rental_name => 'Détails de la location de voiture';

  @override
  String get car_rental_hint => 'ex. Renault Clio - Europcar';

  @override
  String get amount_optional => 'Montant (si applicable)';

  @override
  String get checkin_date => 'Date d\'arrivée';

  @override
  String get checkout_date => 'Date de départ';

  @override
  String get stay_dates => 'Dates de séjour';

  @override
  String get select_checkin_checkout => 'Sélectionnez les dates d\'arrivée et de départ';

  @override
  String get start_date => 'Date de début';

  @override
  String get end_date => 'Date de fin';

  @override
  String get description => 'Description';

  @override
  String get description_hint => 'Ajoutez des notes ou des détails supplémentaires';

  @override
  String get about_desc => 'En savoir plus sur l\'application';

  @override
  String get system => 'Système';

  @override
  String get edit => 'Modifier';

  @override
  String get dark => 'Sombre';

  @override
  String get date_required => 'Date requise';

  @override
  String get date_within_trip => 'La date doit être comprise dans la période du voyage';

  @override
  String get no_favorites => 'Pas encore de favoris';

  @override
  String get add_favorites_hint => 'Vos itinéraires favoris apparaîtront ici';

  @override
  String get premium_title => 'RoamR Premium';

  @override
  String get premium => 'Premium';

  @override
  String get premium_features => 'Débloquer les fonctionnalités Premium';

  @override
  String get unlimited_trip_planning => 'Planification de voyage illimitée';

  @override
  String get unlimited_trip_planning_desc => 'Créez et gérez un nombre illimité de voyages';

  @override
  String get interactive_map => 'Cartes de Voyage Interactives';

  @override
  String get interactive_map_desc => 'Visualisez tout votre itinéraire sur des cartes interactives avec navigation';

  @override
  String get best_value => 'MEILLEURE VALEUR';

  @override
  String get yearly => 'Annuel';

  @override
  String get monthly => 'Mensuel';

  @override
  String get per_year => 'par an';

  @override
  String get per_month => 'par mois';

  @override
  String get save_fifty => 'Économisez 50%';

  @override
  String get restore_purchases => 'Restaurer les achats';

  @override
  String get subscription_details => 'Détails de l\'abonnement';

  @override
  String get subscription_status => 'Statut:';

  @override
  String get subscription_plan => 'Plan:';

  @override
  String get subscription_purchased => 'Acheté:';

  @override
  String get subscription_expires => 'Expire:';

  @override
  String get subscription_renews => 'Se renouvelle:';

  @override
  String get subscription_error => 'Impossible de charger les détails de l\'abonnement';

  @override
  String get loading_premium_options => 'Chargement des options premium...';

  @override
  String get premium_features_unlocked => 'Fonctionnalités premium débloquées !';

  @override
  String get premium_user_access => 'Vous êtes un utilisateur premium avec accès à toutes les fonctionnalités';

  @override
  String get no_locations => 'Aucun itinéraire avec données de localisation';

  @override
  String get map_not_available => 'Carte non disponible';

  @override
  String get add_location_details => 'Il n\'y a pas d\'emplacements à afficher sur la carte. Ajoutez d\'abord des détails d\'emplacement à vos itinéraires.';

  @override
  String get ok => 'OK';

  @override
  String get location_permission_denied => 'Permission de localisation refusée';

  @override
  String get location_permission_permanently_denied => 'Permission de localisation refusée définitivement';

  @override
  String get location_error => 'Erreur lors de l\'obtention de la localisation';

  @override
  String get delete_account => 'Supprimer le compte';

  @override
  String get delete_account_confirmation => 'Supprimer le compte ?';

  @override
  String get delete_account_message => 'Êtes-vous sûr de vouloir supprimer votre compte ? Cette action ne peut pas être annulée et toutes vos données seront définitivement supprimées.';

  @override
  String get account_deleted_successfully => 'Compte supprimé avec succès';

  @override
  String get premium_login_message => 'Connectez-vous pour accéder aux fonctionnalités premium';

  @override
  String get share_app => 'Partager l\'application';

  @override
  String get terms_of_service => 'Conditions d\'utilisation';

  @override
  String get send_feedback => 'Envoyer des commentaires';

  @override
  String get statistics => 'Statistiques';

  @override
  String get select_trip => 'Sélectionner un voyage';

  @override
  String get select_trip_to_view_statistics => 'Sélectionnez un voyage pour voir les statistiques';

  @override
  String get no_expenses_to_display => 'Aucune dépense à afficher';

  @override
  String get expense_over_time => 'Dépenses au fil du temps';

  @override
  String get expenses_by_category => 'Dépenses par catégorie';

  @override
  String get total => 'Total';

  @override
  String get category_distribution => 'Répartition par catégorie';

  @override
  String get remove_from_favorites => 'Supprimer des favoris';

  @override
  String get remove => 'Supprimer';

  @override
  String get remove_from_favorites_confirmation => 'Supprimer des favoris ?';

  @override
  String get remove_from_favorites_message => 'Êtes-vous sûr de vouloir supprimer cet itinéraire de vos favoris ?';

  @override
  String get pie_chart => 'Graphique circulaire';

  @override
  String get bar_chart => 'Graphique à barres';

  @override
  String get trip_summary => 'Résumé du voyage';

  @override
  String get total_trips => 'Total des voyages';

  @override
  String get total_places_visited => 'Lieux visités';

  @override
  String get avg_places_per_trip => 'Lieux/voyage';

  @override
  String get total_days_traveled => 'Jours voyagés';

  @override
  String get shortest_trip => 'Voyage le plus court';

  @override
  String get longest_trip => 'Voyage le plus long';

  @override
  String get day => 'jour';

  @override
  String get days => 'jours';

  @override
  String get flights => 'Vols';

  @override
  String get most_frequent => 'Activité la plus fréquente';

  @override
  String get most_activities_day => 'Plus d\'activités/jour';

  @override
  String get avg_activities_day => 'Moy. activités/jour';

  @override
  String get avg_spend_per_trip => 'Dépense moy./voyage';

  @override
  String get most_expensive_trip => 'Voyage le plus cher';

  @override
  String get least_expensive_trip => 'Voyage le moins cher';

  @override
  String get top_expense_category => 'Catégorie la plus coûteuse';

  @override
  String get achievements => 'Réalisations';

  @override
  String get achievements_description => 'Suivez vos étapes de voyage et débloquez des réalisations au fur et à mesure que vous explorez le monde !';

  @override
  String get destination_achievements => 'Réalisations de Destination';

  @override
  String get expense_achievements => 'Réalisations de Dépenses';

  @override
  String get frequency_achievements => 'Réalisations de Fréquence de Voyage';

  @override
  String get duration_achievements => 'Réalisations de Durée de Voyage';

  @override
  String get flight_achievements => 'Réalisations de Vol';

  @override
  String get novice_traveller_title => 'Voyageur Novice';

  @override
  String get novice_traveller_desc => 'Visitez 3 pays différents ou plus';

  @override
  String get world_traveller_title => 'Voyageur du Monde';

  @override
  String get world_traveller_desc => 'Visitez 10 pays différents ou plus';

  @override
  String get globetrotter_title => 'Globe-trotter';

  @override
  String get globetrotter_desc => 'Visitez 20 pays différents ou plus';

  @override
  String get continental_explorer_title => 'Explorateur Continental';

  @override
  String get continental_explorer_desc => 'Visitez 2 continents ou plus et au moins 20 pays';

  @override
  String get continental_collector_title => 'Collectionneur Continental';

  @override
  String get continental_collector_desc => 'Visitez 4 continents ou plus et au moins 25 pays';

  @override
  String get world_conqueror_title => 'Conquérant du Monde';

  @override
  String get world_conqueror_desc => 'Visitez les 7 continents et au moins 30 pays';

  @override
  String get budget_tracker_title => 'Gestionnaire de Budget';

  @override
  String get budget_tracker_desc => 'Suivez 1 000€+ de dépenses totales';

  @override
  String get expense_manager_title => 'Gestionnaire de Dépenses';

  @override
  String get expense_manager_desc => 'Suivez 5 000€+ de dépenses totales';

  @override
  String get financial_voyager_title => 'Voyageur Financier';

  @override
  String get financial_voyager_desc => 'Suivez 10 000€+ de dépenses totales';

  @override
  String get luxury_traveller_title => 'Voyageur de Luxe';

  @override
  String get luxury_traveller_desc => 'Suivez 20 000€+ de dépenses totales';

  @override
  String get travel_beginner_title => 'Débutant en Voyage';

  @override
  String get travel_beginner_desc => 'Complétez 3 voyages ou plus';

  @override
  String get travel_enthusiast_title => 'Enthousiaste de Voyage';

  @override
  String get travel_enthusiast_desc => 'Complétez 10 voyages ou plus';

  @override
  String get travel_addict_title => 'Accro aux Voyages';

  @override
  String get travel_addict_desc => 'Complétez 20 voyages ou plus';

  @override
  String get day_tripper_title => 'Excursionniste d\'un Jour';

  @override
  String get day_tripper_desc => 'Complétez un voyage d\'au moins 1 jour';

  @override
  String get weekend_wanderer_title => 'Voyageur de Week-end';

  @override
  String get weekend_wanderer_desc => 'Complétez un voyage d\'au moins 3 jours';

  @override
  String get vacation_voyager_title => 'Voyageur de Vacances';

  @override
  String get vacation_voyager_desc => 'Complétez un voyage d\'au moins 7 jours';

  @override
  String get extended_explorer_title => 'Explorateur Prolongé';

  @override
  String get extended_explorer_desc => 'Complétez un voyage d\'au moins 14 jours';

  @override
  String get long_term_traveler_title => 'Voyageur à Long Terme';

  @override
  String get long_term_traveler_desc => 'Complétez un voyage d\'au moins 30 jours';

  @override
  String get nomadic_adventurer_title => 'Aventurier Nomade';

  @override
  String get nomadic_adventurer_desc => 'Complétez un voyage d\'au moins 60 jours';

  @override
  String get first_flight_title => 'Débutant de Vol';

  @override
  String get first_flight_desc => 'Enregistrez 5 vols ou plus';

  @override
  String get frequent_flyer_title => 'Voyageur Fréquent';

  @override
  String get frequent_flyer_desc => 'Enregistrez 15 vols ou plus';

  @override
  String get aviation_enthusiast_title => 'Passionné d\'Aviation';

  @override
  String get aviation_enthusiast_desc => 'Enregistrez 30 vols ou plus';

  @override
  String get progress => 'Progression';

  @override
  String get close => 'Fermer';

  @override
  String get complete_trips_for_achievements => 'Complétez des voyages pour débloquer des succès !';

  @override
  String get view_all => 'Voir Tout';

  @override
  String get app_tagline => 'Votre compagnon de voyage';

  @override
  String get website => 'Site Web';

  @override
  String get facebook => 'Facebook';

  @override
  String get twitter => 'Twitter/X';

  @override
  String get instagram => 'Instagram';

  @override
  String get feedback_email_subject => 'Commentaires sur RoamR';

  @override
  String get feedback_email_body => 'J\'aimerais fournir des commentaires sur RoamR :';

  @override
  String get share_app_message_prefix => 'Découvrez RoamR, une application de planification de voyage : ';

  @override
  String get share_app_subject => 'RoamR - Votre compagnon de voyage';

  @override
  String could_not_launch_url(Object url) {
    return 'Impossible d\'ouvrir $url';
  }

  @override
  String get could_not_share_app => 'Impossible de partager l\'application';

  @override
  String get could_not_launch_email => 'Impossible d\'ouvrir le client de messagerie';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'Photos';

  @override
  String get no_photos_added => 'Aucune photo ajoutée';

  @override
  String get gallery => 'Galerie';

  @override
  String get camera => 'Appareil photo';

  @override
  String get error_loading_image => 'Erreur lors du chargement de l\'image';

  @override
  String get unsupported_attachment_type => 'Type de pièce jointe non pris en charge';

  @override
  String get file_not_found => 'Fichier introuvable';

  @override
  String get see_more => 'Voir plus';

  @override
  String get select_category => 'Sélectionner une catégorie';

  @override
  String get done => 'Terminé';

  @override
  String get search_activities_and_places => 'Rechercher des activités et des lieux';

  @override
  String get no_results_found => 'Aucun résultat trouvé';

  @override
  String get travel => 'Voyage';

  @override
  String get food_drink => 'Nourriture et boisson';

  @override
  String get art_fun => 'Art et divertissement';

  @override
  String get search_results => 'Résultats de recherche';

  @override
  String get trip_photos => 'Photos de voyage';

  @override
  String get error_loading_photos => 'Erreur lors du chargement des photos';

  @override
  String get no_photos_for_trip => 'Pas de photos pour ce voyage';

  @override
  String get no_audio_for_trip => 'Pas d\'enregistrements audio pour ce voyage';

  @override
  String get no_documents_for_trip => 'Pas de documents pour ce voyage';

  @override
  String get no_attachments_for_trip => 'Pas de pièces jointes pour ce voyage';

  @override
  String get audio => 'Audio';

  @override
  String get documents => 'Documents';

  @override
  String get open => 'Ouvrir';

  @override
  String get opening_file => 'Ouverture du fichier';

  @override
  String get select_document => 'Sélectionner un document';

  @override
  String get error_opening_file => 'Erreur lors de l\'ouverture du fichier';

  @override
  String get no_content_available => 'Aucun contenu disponible à afficher';

  @override
  String get error_loading_document => 'Erreur lors du chargement du document';

  @override
  String get size => 'Taille';

  @override
  String get modified => 'Modifié';

  @override
  String get word_document => 'Le contenu du document Word ne peut pas être affiché directement';

  @override
  String get excel_spreadsheet => 'Le contenu de la feuille de calcul Excel ne peut pas être affiché directement';

  @override
  String get file_type_not_supported => 'Ce type de fichier ne peut pas être affiché directement';

  @override
  String get file_available_at => 'Le fichier est disponible à';

  @override
  String get attachments => 'Pièces jointes';

  @override
  String get add_attachment => 'Ajouter une pièce jointe';

  @override
  String get choose_attachment_type => 'Choisir le type de pièce jointe';

  @override
  String get voucher_code => 'Code Promo';

  @override
  String get enter_voucher_code => 'Entrer un Code Promo';

  @override
  String get enter_voucher_code_hint => 'Entrez votre code promo';

  @override
  String get please_enter_voucher_code => 'Veuillez entrer un code promo';

  @override
  String get voucher_code_applied => 'Code promo appliqué avec succès !';

  @override
  String get invalid_voucher_code => 'Code promo invalide. Veuillez réessayer.';

  @override
  String get apply => 'Appliquer';

  @override
  String get night => 'nuit';

  @override
  String get nights => 'nuits';

  @override
  String get verify_email => 'Vérifier l\'email';

  @override
  String get verification_code_sent => 'Un code de vérification a été envoyé à';

  @override
  String get verification_code => 'Code de vérification';

  @override
  String get please_enter_verification_code => 'Veuillez entrer le code de vérification';

  @override
  String get verification_code_too_short => 'Le code de vérification est trop court';

  @override
  String get verify => 'Vérifier';

  @override
  String get resend_code => 'Renvoyer le code';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'Veuillez attendre $seconds secondes avant de demander un nouveau code';
  }

  @override
  String get please_enter_name => 'Veuillez entrer votre nom';

  @override
  String get please_enter_email => 'Veuillez entrer votre email';

  @override
  String get please_enter_valid_email => 'Veuillez entrer une adresse email valide';

  @override
  String get verification_code_login => 'Nous enverrons un code de vérification à votre email';

  @override
  String get verification_code_register => 'Nous enverrons un code de vérification à votre email pour finaliser l\'inscription';

  @override
  String get continue_with_email => 'Continuer avec l\'email';

  @override
  String get register_with_email => 'S\'inscrire avec l\'email';

  @override
  String get error_invalid_email => 'L\'adresse e-mail n\'est pas correctement formatée.';

  @override
  String get error_user_disabled => 'Ce compte utilisateur a été désactivé.';

  @override
  String get error_user_not_found => 'Ce compte n\'existe pas ou a été supprimé.';

  @override
  String get error_invalid_credentials => 'E-mail ou mot de passe incorrect.';

  @override
  String get error_email_exists => 'L\'adresse e-mail est déjà utilisée par un autre compte.';

  @override
  String get error_weak_password => 'Le mot de passe est trop faible.';

  @override
  String get error_email_not_registered => 'Cet e-mail n\'est pas enregistré. Veuillez vous inscrire d\'abord.';

  @override
  String get error_otp_disabled => 'La vérification OTP est désactivée. Veuillez essayer une autre méthode.';

  @override
  String get error_unknown => 'Une erreur inconnue s\'est produite. Veuillez réessayer plus tard.';

  @override
  String get account_information => 'Informations du compte';

  @override
  String get account_actions => 'Actions du compte';

  @override
  String get enter_your_name => 'Entrez votre nom';

  @override
  String get onboarding_welcome_title => 'Bienvenue sur RoamR';

  @override
  String get onboarding_welcome_subtitle => 'Votre compagnon de voyage personnel';

  @override
  String get onboarding_welcome_button => 'Commencer';

  @override
  String get onboarding_select_language => 'Sélectionner la langue';

  @override
  String get onboarding_features_title => 'Fonctionnalités';

  @override
  String get onboarding_feature_maps => 'Cartes Interactives';

  @override
  String get onboarding_feature_calendar => 'Calendrier de Voyage';

  @override
  String get onboarding_feature_social => 'Partage Social';

  @override
  String get onboarding_feature_personalize => 'Expérience Personnalisée';

  @override
  String get onboarding_features_button => 'Suivant';

  @override
  String get onboarding_theme_title => 'Choisir le Thème';

  @override
  String get onboarding_theme_subtitle => 'Sélectionnez votre thème préféré';

  @override
  String get onboarding_theme_button => 'Suivant';

  @override
  String get features_title => 'Planifiez votre voyage parfait';

  @override
  String get features_subtitle => 'Tout ce dont vous avez besoin pour organiser votre voyage';

  @override
  String get location_search => 'Recherche intelligente de lieux';

  @override
  String get location_search_desc => 'Trouvez et ajoutez des lieux à votre itinéraire facilement';

  @override
  String get itinerary_map => 'Carte interactive de l\'itinéraire';

  @override
  String get itinerary_map_desc => 'Visualisez votre itinéraire complet sur une carte interactive';

  @override
  String get expense_tracking => 'Suivi des dépenses';

  @override
  String get expense_tracking_desc => 'Suivez et catégorisez toutes vos dépenses de voyage';

  @override
  String get travel_analytics => 'Analyses de voyage';

  @override
  String get travel_analytics_desc => 'Consultez des statistiques et des graphiques de vos habitudes de voyage';

  @override
  String get trip_attachments => 'Pièces jointes du voyage';

  @override
  String get trip_attachments_desc => 'Joignez des photos, documents et souvenirs à vos voyages';

  @override
  String get comprehensive_planning => 'Planification complète';

  @override
  String get comprehensive_planning_desc => 'Planifiez vols, hôtels, activités et plus avec des options détaillées';

  @override
  String get no_attachments_added_yet => 'Aucune pièce jointe ajoutée pour le moment';

  @override
  String get usage_left => 'restant';

  @override
  String get usage_details_title => 'Détails d\'utilisation';

  @override
  String get unlimited => 'Illimité';

  @override
  String get used => 'Utilisé';

  @override
  String get left => 'restant';

  @override
  String get premium_required_message => 'Vous avez atteint la limite gratuite. Passez à la version premium pour créer plus de voyages.';

  @override
  String get upgrade => 'Mettre à niveau';

  @override
  String get itineraries => 'Itinéraires';

  @override
  String get notification_title_no_trips => 'Commencez votre aventure !';

  @override
  String get notification_body_no_trips => 'Créez votre premier voyage et commencez à planifier votre parcours.';

  @override
  String get notification_title_no_itinerary => 'Planifiez votre itinéraire !';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'Ajoutez un itinéraire à votre voyage \"$tripName\".';
  }

  @override
  String get notification_title_no_places => 'Ajoutez des lieux à visiter !';

  @override
  String notification_body_no_places(Object tripName) {
    return 'Améliorez votre voyage \"$tripName\" en ajoutant des lieux à visiter.';
  }

  @override
  String get notification_title_keep_planning => 'Continuez à planifier votre voyage !';

  @override
  String notification_body_keep_planning(Object tripName) {
    return 'Continuez à construire l\'itinéraire parfait pour \"$tripName\".';
  }

  @override
  String get ai_chat_title => 'AI Chat';

  @override
  String get ai_chat_input_hint => 'Ask me about your travel plans...';

  @override
  String get ai_chat_loading => 'RoamR AI is thinking...';

  @override
  String get ai_chat_welcome => 'Hi! I\'m RoamR AI, your travel assistant. How can I help you plan your next adventure?';
}
