// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for German (`de`).
class AppLocalizationsDe extends AppLocalizations {
  AppLocalizationsDe([String locale = 'de']) : super(locale);

  @override
  String get language => 'Sprache';

  @override
  String get language_desc => 'Ändern Sie Ihre bevorzugte Sprache';

  @override
  String get all => 'Alle';

  @override
  String get grocery => 'Lebensmittel';

  @override
  String get food => 'Essen';

  @override
  String get work => 'Arbeit';

  @override
  String get entertainment => 'Unterhaltung';

  @override
  String get traveling => 'Reisen';

  @override
  String get other => 'Andere';

  @override
  String get total_expenses => 'Gesamtausgaben';

  @override
  String get select_catagory => 'Kategorie auswählen';

  @override
  String get category => 'Kategorie';

  @override
  String get your_trips => 'Deine Reisen';

  @override
  String get no_trips => 'Noch keine Reisen hinzugefügt.';

  @override
  String get add_trip => 'Reise hinzufügen';

  @override
  String get edit_trip => 'Reise bearbeiten';

  @override
  String get title => 'Titel';

  @override
  String get enter_title => 'Reisename eingeben';

  @override
  String get date_range => 'Wann reist du?';

  @override
  String get select_date_range => 'Bitte wähle einen Datumsbereich';

  @override
  String get country => 'Land';

  @override
  String get select_country => 'Wähle ein Land';

  @override
  String get country_hint => 'Wohin reist du?';

  @override
  String get city => 'Stadt';

  @override
  String get city_hint => 'Gib eine Stadt ein';

  @override
  String get select_city => 'Wähle eine Stadt';

  @override
  String get failed_to_load_trips => 'Fehler beim Laden der Reisen';

  @override
  String get past_trips => 'Vergangene Reisen';

  @override
  String get trip_name => 'Reisename';

  @override
  String get trip_name_hint => 'z.B. Sommerurlaub 2023';

  @override
  String get amount => 'Betrag';

  @override
  String get date => 'Datum';

  @override
  String get departure_date => 'Abreisedatum';

  @override
  String get edit_itinerary => 'Reiseplan bearbeiten';

  @override
  String get add_itinerary => 'Reiseplan hinzufügen';

  @override
  String get itinerary_details => 'Reiseplandetails';

  @override
  String get location => 'Standort';

  @override
  String get location_hint => 'z.B. 123 Hauptstraße, Stadt, Land';

  @override
  String get pick_location => 'Standort auswählen';

  @override
  String get update_location => 'Standort aktualisieren';

  @override
  String get itinerary_name => 'Worum geht es?';

  @override
  String get itinerary_name_hint => 'z.B. Tag 1: Ankunft und Stadtrundfahrt';

  @override
  String get confirmDeletion => 'Löschen bestätigen';

  @override
  String get confirmDeletionMessage => 'Bist du sicher, dass du diesen Eintrag löschen möchtest?';

  @override
  String get cancel => 'Abbrechen';

  @override
  String get delete => 'Löschen';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get accommodation => 'Unterkunft';

  @override
  String get transportation => 'Transport';

  @override
  String get sightseeing => 'Besichtigung';

  @override
  String get shopping => 'Einkaufen';

  @override
  String get activity => 'Aktivität';

  @override
  String get parking => 'Parken';

  @override
  String get note => 'Notiz';

  @override
  String get movie => 'Film';

  @override
  String get flight => 'Flug';

  @override
  String get carRental => 'Autovermietung';

  @override
  String get flight_number => 'Flugnummer';

  @override
  String get flight_number_hint => 'z.B. LH123';

  @override
  String get airline_name => 'Fluggesellschaft';

  @override
  String get airline_name_hint => 'z.B. Lufthansa';

  @override
  String get home => 'Startseite';

  @override
  String get trips => 'Reisen';

  @override
  String get saved => 'Lesezeichen';

  @override
  String get accommodations => 'Unterkünfte';

  @override
  String get pro => 'Demnächst';

  @override
  String get account => 'Konto';

  @override
  String get settings => 'Einstellungen';

  @override
  String get favorites => 'Lesezeichen';

  @override
  String get unlock_premium => 'RoamR Premium';

  @override
  String get premium_subtitle => 'Bringe deine Reiseplanung auf die nächste Stufe';

  @override
  String get coming_soon => 'Demnächst';

  @override
  String get ai_trip_planner => 'KI-Reiseplaner';

  @override
  String get ai_trip_planner_desc => 'Erhalte personalisierte Reisevorschläge und Reisepläne mit KI';

  @override
  String get trip_gallery => 'Reisegalerie';

  @override
  String get trip_gallery_desc => 'Füge Fotos, Dokumente und Erinnerungen für jede Reise hinzu';

  @override
  String get receipt_scanner => 'Belegscanner';

  @override
  String get receipt_scanner_desc => 'Verfolge Ausgaben automatisch durch Scannen deiner Belege';

  @override
  String get interactive_maps => 'Interaktive Karten';

  @override
  String get interactive_maps_desc => 'Visualisiere deine gesamte Reiseroute mit interaktiven Karten';

  @override
  String get trip_sharing => 'Reise teilen';

  @override
  String get trip_sharing_desc => 'Plane Reisen gemeinsam mit Freunden und Familie';

  @override
  String get guest_user => 'Melden Sie sich bei Ihrem Konto an';

  @override
  String get account_settings => 'Kontoeinstellungen';

  @override
  String get edit_profile => 'Profil bearbeiten';

  @override
  String get profile => 'Profil';

  @override
  String get profile_updated_successfully => 'Profil erfolgreich aktualisiert';

  @override
  String get save => 'Speichern';

  @override
  String get sign_out_confirmation => 'Bist du sicher, dass du dich abmelden möchtest?';

  @override
  String get please_enter_your_name => 'Bitte gib deinen Namen ein';

  @override
  String get failed_to_load_profile => 'Fehler beim Laden der Profildaten';

  @override
  String get notifications => 'Benachrichtigungen';

  @override
  String get notifications_desc => 'Verwalte deine Benachrichtigungseinstellungen';

  @override
  String get preferences => 'Einstellungen';

  @override
  String get theme => 'Thema';

  @override
  String get light => 'Hell';

  @override
  String get currency => 'Währung';

  @override
  String get currency_desc => 'Lege deine bevorzugte Währung fest';

  @override
  String get about => 'Über';

  @override
  String get about_app => 'Über RoamR';

  @override
  String get rate_app => 'App bewerten';

  @override
  String get privacy_policy => 'Datenschutzrichtlinie';

  @override
  String get sign_in => 'Anmelden';

  @override
  String get sign_out => 'Abmelden';

  @override
  String get register => 'Registrieren';

  @override
  String get create_account => 'Konto erstellen';

  @override
  String get continue_with_google => 'Mit Google fortfahren';

  @override
  String get continue_with_apple => 'Mit Apple fortfahren';

  @override
  String get or => 'oder';

  @override
  String get email => 'E-Mail';

  @override
  String get password => 'Passwort';

  @override
  String get confirm_password => 'Passwort bestätigen';

  @override
  String get name => 'Name';

  @override
  String get dont_have_account => 'Noch kein Konto?';

  @override
  String get already_have_account => 'Bereits ein Konto?';

  @override
  String get signed_out_successfully => 'Erfolgreich abgemeldet';

  @override
  String get date_range_example => 'z.B. 25. Jan - 16. Feb';

  @override
  String get add_place => 'Orte hinzufügen, die du besuchen möchtest';

  @override
  String get restaurant_name => 'Restaurantname';

  @override
  String get restaurant_name_hint => 'z.B. Sushi Palast';

  @override
  String get accommodation_name => 'Unterkunftsname';

  @override
  String get accommodation_name_hint => 'z.B. Hilton Hotel';

  @override
  String get transportation_name => 'Transportname';

  @override
  String get transportation_name_hint => 'z.B. Zug nach Berlin';

  @override
  String get sightseeing_name => 'Ortsname';

  @override
  String get sightseeing_hint => 'z.B. Brandenburger Tor';

  @override
  String get shopping_name => 'Geschäftsname';

  @override
  String get shopping_hint => 'z.B. Kaufhaus des Westens';

  @override
  String get activity_name => 'Aktivitätsname';

  @override
  String get activity_hint => 'z.B. Kochkurs';

  @override
  String get parking_name => 'Parkplatz';

  @override
  String get parking_hint => 'z.B. Zentralparkplatz';

  @override
  String get note_name => 'Notiztitel';

  @override
  String get note_hint => 'z.B. Wichtige Erinnerungen';

  @override
  String get movie_name => 'Film/Show-Name';

  @override
  String get movie_hint => 'z.B. Lokale Theateraufführung';

  @override
  String get flight_title => 'Flugdetails';

  @override
  String get flight_title_hint => 'z.B. Flug von Berlin nach München';

  @override
  String get car_rental_name => 'Mietwagendetails';

  @override
  String get car_rental_hint => 'z.B. VW Golf - Sixt';

  @override
  String get amount_optional => 'Betrag (falls zutreffend)';

  @override
  String get checkin_date => 'Check-in-Datum';

  @override
  String get checkout_date => 'Check-out-Datum';

  @override
  String get stay_dates => 'Aufenthaltsdaten';

  @override
  String get select_checkin_checkout => 'Wähle Check-in und Check-out Daten';

  @override
  String get start_date => 'Startdatum';

  @override
  String get end_date => 'Enddatum';

  @override
  String get description => 'Beschreibung';

  @override
  String get description_hint => 'Füge zusätzliche Notizen oder Details hinzu';

  @override
  String get about_desc => 'Erfahre mehr über die App';

  @override
  String get system => 'System';

  @override
  String get edit => 'Bearbeiten';

  @override
  String get dark => 'Dunkel';

  @override
  String get date_required => 'Datum erforderlich';

  @override
  String get date_within_trip => 'Datum muss innerhalb des Reisezeitraums liegen';

  @override
  String get no_favorites => 'Noch keine Lesezeichen';

  @override
  String get add_favorites_hint => 'Deine mit Lesezeichen versehenen Reiserouten werden hier angezeigt';

  @override
  String get premium_title => 'RoamR Premium';

  @override
  String get premium => 'Premium';

  @override
  String get premium_features => 'Premium-Funktionen freischalten';

  @override
  String get unlimited_trip_planning => 'Unbegrenzte Reiseplanung';

  @override
  String get unlimited_trip_planning_desc => 'Erstelle und verwalte unbegrenzt viele Reisen';

  @override
  String get interactive_map => 'Interaktive Reisekarten';

  @override
  String get interactive_map_desc => 'Sieh dir deine gesamte Reiseroute auf interaktiven Karten mit Navigation an';

  @override
  String get best_value => 'BESTES ANGEBOT';

  @override
  String get yearly => 'Jährlich';

  @override
  String get monthly => 'Monatlich';

  @override
  String get per_year => 'pro Jahr';

  @override
  String get per_month => 'pro Monat';

  @override
  String get save_fifty => 'Spare 50%';

  @override
  String get restore_purchases => 'Käufe wiederherstellen';

  @override
  String get subscription_details => 'Abonnementdetails';

  @override
  String get subscription_status => 'Status:';

  @override
  String get subscription_plan => 'Plan:';

  @override
  String get subscription_purchased => 'Gekauft:';

  @override
  String get subscription_expires => 'Läuft ab:';

  @override
  String get subscription_renews => 'Verlängert sich am:';

  @override
  String get subscription_error => 'Abonnementdetails konnten nicht geladen werden';

  @override
  String get loading_premium_options => 'Premium-Optionen werden geladen...';

  @override
  String get premium_features_unlocked => 'Premium-Funktionen freigeschaltet!';

  @override
  String get premium_user_access => 'Du bist ein Premium-Benutzer mit Zugriff auf alle Funktionen';

  @override
  String get no_locations => 'Keine Reisepläne mit Standortdaten';

  @override
  String get map_not_available => 'Karte nicht verfügbar';

  @override
  String get add_location_details => 'Es gibt keine Standorte, die auf der Karte angezeigt werden können. Füge zuerst Standortdetails zu deinen Reiseplänen hinzu.';

  @override
  String get ok => 'OK';

  @override
  String get location_permission_denied => 'Standortberechtigung verweigert';

  @override
  String get location_permission_permanently_denied => 'Standortberechtigung dauerhaft verweigert';

  @override
  String get location_error => 'Fehler beim Abrufen des Standorts';

  @override
  String get delete_account => 'Konto löschen';

  @override
  String get delete_account_confirmation => 'Konto löschen?';

  @override
  String get delete_account_message => 'Bist du sicher, dass du dein Konto löschen möchtest? Diese Aktion kann nicht rückgängig gemacht werden und alle deine Daten werden dauerhaft gelöscht.';

  @override
  String get account_deleted_successfully => 'Konto erfolgreich gelöscht';

  @override
  String get premium_login_message => 'Melde dich an, um auf Premium-Funktionen zuzugreifen';

  @override
  String get share_app => 'App teilen';

  @override
  String get terms_of_service => 'Nutzungsbedingungen';

  @override
  String get send_feedback => 'Feedback senden';

  @override
  String get statistics => 'Statistiken';

  @override
  String get select_trip => 'Reise auswählen';

  @override
  String get select_trip_to_view_statistics => 'Wähle eine Reise, um Statistiken anzuzeigen';

  @override
  String get no_expenses_to_display => 'Keine Ausgaben zum Anzeigen';

  @override
  String get expense_over_time => 'Ausgaben im Zeitverlauf';

  @override
  String get expenses_by_category => 'Ausgaben nach Kategorie';

  @override
  String get total => 'Gesamt';

  @override
  String get category_distribution => 'Kategorieverteilung';

  @override
  String get remove_from_favorites => 'Aus Lesezeichen entfernen';

  @override
  String get remove => 'Entfernen';

  @override
  String get remove_from_favorites_confirmation => 'Aus Lesezeichen entfernen?';

  @override
  String get remove_from_favorites_message => 'Sind Sie sicher, dass Sie diese Reiseroute aus Ihren Lesezeichen entfernen möchten?';

  @override
  String get pie_chart => 'Kreisdiagramm';

  @override
  String get bar_chart => 'Balkendiagramm';

  @override
  String get trip_summary => 'Reisezusammenfassung';

  @override
  String get total_trips => 'Gesamte Reisen';

  @override
  String get total_places_visited => 'Besuchte Orte';

  @override
  String get avg_places_per_trip => 'Orte/Reise';

  @override
  String get total_days_traveled => 'Reisetage';

  @override
  String get shortest_trip => 'Kürzeste Reise';

  @override
  String get longest_trip => 'Längste Reise';

  @override
  String get day => 'Tag';

  @override
  String get days => 'Tage';

  @override
  String get flights => 'Flüge';

  @override
  String get most_frequent => 'Häufigste Aktivität';

  @override
  String get most_activities_day => 'Meiste Aktivitäten/Tag';

  @override
  String get avg_activities_day => 'Durchschn. Aktivitäten/Tag';

  @override
  String get avg_spend_per_trip => 'Durchschn. Ausgaben/Reise';

  @override
  String get most_expensive_trip => 'Teuerste Reise';

  @override
  String get least_expensive_trip => 'Günstigste Reise';

  @override
  String get top_expense_category => 'Top-Ausgabenkategorie';

  @override
  String get achievements => 'Erfolge';

  @override
  String get achievements_description => 'Verfolge deine Reisemeilensteine und schalte Erfolge frei, während du die Welt erkundest!';

  @override
  String get destination_achievements => 'Reiseziel-Erfolge';

  @override
  String get expense_achievements => 'Ausgaben-Erfolge';

  @override
  String get frequency_achievements => 'Reisehäufigkeits-Erfolge';

  @override
  String get duration_achievements => 'Reisedauer-Erfolge';

  @override
  String get flight_achievements => 'Flug-Erfolge';

  @override
  String get novice_traveller_title => 'Anfänger-Reisender';

  @override
  String get novice_traveller_desc => 'Besuche 3 oder mehr verschiedene Länder';

  @override
  String get world_traveller_title => 'Weltreisender';

  @override
  String get world_traveller_desc => 'Besuche 10 oder mehr verschiedene Länder';

  @override
  String get globetrotter_title => 'Globetrotter';

  @override
  String get globetrotter_desc => 'Besuche 20 oder mehr verschiedene Länder';

  @override
  String get continental_explorer_title => 'Kontinental-Entdecker';

  @override
  String get continental_explorer_desc => 'Besuche 2 oder mehr Kontinente und mindestens 20 Länder';

  @override
  String get continental_collector_title => 'Kontinental-Sammler';

  @override
  String get continental_collector_desc => 'Besuche 4 oder mehr Kontinente und mindestens 25 Länder';

  @override
  String get world_conqueror_title => 'Welteroberer';

  @override
  String get world_conqueror_desc => 'Besuche alle 7 Kontinente und mindestens 30 Länder';

  @override
  String get budget_tracker_title => 'Budget-Tracker';

  @override
  String get budget_tracker_desc => 'Verfolge 1.000€+ an Gesamtausgaben';

  @override
  String get expense_manager_title => 'Ausgaben-Manager';

  @override
  String get expense_manager_desc => 'Verfolge 5.000€+ an Gesamtausgaben';

  @override
  String get financial_voyager_title => 'Finanz-Reisender';

  @override
  String get financial_voyager_desc => 'Verfolge 10.000€+ an Gesamtausgaben';

  @override
  String get luxury_traveller_title => 'Luxus-Reisender';

  @override
  String get luxury_traveller_desc => 'Verfolge 20.000€+ an Gesamtausgaben';

  @override
  String get travel_beginner_title => 'Reise-Anfänger';

  @override
  String get travel_beginner_desc => 'Schließe 3 oder mehr Reisen ab';

  @override
  String get travel_enthusiast_title => 'Reise-Enthusiast';

  @override
  String get travel_enthusiast_desc => 'Schließe 10 oder mehr Reisen ab';

  @override
  String get travel_addict_title => 'Reise-Süchtiger';

  @override
  String get travel_addict_desc => 'Schließe 20 oder mehr Reisen ab';

  @override
  String get day_tripper_title => 'Tagesausflügler';

  @override
  String get day_tripper_desc => 'Schließe eine Reise von mindestens 1 Tag ab';

  @override
  String get weekend_wanderer_title => 'Wochenend-Wanderer';

  @override
  String get weekend_wanderer_desc => 'Schließe eine Reise von mindestens 3 Tagen ab';

  @override
  String get vacation_voyager_title => 'Urlaubs-Reisender';

  @override
  String get vacation_voyager_desc => 'Schließe eine Reise von mindestens 7 Tagen ab';

  @override
  String get extended_explorer_title => 'Langzeit-Entdecker';

  @override
  String get extended_explorer_desc => 'Schließe eine Reise von mindestens 14 Tagen ab';

  @override
  String get long_term_traveler_title => 'Langzeit-Reisender';

  @override
  String get long_term_traveler_desc => 'Schließe eine Reise von mindestens 30 Tagen ab';

  @override
  String get nomadic_adventurer_title => 'Nomadischer Abenteurer';

  @override
  String get nomadic_adventurer_desc => 'Schließe eine Reise von mindestens 60 Tagen ab';

  @override
  String get first_flight_title => 'Flug-Anfänger';

  @override
  String get first_flight_desc => 'Zeichne 5 oder mehr Flüge auf';

  @override
  String get frequent_flyer_title => 'Vielflieger';

  @override
  String get frequent_flyer_desc => 'Zeichne 15 oder mehr Flüge auf';

  @override
  String get aviation_enthusiast_title => 'Luftfahrt-Enthusiast';

  @override
  String get aviation_enthusiast_desc => 'Zeichne 30 oder mehr Flüge auf';

  @override
  String get progress => 'Fortschritt';

  @override
  String get close => 'Schließen';

  @override
  String get complete_trips_for_achievements => 'Schließe Reisen ab, um Erfolge freizuschalten!';

  @override
  String get view_all => 'Alle anzeigen';

  @override
  String get app_tagline => 'Dein Reisebegleiter';

  @override
  String get website => 'Webseite';

  @override
  String get facebook => 'Facebook';

  @override
  String get twitter => 'Twitter/X';

  @override
  String get instagram => 'Instagram';

  @override
  String get feedback_email_subject => 'RoamR Feedback';

  @override
  String get feedback_email_body => 'Ich möchte Feedback zu RoamR geben:';

  @override
  String get share_app_message_prefix => 'Entdecke RoamR, eine Reiseplanungs-App: ';

  @override
  String get share_app_subject => 'RoamR - Dein Reisebegleiter';

  @override
  String could_not_launch_url(Object url) {
    return 'Konnte $url nicht öffnen';
  }

  @override
  String get could_not_share_app => 'App konnte nicht geteilt werden';

  @override
  String get could_not_launch_email => 'E-Mail-Client konnte nicht geöffnet werden';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'Fotos';

  @override
  String get no_photos_added => 'Noch keine Fotos hinzugefügt';

  @override
  String get gallery => 'Galerie';

  @override
  String get camera => 'Kamera';

  @override
  String get error_loading_image => 'Fehler beim Laden des Bildes';

  @override
  String get unsupported_attachment_type => 'Nicht unterstützter Anhangstyp';

  @override
  String get file_not_found => 'Datei nicht gefunden';

  @override
  String get see_more => 'Mehr anzeigen';

  @override
  String get select_category => 'Kategorie auswählen';

  @override
  String get done => 'Fertig';

  @override
  String get search_activities_and_places => 'Aktivitäten und Orte suchen';

  @override
  String get no_results_found => 'Keine Ergebnisse gefunden';

  @override
  String get travel => 'Reisen';

  @override
  String get food_drink => 'Essen & Trinken';

  @override
  String get art_fun => 'Kunst & Spaß';

  @override
  String get search_results => 'Suchergebnisse';

  @override
  String get trip_photos => 'Reisefotos';

  @override
  String get error_loading_photos => 'Fehler beim Laden der Fotos';

  @override
  String get no_photos_for_trip => 'Keine Fotos für diese Reise';

  @override
  String get no_audio_for_trip => 'Keine Audioaufnahmen für diese Reise';

  @override
  String get no_documents_for_trip => 'Keine Dokumente für diese Reise';

  @override
  String get no_attachments_for_trip => 'Keine Anhänge für diese Reise';

  @override
  String get audio => 'Audio';

  @override
  String get documents => 'Dokumente';

  @override
  String get open => 'Öffnen';

  @override
  String get opening_file => 'Datei wird geöffnet';

  @override
  String get select_document => 'Dokument auswählen';

  @override
  String get error_opening_file => 'Fehler beim Öffnen der Datei';

  @override
  String get no_content_available => 'Kein Inhalt zum Anzeigen verfügbar';

  @override
  String get error_loading_document => 'Fehler beim Laden des Dokuments';

  @override
  String get size => 'Größe';

  @override
  String get modified => 'Geändert';

  @override
  String get word_document => 'Word-Dokumentinhalte können nicht direkt angezeigt werden';

  @override
  String get excel_spreadsheet => 'Excel-Tabelleninhalte können nicht direkt angezeigt werden';

  @override
  String get file_type_not_supported => 'Dieser Dateityp kann nicht direkt angezeigt werden';

  @override
  String get file_available_at => 'Die Datei ist verfügbar unter';

  @override
  String get attachments => 'Anhänge';

  @override
  String get add_attachment => 'Anhang hinzufügen';

  @override
  String get choose_attachment_type => 'Anhangstyp auswählen';

  @override
  String get voucher_code => 'Gutscheincode';

  @override
  String get enter_voucher_code => 'Gutscheincode eingeben';

  @override
  String get enter_voucher_code_hint => 'Gib deinen Gutscheincode ein';

  @override
  String get please_enter_voucher_code => 'Bitte gib einen Gutscheincode ein';

  @override
  String get voucher_code_applied => 'Gutscheincode erfolgreich angewendet!';

  @override
  String get invalid_voucher_code => 'Ungültiger Gutscheincode. Bitte versuche es erneut.';

  @override
  String get apply => 'Anwenden';

  @override
  String get night => 'Nacht';

  @override
  String get nights => 'Nächte';

  @override
  String get verify_email => 'E-Mail bestätigen';

  @override
  String get verification_code_sent => 'Ein Bestätigungscode wurde gesendet an';

  @override
  String get verification_code => 'Bestätigungscode';

  @override
  String get please_enter_verification_code => 'Bitte gib den Bestätigungscode ein';

  @override
  String get verification_code_too_short => 'Bestätigungscode ist zu kurz';

  @override
  String get verify => 'Bestätigen';

  @override
  String get resend_code => 'Code erneut senden';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'Bitte warte $seconds Sekunden, bevor du einen neuen Code anforderst';
  }

  @override
  String get please_enter_name => 'Bitte gib deinen Namen ein';

  @override
  String get please_enter_email => 'Bitte gib deine E-Mail-Adresse ein';

  @override
  String get please_enter_valid_email => 'Bitte gib eine gültige E-Mail-Adresse ein';

  @override
  String get verification_code_login => 'Wir senden einen Bestätigungscode an deine E-Mail';

  @override
  String get verification_code_register => 'Wir senden einen Bestätigungscode an deine E-Mail, um die Registrierung abzuschließen';

  @override
  String get continue_with_email => 'Mit E-Mail fortfahren';

  @override
  String get register_with_email => 'Mit E-Mail registrieren';

  @override
  String get error_invalid_email => 'Die E-Mail-Adresse ist nicht korrekt formatiert.';

  @override
  String get error_user_disabled => 'Dieses Benutzerkonto wurde deaktiviert.';

  @override
  String get error_user_not_found => 'Dieses Konto existiert nicht oder wurde gelöscht.';

  @override
  String get error_invalid_credentials => 'Falsche E-Mail oder Passwort angegeben.';

  @override
  String get error_email_exists => 'Die E-Mail-Adresse wird bereits von einem anderen Konto verwendet.';

  @override
  String get error_weak_password => 'Das Passwort ist zu schwach.';

  @override
  String get error_email_not_registered => 'Diese E-Mail ist nicht registriert. Bitte registrieren Sie sich zuerst.';

  @override
  String get error_otp_disabled => 'OTP-Verifizierung ist deaktiviert. Bitte versuchen Sie eine andere Methode.';

  @override
  String get error_unknown => 'Ein unbekannter Fehler ist aufgetreten. Bitte versuchen Sie es später erneut.';

  @override
  String get account_information => 'Kontoinformationen';

  @override
  String get account_actions => 'Kontoaktionen';

  @override
  String get enter_your_name => 'Gib deinen Namen ein';

  @override
  String get onboarding_welcome_title => 'Willkommen bei RoamR';

  @override
  String get onboarding_welcome_subtitle => 'Dein persönlicher Reisebegleiter';

  @override
  String get onboarding_welcome_button => 'Loslegen';

  @override
  String get onboarding_select_language => 'Sprache auswählen';

  @override
  String get onboarding_features_title => 'Funktionen';

  @override
  String get onboarding_feature_maps => 'Interaktive Karten';

  @override
  String get onboarding_feature_calendar => 'Reisekalender';

  @override
  String get onboarding_feature_social => 'Soziales Teilen';

  @override
  String get onboarding_feature_personalize => 'Personalisierte Erfahrung';

  @override
  String get onboarding_features_button => 'Weiter';

  @override
  String get onboarding_theme_title => 'Thema auswählen';

  @override
  String get onboarding_theme_subtitle => 'Wähle dein bevorzugtes Thema';

  @override
  String get onboarding_theme_button => 'Weiter';

  @override
  String get features_title => 'Planen Sie Ihre perfekte Reise';

  @override
  String get features_subtitle => 'Alles, was Sie zur Organisation Ihrer Reise benötigen';

  @override
  String get location_search => 'Intelligente Standortsuche';

  @override
  String get location_search_desc => 'Finden und fügen Sie Orte zu Ihrer Reiseroute hinzu';

  @override
  String get itinerary_map => 'Interaktive Reiseroutenkarte';

  @override
  String get itinerary_map_desc => 'Visualisieren Sie Ihre gesamte Reiseroute auf einer interaktiven Karte';

  @override
  String get expense_tracking => 'Ausgabenverfolgung';

  @override
  String get expense_tracking_desc => 'Verfolgen und kategorisieren Sie alle Ihre Reiseausgaben';

  @override
  String get travel_analytics => 'Reiseanalysen';

  @override
  String get travel_analytics_desc => 'Betrachten Sie Einblicke und Diagramme Ihrer Reisemuster';

  @override
  String get trip_attachments => 'Reiseanhänge';

  @override
  String get trip_attachments_desc => 'Fügen Sie Fotos, Dokumente und Erinnerungen zu Ihren Reisen hinzu';

  @override
  String get comprehensive_planning => 'Umfassende Planung';

  @override
  String get comprehensive_planning_desc => 'Planen Sie Flüge, Hotels, Aktivitäten und mehr mit detaillierten Optionen';

  @override
  String get no_attachments_added_yet => 'Noch keine Anhänge hinzugefügt';

  @override
  String get usage_left => 'übrig';

  @override
  String get usage_details_title => 'Nutzungsdetails';

  @override
  String get unlimited => 'Unbegrenzt';

  @override
  String get used => 'Verbraucht';

  @override
  String get left => 'übrig';

  @override
  String get premium_required_message => 'Sie haben das kostenlose Limit erreicht. Upgrade auf Premium, um weitere Reisen zu erstellen.';

  @override
  String get upgrade => 'Upgrade';

  @override
  String get itineraries => 'Reisepläne';

  @override
  String get notification_title_no_trips => 'Starte dein Abenteuer!';

  @override
  String get notification_body_no_trips => 'Erstelle deine erste Reise und beginne mit der Planung.';

  @override
  String get notification_title_no_itinerary => 'Plane deine Reiseroute!';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'Füge deiner Reise \"$tripName\" eine Reiseroute hinzu.';
  }

  @override
  String get notification_title_no_places => 'Füge Orte zum Besuchen hinzu!';

  @override
  String notification_body_no_places(Object tripName) {
    return 'Erweitere deine Reise \"$tripName\" um sehenswerte Orte.';
  }

  @override
  String get notification_title_keep_planning => 'Plane weiter deine Reise!';

  @override
  String notification_body_keep_planning(Object tripName) {
    return 'Baue deine perfekte Reiseroute für \"$tripName\" weiter aus.';
  }
}
