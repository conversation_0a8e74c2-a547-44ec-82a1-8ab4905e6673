{"@@locale": "en", "language": "Language", "language_desc": "Change your preferred language", "all": "All", "grocery": "Grocery", "food": "Food", "work": "Work", "entertainment": "Entertainment", "traveling": "Traveling", "other": "Other", "total_expenses": "Total Expenses", "select_catagory": "Select Category", "category": "Category", "your_trips": "Your Trips", "no_trips": "No trips added yet.", "add_trip": "Add Trip", "edit_trip": "Edit Trip", "title": "Title", "enter_title": "Enter trip name", "date_range": "When are you going?", "select_date_range": "Please select a date range", "country": "Country", "select_country": "Select a country", "country_hint": "Where are you going?", "city": "City", "city_hint": "Enter a city", "select_city": "Select a city", "failed_to_load_trips": "Failed to load trips", "past_trips": "Past Trips", "trip_name": "Trip Name", "trip_name_hint": "e.g. Japan Trip 2025", "amount": "Amount", "date": "Date", "departure_date": "Departure Date", "edit_itinerary": "Edit Itinerary", "add_itinerary": "Add Itinerary", "itinerary_details": "Itinerary Details", "location": "Location", "location_hint": "e.g. 123 Main St, City, Country", "pick_location": "Pick a location", "update_location": "Update location", "itinerary_name": "What is this visit about?", "itinerary_name_hint": "e.g. Day 1: Arrival and City Tour", "confirmDeletion": "Confirm Deletion", "confirmDeletionMessage": "Are you sure you want to delete this item?", "cancel": "Cancel", "delete": "Delete", "restaurant": "Restaurant", "accommodation": "Accommodation", "transportation": "Transportation", "sightseeing": "Sightseeing", "shopping": "Shopping", "activity": "Activity", "parking": "Parking", "note": "Note", "movie": "Movie", "flight": "Flight", "carRental": "Car Rental", "flight_number": "Flight Number", "flight_number_hint": "e.g. AA123", "airline_name": "Airline Name", "airline_name_hint": "e.g. American Airlines", "home": "Home", "trips": "Trips", "saved": "Bookmarks", "accommodations": "Accommodations", "pro": "Coming soon", "account": "Account", "settings": "Settings", "favorites": "Bookmarks", "unlock_premium": "RoamR Premium", "premium_subtitle": "Take your travel planning to the next level", "coming_soon": "Coming Soon", "ai_trip_planner": "AI Trip Planner", "ai_trip_planner_desc": "Get personalized trip suggestions and itineraries powered by AI", "trip_gallery": "Trip Gallery", "trip_gallery_desc": "Attach and organize photos, documents, and memories for each trip", "receipt_scanner": "Receipt Scanner", "receipt_scanner_desc": "Automatically track expenses by scanning your bills and receipts", "interactive_maps": "Interactive Maps", "interactive_maps_desc": "Visualize your entire trip route with interactive maps and navigation", "trip_sharing": "Trip Sharing", "trip_sharing_desc": "Collaborate and plan trips with friends and family", "guest_user": "Sign in to your account", "account_settings": "Account <PERSON><PERSON>", "edit_profile": "Edit Profile", "profile": "Profile", "profile_updated_successfully": "Profile updated successfully", "save": "Save", "sign_out_confirmation": "Are you sure you want to sign out?", "please_enter_your_name": "Please enter your name", "failed_to_load_profile": "Failed to load profile data", "notifications": "Notifications", "notifications_desc": "Manage your notification preferences", "preferences": "Preferences", "theme": "Theme", "light": "Light", "currency": "<PERSON><PERSON><PERSON><PERSON>", "currency_desc": "Set your preferred currency", "about": "About", "about_app": "About RoamR", "rate_app": "Rate App", "privacy_policy": "Privacy Policy", "sign_in": "Sign In", "sign_out": "Sign Out", "register": "Register", "create_account": "Create Account", "continue_with_google": "Continue with Google", "continue_with_apple": "Continue with Apple", "or": "OR", "email": "Email", "password": "Password", "confirm_password": "Confirm Password", "name": "Name", "dont_have_account": "Don't have an account?", "already_have_account": "Already have an account?", "signed_out_successfully": "Signed out successfully", "date_range_example": "e.g. 25 Jan - 16 Feb", "add_place": "Add places you want to visit", "restaurant_name": "Restaurant Name", "restaurant_name_hint": "e.g. Sushi Palace", "accommodation_name": "Accommodation Name", "accommodation_name_hint": "e.g. Hilton Hotel", "transportation_name": "Transportation Name", "transportation_name_hint": "e.g. Train to Tokyo", "sightseeing_name": "Place Name", "sightseeing_hint": "e.g. Tokyo Tower", "shopping_name": "Store Name", "shopping_hint": "e.g. Shibuya Mall", "activity_name": "Activity Name", "activity_hint": "e.g. Cooking Class", "parking_name": "Parking Location", "parking_hint": "e.g. Central Parking", "note_name": "Note Title", "note_hint": "e.g. Important Reminders", "movie_name": "Movie/Show Name", "movie_hint": "e.g. Local Theater Show", "flight_title": "Flight Details", "flight_title_hint": "e.g. Tokyo to Osaka Flight", "car_rental_name": "Car Rental Details", "car_rental_hint": "e.g. Toyota Camry - Hertz", "amount_optional": "Amount (if applicable)", "checkin_date": "Check-in Date", "checkout_date": "Checkout Date", "stay_dates": "Stay Dates", "select_checkin_checkout": "Select check-in and check-out dates", "start_date": "Start Date", "end_date": "End Date", "description": "Description", "description_hint": "Add any additional notes or details", "about_desc": "Learn more about the app", "system": "System", "edit": "Edit", "dark": "Dark", "date_required": "Date is required", "date_within_trip": "Date must be within trip dates", "no_favorites": "No bookmarks yet", "add_favorites_hint": "Your bookmarked itineraries will appear here", "premium_title": "RoamR Premium", "premium": "Premium", "premium_features": "Unlock Premium Features", "unlimited_trip_planning": "Unlimited Trip Planning", "unlimited_trip_planning_desc": "Create and manage unlimited trips", "interactive_map": "Interactive Trip Maps", "interactive_map_desc": "View your entire itinerary on interactive maps with navigation", "best_value": "BEST VALUE", "yearly": "Yearly", "monthly": "Monthly", "per_year": "per year", "per_month": "per month", "save_fifty": "Save 50%", "restore_purchases": "<PERSON><PERSON> Purchases", "subscription_details": "Subscription Details", "subscription_status": "Status:", "subscription_plan": "Plan:", "subscription_purchased": "Purchased:", "subscription_expires": "Expires:", "subscription_renews": "Renews:", "subscription_error": "Could not load subscription details", "loading_premium_options": "Loading premium options...", "premium_features_unlocked": "Premium features unlocked!", "premium_user_access": "You are a premium user with access to all features", "no_locations": "No itineraries with location data", "map_not_available": "Map Not Available", "add_location_details": "There are no locations to display on the map. Add location details to your itineraries first.", "ok": "OK", "location_permission_denied": "Location permission denied", "location_permission_permanently_denied": "Location permission permanently denied", "location_error": "Error getting location", "delete_account": "Delete Account", "delete_account_confirmation": "Delete Account?", "delete_account_message": "Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.", "account_deleted_successfully": "Account deleted successfully", "premium_login_message": "Sign in to access premium features", "share_app": "Share App", "terms_of_service": "Terms of Service", "send_feedback": "Send Feedback", "statistics": "Statistics", "select_trip": "Select Trip", "select_trip_to_view_statistics": "Select a trip to view statistics", "no_expenses_to_display": "No expenses to display", "expense_over_time": "Expense Over Time", "expenses_by_category": "Expenses By Category", "total": "Total", "category_distribution": "Category Distribution", "remove_from_favorites": "Remove from Bookmarks", "remove": "Remove", "remove_from_favorites_confirmation": "Remove from Bookmarks?", "remove_from_favorites_message": "Are you sure you want to remove this itinerary from your bookmarks?", "pie_chart": "Pie Chart", "bar_chart": "Bar Chart", "trip_summary": "Travel Summary", "total_trips": "Total Trips", "total_places_visited": "Places Visited", "avg_places_per_trip": "Avg Places/Trip", "total_days_traveled": "Days Traveled", "shortest_trip": "Shortest Trip", "longest_trip": "Longest Trip", "day": "day", "days": "days", "flights": "Flights", "most_frequent": "Most Frequent Activity", "most_activities_day": "Most Activities/Day", "avg_activities_day": "Avg Activities/Day", "avg_spend_per_trip": "Avg Spend/Trip", "most_expensive_trip": "Most Expensive Trip", "least_expensive_trip": "Least Expensive Trip", "top_expense_category": "Most Expensive Category", "achievements": "Achievements", "achievements_description": "Track your travel milestones and unlock achievements as you explore the world!", "destination_achievements": "Destination Achievements", "expense_achievements": "Expense Achievements", "frequency_achievements": "Trip Frequency Achievements", "duration_achievements": "Trip Duration Achievements", "flight_achievements": "Flight Achievements", "novice_traveller_title": "<PERSON><PERSON> Traveller", "novice_traveller_desc": "Visit 3 or more different countries", "world_traveller_title": "World Traveller", "world_traveller_desc": "Visit 10 or more different countries", "globetrotter_title": "Globetrotter", "globetrotter_desc": "Visit 20 or more different countries", "continental_explorer_title": "Continental Explorer", "continental_explorer_desc": "Visit 2 or more continents and at least 20 countries", "continental_collector_title": "Continental Collector", "continental_collector_desc": "Visit 4 or more continents and at least 25 countries", "world_conqueror_title": "World Conqueror", "world_conqueror_desc": "Visit all 7 continents and at least 30 countries", "budget_tracker_title": "Budget Tracker", "budget_tracker_desc": "Track $1,000+ in total expenses", "expense_manager_title": "Expense Manager", "expense_manager_desc": "Track $5,000+ in total expenses", "financial_voyager_title": "Financial Voyager", "financial_voyager_desc": "Track $10,000+ in total expenses", "luxury_traveller_title": "<PERSON><PERSON><PERSON> Traveller", "luxury_traveller_desc": "Track $20,000+ in total expenses", "travel_beginner_title": "Travel Beginner", "travel_beginner_desc": "Complete 3 or more trips", "travel_enthusiast_title": "Travel Enthusiast", "travel_enthusiast_desc": "Complete 10 or more trips", "travel_addict_title": "Travel Addict", "travel_addict_desc": "Complete 20 or more trips", "day_tripper_title": "Day Tripper", "day_tripper_desc": "Complete a trip of at least 1 day", "weekend_wanderer_title": "<PERSON> Wanderer", "weekend_wanderer_desc": "Complete a trip of at least 3 days", "vacation_voyager_title": "Vacation Voyager", "vacation_voyager_desc": "Complete a trip of at least 7 days", "extended_explorer_title": "Extended Explorer", "extended_explorer_desc": "Complete a trip of at least 14 days", "long_term_traveler_title": "Long-term Traveler", "long_term_traveler_desc": "Complete a trip of at least 30 days", "nomadic_adventurer_title": "Nomadic Adventurer", "nomadic_adventurer_desc": "Complete a trip of at least 60 days", "first_flight_title": "Flight Beginner", "first_flight_desc": "Record 5 or more flights", "frequent_flyer_title": "Frequent Flyer", "frequent_flyer_desc": "Record 15 or more flights", "aviation_enthusiast_title": "Aviation Enthusiast", "aviation_enthusiast_desc": "Record 30 or more flights", "progress": "Progress", "close": "Close", "complete_trips_for_achievements": "Complete trips to unlock achievements!", "view_all": "View All", "app_tagline": "Your travel companion", "website": "Website", "facebook": "Facebook", "twitter": "Twitter/X", "instagram": "Instagram", "feedback_email_subject": "RoamR Feedback", "feedback_email_body": "I would like to provide feedback about RoamR:", "share_app_message_prefix": "Check out RoamR, a travel planning app: ", "share_app_subject": "RoamR - Your travel companion", "could_not_launch_url": "Could not launch {url}", "could_not_share_app": "Could not share app", "could_not_launch_email": "Could not launch email client", "copyright": "© {year} RoamR", "photos": "Photos", "no_photos_added": "No photos added yet", "gallery": "Gallery", "camera": "Camera", "error_loading_image": "Error loading image", "unsupported_attachment_type": "Unsupported attachment type", "file_not_found": "File not found", "see_more": "See More", "select_category": "Select Category", "done": "Done", "search_activities_and_places": "Search activities and places", "no_results_found": "No results found", "travel": "Travel", "food_drink": "Food & Drink", "art_fun": "Art & Fun", "search_results": "Search Results", "trip_photos": "Trip Photos", "error_loading_photos": "Error loading photos", "no_photos_for_trip": "No photos for this trip", "no_audio_for_trip": "No audio recordings for this trip", "no_documents_for_trip": "No documents for this trip", "no_attachments_for_trip": "No attachments for this trip", "audio": "Audio", "documents": "Documents", "open": "Open", "opening_file": "Opening file", "select_document": "Select Document", "error_opening_file": "Error opening file", "no_content_available": "No content available to display", "error_loading_document": "Error loading document", "size": "Size", "modified": "Modified", "word_document": "Word document content cannot be displayed directly", "excel_spreadsheet": "Excel spreadsheet content cannot be displayed directly", "file_type_not_supported": "This file type cannot be displayed directly", "file_available_at": "The file is available at", "attachments": "Attachments", "add_attachment": "Add Attachment", "choose_attachment_type": "Choose Attachment Type", "voucher_code": "Voucher Code", "enter_voucher_code": "Enter Voucher Code", "enter_voucher_code_hint": "Enter your voucher code", "please_enter_voucher_code": "Please enter a voucher code", "voucher_code_applied": "Voucher code applied successfully!", "invalid_voucher_code": "Invalid voucher code. Please try again.", "apply": "Apply", "night": "night", "nights": "nights", "verify_email": "<PERSON><PERSON><PERSON>", "verification_code_sent": "A verification code has been sent to", "verification_code": "Verification Code", "please_enter_verification_code": "Please enter the verification code", "verification_code_too_short": "Verification code is too short", "verify": "Verify", "resend_code": "Resend Code", "please_wait_before_resend": "Please wait {seconds} seconds before requesting a new code", "please_enter_name": "Please enter your name", "please_enter_email": "Please enter your email", "please_enter_valid_email": "Please enter a valid email address", "verification_code_login": "We will send a verification code to your email", "verification_code_register": "We will send a verification code to your email to complete registration", "continue_with_email": "Continue with <PERSON>ail", "register_with_email": "Register with Email", "error_invalid_email": "The email address is badly formatted.", "error_user_disabled": "This user account has been disabled.", "error_user_not_found": "This account does not exist or has been deleted.", "error_invalid_credentials": "Wrong email or password provided.", "error_email_exists": "The email address is already in use by another account.", "error_weak_password": "The password is too weak.", "error_email_not_registered": "This email is not registered. Please sign up first.", "error_otp_disabled": "OTP verification is disabled. Please try another method.", "error_unknown": "An unknown error occurred. Please try again later.", "account_information": "Account Information", "account_actions": "Account Actions", "enter_your_name": "Enter your name", "onboarding_welcome_title": "Welcome to RoamR", "onboarding_welcome_subtitle": "Your personal travel companion", "onboarding_welcome_button": "Get Started", "onboarding_select_language": "Select Language", "onboarding_features_title": "Features", "onboarding_feature_maps": "Interactive Maps", "onboarding_feature_calendar": "Trip Calendar", "onboarding_feature_social": "Social Sharing", "onboarding_feature_personalize": "Personalized Experience", "onboarding_features_button": "Next", "onboarding_theme_title": "Choose <PERSON>", "onboarding_theme_subtitle": "Select your preferred theme", "onboarding_theme_button": "Next", "features_title": "Plan Your Perfect Trip", "features_subtitle": "Everything you need to organize your travel", "location_search": "Smart Location Search", "location_search_desc": "Find and add places to your itinerary with ease", "itinerary_map": "Interactive Itinerary Map", "itinerary_map_desc": "Visualize your entire trip route on an interactive map", "expense_tracking": "Expense Tracking", "expense_tracking_desc": "Track and categorize all your travel expenses", "travel_analytics": "Travel Analytics", "travel_analytics_desc": "View insights and charts of your travel patterns", "trip_attachments": "Trip Attachments", "trip_attachments_desc": "Attach photos, documents, and memories to your trips", "comprehensive_planning": "Comprehensive Planning", "comprehensive_planning_desc": "Plan flights, hotels, activities, and more with detailed options", "no_attachments_added_yet": "No attachments added yet", "usage_left": "left", "usage_details_title": "Usage Details", "unlimited": "Unlimited", "used": "Used", "left": "left", "premium_required_message": "You have reached the free limit. Upgrade to premium to create more trips.", "upgrade": "Upgrade", "itineraries": "Itineraries", "notification_title_no_trips": "Start your adventure!", "notification_body_no_trips": "Create your first trip and start planning your journey.", "notification_title_no_itinerary": "Plan your itinerary!", "notification_body_no_itinerary": "Add an itinerary to your trip \"{tripName}\".", "notification_title_no_places": "Add places to visit!", "notification_body_no_places": "Enhance your trip \"{tripName}\" by adding places to visit.", "notification_title_keep_planning": "Keep planning your trip!", "notification_body_keep_planning": "Continue building your perfect itinerary for \"{tripName}\".", "ai_chat_title": "AI Chat", "ai_chat_input_hint": "Ask me about your travel plans...", "ai_chat_loading": "RoamR AI is thinking...", "ai_chat_welcome": "Hi! I'm RoamR AI, your travel assistant. How can I help you plan your next adventure?"}