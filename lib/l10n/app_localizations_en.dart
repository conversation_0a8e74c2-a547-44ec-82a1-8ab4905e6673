// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get language => 'Language';

  @override
  String get language_desc => 'Change your preferred language';

  @override
  String get all => 'All';

  @override
  String get grocery => 'Grocery';

  @override
  String get food => 'Food';

  @override
  String get work => 'Work';

  @override
  String get entertainment => 'Entertainment';

  @override
  String get traveling => 'Traveling';

  @override
  String get other => 'Other';

  @override
  String get total_expenses => 'Total Expenses';

  @override
  String get select_catagory => 'Select Category';

  @override
  String get category => 'Category';

  @override
  String get your_trips => 'Your Trips';

  @override
  String get no_trips => 'No trips added yet.';

  @override
  String get add_trip => 'Add Trip';

  @override
  String get edit_trip => 'Edit Trip';

  @override
  String get title => 'Title';

  @override
  String get enter_title => 'Enter trip name';

  @override
  String get date_range => 'When are you going?';

  @override
  String get select_date_range => 'Please select a date range';

  @override
  String get country => 'Country';

  @override
  String get select_country => 'Select a country';

  @override
  String get country_hint => 'Where are you going?';

  @override
  String get city => 'City';

  @override
  String get city_hint => 'Enter a city';

  @override
  String get select_city => 'Select a city';

  @override
  String get failed_to_load_trips => 'Failed to load trips';

  @override
  String get past_trips => 'Past Trips';

  @override
  String get trip_name => 'Trip Name';

  @override
  String get trip_name_hint => 'e.g. Japan Trip 2025';

  @override
  String get amount => 'Amount';

  @override
  String get date => 'Date';

  @override
  String get departure_date => 'Departure Date';

  @override
  String get edit_itinerary => 'Edit Itinerary';

  @override
  String get add_itinerary => 'Add Itinerary';

  @override
  String get itinerary_details => 'Itinerary Details';

  @override
  String get location => 'Location';

  @override
  String get location_hint => 'e.g. 123 Main St, City, Country';

  @override
  String get pick_location => 'Pick a location';

  @override
  String get update_location => 'Update location';

  @override
  String get itinerary_name => 'What is this visit about?';

  @override
  String get itinerary_name_hint => 'e.g. Day 1: Arrival and City Tour';

  @override
  String get confirmDeletion => 'Confirm Deletion';

  @override
  String get confirmDeletionMessage => 'Are you sure you want to delete this item?';

  @override
  String get cancel => 'Cancel';

  @override
  String get delete => 'Delete';

  @override
  String get restaurant => 'Restaurant';

  @override
  String get accommodation => 'Accommodation';

  @override
  String get transportation => 'Transportation';

  @override
  String get sightseeing => 'Sightseeing';

  @override
  String get shopping => 'Shopping';

  @override
  String get activity => 'Activity';

  @override
  String get parking => 'Parking';

  @override
  String get note => 'Note';

  @override
  String get movie => 'Movie';

  @override
  String get flight => 'Flight';

  @override
  String get carRental => 'Car Rental';

  @override
  String get flight_number => 'Flight Number';

  @override
  String get flight_number_hint => 'e.g. AA123';

  @override
  String get airline_name => 'Airline Name';

  @override
  String get airline_name_hint => 'e.g. American Airlines';

  @override
  String get home => 'Home';

  @override
  String get trips => 'Trips';

  @override
  String get saved => 'Bookmarks';

  @override
  String get accommodations => 'Accommodations';

  @override
  String get pro => 'Coming soon';

  @override
  String get account => 'Account';

  @override
  String get settings => 'Settings';

  @override
  String get favorites => 'Bookmarks';

  @override
  String get unlock_premium => 'RoamR Premium';

  @override
  String get premium_subtitle => 'Take your travel planning to the next level';

  @override
  String get coming_soon => 'Coming Soon';

  @override
  String get ai_trip_planner => 'AI Trip Planner';

  @override
  String get ai_trip_planner_desc => 'Get personalized trip suggestions and itineraries powered by AI';

  @override
  String get trip_gallery => 'Trip Gallery';

  @override
  String get trip_gallery_desc => 'Attach and organize photos, documents, and memories for each trip';

  @override
  String get receipt_scanner => 'Receipt Scanner';

  @override
  String get receipt_scanner_desc => 'Automatically track expenses by scanning your bills and receipts';

  @override
  String get interactive_maps => 'Interactive Maps';

  @override
  String get interactive_maps_desc => 'Visualize your entire trip route with interactive maps and navigation';

  @override
  String get trip_sharing => 'Trip Sharing';

  @override
  String get trip_sharing_desc => 'Collaborate and plan trips with friends and family';

  @override
  String get guest_user => 'Sign in to your account';

  @override
  String get account_settings => 'Account Settings';

  @override
  String get edit_profile => 'Edit Profile';

  @override
  String get profile => 'Profile';

  @override
  String get profile_updated_successfully => 'Profile updated successfully';

  @override
  String get save => 'Save';

  @override
  String get sign_out_confirmation => 'Are you sure you want to sign out?';

  @override
  String get please_enter_your_name => 'Please enter your name';

  @override
  String get failed_to_load_profile => 'Failed to load profile data';

  @override
  String get notifications => 'Notifications';

  @override
  String get notifications_desc => 'Manage your notification preferences';

  @override
  String get preferences => 'Preferences';

  @override
  String get theme => 'Theme';

  @override
  String get light => 'Light';

  @override
  String get currency => 'Currency';

  @override
  String get currency_desc => 'Set your preferred currency';

  @override
  String get about => 'About';

  @override
  String get about_app => 'About RoamR';

  @override
  String get rate_app => 'Rate App';

  @override
  String get privacy_policy => 'Privacy Policy';

  @override
  String get sign_in => 'Sign In';

  @override
  String get sign_out => 'Sign Out';

  @override
  String get register => 'Register';

  @override
  String get create_account => 'Create Account';

  @override
  String get continue_with_google => 'Continue with Google';

  @override
  String get continue_with_apple => 'Continue with Apple';

  @override
  String get or => 'OR';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirm_password => 'Confirm Password';

  @override
  String get name => 'Name';

  @override
  String get dont_have_account => 'Don\'t have an account?';

  @override
  String get already_have_account => 'Already have an account?';

  @override
  String get signed_out_successfully => 'Signed out successfully';

  @override
  String get date_range_example => 'e.g. 25 Jan - 16 Feb';

  @override
  String get add_place => 'Add places you want to visit';

  @override
  String get restaurant_name => 'Restaurant Name';

  @override
  String get restaurant_name_hint => 'e.g. Sushi Palace';

  @override
  String get accommodation_name => 'Accommodation Name';

  @override
  String get accommodation_name_hint => 'e.g. Hilton Hotel';

  @override
  String get transportation_name => 'Transportation Name';

  @override
  String get transportation_name_hint => 'e.g. Train to Tokyo';

  @override
  String get sightseeing_name => 'Place Name';

  @override
  String get sightseeing_hint => 'e.g. Tokyo Tower';

  @override
  String get shopping_name => 'Store Name';

  @override
  String get shopping_hint => 'e.g. Shibuya Mall';

  @override
  String get activity_name => 'Activity Name';

  @override
  String get activity_hint => 'e.g. Cooking Class';

  @override
  String get parking_name => 'Parking Location';

  @override
  String get parking_hint => 'e.g. Central Parking';

  @override
  String get note_name => 'Note Title';

  @override
  String get note_hint => 'e.g. Important Reminders';

  @override
  String get movie_name => 'Movie/Show Name';

  @override
  String get movie_hint => 'e.g. Local Theater Show';

  @override
  String get flight_title => 'Flight Details';

  @override
  String get flight_title_hint => 'e.g. Tokyo to Osaka Flight';

  @override
  String get car_rental_name => 'Car Rental Details';

  @override
  String get car_rental_hint => 'e.g. Toyota Camry - Hertz';

  @override
  String get amount_optional => 'Amount (if applicable)';

  @override
  String get checkin_date => 'Check-in Date';

  @override
  String get checkout_date => 'Checkout Date';

  @override
  String get stay_dates => 'Stay Dates';

  @override
  String get select_checkin_checkout => 'Select check-in and check-out dates';

  @override
  String get start_date => 'Start Date';

  @override
  String get end_date => 'End Date';

  @override
  String get description => 'Description';

  @override
  String get description_hint => 'Add any additional notes or details';

  @override
  String get about_desc => 'Learn more about the app';

  @override
  String get system => 'System';

  @override
  String get edit => 'Edit';

  @override
  String get dark => 'Dark';

  @override
  String get date_required => 'Date is required';

  @override
  String get date_within_trip => 'Date must be within trip dates';

  @override
  String get no_favorites => 'No bookmarks yet';

  @override
  String get add_favorites_hint => 'Your bookmarked itineraries will appear here';

  @override
  String get premium_title => 'RoamR Premium';

  @override
  String get premium => 'Premium';

  @override
  String get premium_features => 'Unlock Premium Features';

  @override
  String get unlimited_trip_planning => 'Unlimited Trip Planning';

  @override
  String get unlimited_trip_planning_desc => 'Create and manage unlimited trips';

  @override
  String get interactive_map => 'Interactive Trip Maps';

  @override
  String get interactive_map_desc => 'View your entire itinerary on interactive maps with navigation';

  @override
  String get best_value => 'BEST VALUE';

  @override
  String get yearly => 'Yearly';

  @override
  String get monthly => 'Monthly';

  @override
  String get per_year => 'per year';

  @override
  String get per_month => 'per month';

  @override
  String get save_fifty => 'Save 50%';

  @override
  String get restore_purchases => 'Restore Purchases';

  @override
  String get subscription_details => 'Subscription Details';

  @override
  String get subscription_status => 'Status:';

  @override
  String get subscription_plan => 'Plan:';

  @override
  String get subscription_purchased => 'Purchased:';

  @override
  String get subscription_expires => 'Expires:';

  @override
  String get subscription_renews => 'Renews:';

  @override
  String get subscription_error => 'Could not load subscription details';

  @override
  String get loading_premium_options => 'Loading premium options...';

  @override
  String get premium_features_unlocked => 'Premium features unlocked!';

  @override
  String get premium_user_access => 'You are a premium user with access to all features';

  @override
  String get no_locations => 'No itineraries with location data';

  @override
  String get map_not_available => 'Map Not Available';

  @override
  String get add_location_details => 'There are no locations to display on the map. Add location details to your itineraries first.';

  @override
  String get ok => 'OK';

  @override
  String get location_permission_denied => 'Location permission denied';

  @override
  String get location_permission_permanently_denied => 'Location permission permanently denied';

  @override
  String get location_error => 'Error getting location';

  @override
  String get delete_account => 'Delete Account';

  @override
  String get delete_account_confirmation => 'Delete Account?';

  @override
  String get delete_account_message => 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently deleted.';

  @override
  String get account_deleted_successfully => 'Account deleted successfully';

  @override
  String get premium_login_message => 'Sign in to access premium features';

  @override
  String get share_app => 'Share App';

  @override
  String get terms_of_service => 'Terms of Service';

  @override
  String get send_feedback => 'Send Feedback';

  @override
  String get statistics => 'Statistics';

  @override
  String get select_trip => 'Select Trip';

  @override
  String get select_trip_to_view_statistics => 'Select a trip to view statistics';

  @override
  String get no_expenses_to_display => 'No expenses to display';

  @override
  String get expense_over_time => 'Expense Over Time';

  @override
  String get expenses_by_category => 'Expenses By Category';

  @override
  String get total => 'Total';

  @override
  String get category_distribution => 'Category Distribution';

  @override
  String get remove_from_favorites => 'Remove from Bookmarks';

  @override
  String get remove => 'Remove';

  @override
  String get remove_from_favorites_confirmation => 'Remove from Bookmarks?';

  @override
  String get remove_from_favorites_message => 'Are you sure you want to remove this itinerary from your bookmarks?';

  @override
  String get pie_chart => 'Pie Chart';

  @override
  String get bar_chart => 'Bar Chart';

  @override
  String get trip_summary => 'Travel Summary';

  @override
  String get total_trips => 'Total Trips';

  @override
  String get total_places_visited => 'Places Visited';

  @override
  String get avg_places_per_trip => 'Avg Places/Trip';

  @override
  String get total_days_traveled => 'Days Traveled';

  @override
  String get shortest_trip => 'Shortest Trip';

  @override
  String get longest_trip => 'Longest Trip';

  @override
  String get day => 'day';

  @override
  String get days => 'days';

  @override
  String get flights => 'Flights';

  @override
  String get most_frequent => 'Most Frequent Activity';

  @override
  String get most_activities_day => 'Most Activities/Day';

  @override
  String get avg_activities_day => 'Avg Activities/Day';

  @override
  String get avg_spend_per_trip => 'Avg Spend/Trip';

  @override
  String get most_expensive_trip => 'Most Expensive Trip';

  @override
  String get least_expensive_trip => 'Least Expensive Trip';

  @override
  String get top_expense_category => 'Most Expensive Category';

  @override
  String get achievements => 'Achievements';

  @override
  String get achievements_description => 'Track your travel milestones and unlock achievements as you explore the world!';

  @override
  String get destination_achievements => 'Destination Achievements';

  @override
  String get expense_achievements => 'Expense Achievements';

  @override
  String get frequency_achievements => 'Trip Frequency Achievements';

  @override
  String get duration_achievements => 'Trip Duration Achievements';

  @override
  String get flight_achievements => 'Flight Achievements';

  @override
  String get novice_traveller_title => 'Novice Traveller';

  @override
  String get novice_traveller_desc => 'Visit 3 or more different countries';

  @override
  String get world_traveller_title => 'World Traveller';

  @override
  String get world_traveller_desc => 'Visit 10 or more different countries';

  @override
  String get globetrotter_title => 'Globetrotter';

  @override
  String get globetrotter_desc => 'Visit 20 or more different countries';

  @override
  String get continental_explorer_title => 'Continental Explorer';

  @override
  String get continental_explorer_desc => 'Visit 2 or more continents and at least 20 countries';

  @override
  String get continental_collector_title => 'Continental Collector';

  @override
  String get continental_collector_desc => 'Visit 4 or more continents and at least 25 countries';

  @override
  String get world_conqueror_title => 'World Conqueror';

  @override
  String get world_conqueror_desc => 'Visit all 7 continents and at least 30 countries';

  @override
  String get budget_tracker_title => 'Budget Tracker';

  @override
  String get budget_tracker_desc => 'Track \$1,000+ in total expenses';

  @override
  String get expense_manager_title => 'Expense Manager';

  @override
  String get expense_manager_desc => 'Track \$5,000+ in total expenses';

  @override
  String get financial_voyager_title => 'Financial Voyager';

  @override
  String get financial_voyager_desc => 'Track \$10,000+ in total expenses';

  @override
  String get luxury_traveller_title => 'Luxury Traveller';

  @override
  String get luxury_traveller_desc => 'Track \$20,000+ in total expenses';

  @override
  String get travel_beginner_title => 'Travel Beginner';

  @override
  String get travel_beginner_desc => 'Complete 3 or more trips';

  @override
  String get travel_enthusiast_title => 'Travel Enthusiast';

  @override
  String get travel_enthusiast_desc => 'Complete 10 or more trips';

  @override
  String get travel_addict_title => 'Travel Addict';

  @override
  String get travel_addict_desc => 'Complete 20 or more trips';

  @override
  String get day_tripper_title => 'Day Tripper';

  @override
  String get day_tripper_desc => 'Complete a trip of at least 1 day';

  @override
  String get weekend_wanderer_title => 'Weekend Wanderer';

  @override
  String get weekend_wanderer_desc => 'Complete a trip of at least 3 days';

  @override
  String get vacation_voyager_title => 'Vacation Voyager';

  @override
  String get vacation_voyager_desc => 'Complete a trip of at least 7 days';

  @override
  String get extended_explorer_title => 'Extended Explorer';

  @override
  String get extended_explorer_desc => 'Complete a trip of at least 14 days';

  @override
  String get long_term_traveler_title => 'Long-term Traveler';

  @override
  String get long_term_traveler_desc => 'Complete a trip of at least 30 days';

  @override
  String get nomadic_adventurer_title => 'Nomadic Adventurer';

  @override
  String get nomadic_adventurer_desc => 'Complete a trip of at least 60 days';

  @override
  String get first_flight_title => 'Flight Beginner';

  @override
  String get first_flight_desc => 'Record 5 or more flights';

  @override
  String get frequent_flyer_title => 'Frequent Flyer';

  @override
  String get frequent_flyer_desc => 'Record 15 or more flights';

  @override
  String get aviation_enthusiast_title => 'Aviation Enthusiast';

  @override
  String get aviation_enthusiast_desc => 'Record 30 or more flights';

  @override
  String get progress => 'Progress';

  @override
  String get close => 'Close';

  @override
  String get complete_trips_for_achievements => 'Complete trips to unlock achievements!';

  @override
  String get view_all => 'View All';

  @override
  String get app_tagline => 'Your travel companion';

  @override
  String get website => 'Website';

  @override
  String get facebook => 'Facebook';

  @override
  String get twitter => 'Twitter/X';

  @override
  String get instagram => 'Instagram';

  @override
  String get feedback_email_subject => 'RoamR Feedback';

  @override
  String get feedback_email_body => 'I would like to provide feedback about RoamR:';

  @override
  String get share_app_message_prefix => 'Check out RoamR, a travel planning app: ';

  @override
  String get share_app_subject => 'RoamR - Your travel companion';

  @override
  String could_not_launch_url(Object url) {
    return 'Could not launch $url';
  }

  @override
  String get could_not_share_app => 'Could not share app';

  @override
  String get could_not_launch_email => 'Could not launch email client';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'Photos';

  @override
  String get no_photos_added => 'No photos added yet';

  @override
  String get gallery => 'Gallery';

  @override
  String get camera => 'Camera';

  @override
  String get error_loading_image => 'Error loading image';

  @override
  String get unsupported_attachment_type => 'Unsupported attachment type';

  @override
  String get file_not_found => 'File not found';

  @override
  String get see_more => 'See More';

  @override
  String get select_category => 'Select Category';

  @override
  String get done => 'Done';

  @override
  String get search_activities_and_places => 'Search activities and places';

  @override
  String get no_results_found => 'No results found';

  @override
  String get travel => 'Travel';

  @override
  String get food_drink => 'Food & Drink';

  @override
  String get art_fun => 'Art & Fun';

  @override
  String get search_results => 'Search Results';

  @override
  String get trip_photos => 'Trip Photos';

  @override
  String get error_loading_photos => 'Error loading photos';

  @override
  String get no_photos_for_trip => 'No photos for this trip';

  @override
  String get no_audio_for_trip => 'No audio recordings for this trip';

  @override
  String get no_documents_for_trip => 'No documents for this trip';

  @override
  String get no_attachments_for_trip => 'No attachments for this trip';

  @override
  String get audio => 'Audio';

  @override
  String get documents => 'Documents';

  @override
  String get open => 'Open';

  @override
  String get opening_file => 'Opening file';

  @override
  String get select_document => 'Select Document';

  @override
  String get error_opening_file => 'Error opening file';

  @override
  String get no_content_available => 'No content available to display';

  @override
  String get error_loading_document => 'Error loading document';

  @override
  String get size => 'Size';

  @override
  String get modified => 'Modified';

  @override
  String get word_document => 'Word document content cannot be displayed directly';

  @override
  String get excel_spreadsheet => 'Excel spreadsheet content cannot be displayed directly';

  @override
  String get file_type_not_supported => 'This file type cannot be displayed directly';

  @override
  String get file_available_at => 'The file is available at';

  @override
  String get attachments => 'Attachments';

  @override
  String get add_attachment => 'Add Attachment';

  @override
  String get choose_attachment_type => 'Choose Attachment Type';

  @override
  String get voucher_code => 'Voucher Code';

  @override
  String get enter_voucher_code => 'Enter Voucher Code';

  @override
  String get enter_voucher_code_hint => 'Enter your voucher code';

  @override
  String get please_enter_voucher_code => 'Please enter a voucher code';

  @override
  String get voucher_code_applied => 'Voucher code applied successfully!';

  @override
  String get invalid_voucher_code => 'Invalid voucher code. Please try again.';

  @override
  String get apply => 'Apply';

  @override
  String get night => 'night';

  @override
  String get nights => 'nights';

  @override
  String get verify_email => 'Verify Email';

  @override
  String get verification_code_sent => 'A verification code has been sent to';

  @override
  String get verification_code => 'Verification Code';

  @override
  String get please_enter_verification_code => 'Please enter the verification code';

  @override
  String get verification_code_too_short => 'Verification code is too short';

  @override
  String get verify => 'Verify';

  @override
  String get resend_code => 'Resend Code';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'Please wait $seconds seconds before requesting a new code';
  }

  @override
  String get please_enter_name => 'Please enter your name';

  @override
  String get please_enter_email => 'Please enter your email';

  @override
  String get please_enter_valid_email => 'Please enter a valid email address';

  @override
  String get verification_code_login => 'We will send a verification code to your email';

  @override
  String get verification_code_register => 'We will send a verification code to your email to complete registration';

  @override
  String get continue_with_email => 'Continue with Email';

  @override
  String get register_with_email => 'Register with Email';

  @override
  String get error_invalid_email => 'The email address is badly formatted.';

  @override
  String get error_user_disabled => 'This user account has been disabled.';

  @override
  String get error_user_not_found => 'This account does not exist or has been deleted.';

  @override
  String get error_invalid_credentials => 'Wrong email or password provided.';

  @override
  String get error_email_exists => 'The email address is already in use by another account.';

  @override
  String get error_weak_password => 'The password is too weak.';

  @override
  String get error_email_not_registered => 'This email is not registered. Please sign up first.';

  @override
  String get error_otp_disabled => 'OTP verification is disabled. Please try another method.';

  @override
  String get error_unknown => 'An unknown error occurred. Please try again later.';

  @override
  String get account_information => 'Account Information';

  @override
  String get account_actions => 'Account Actions';

  @override
  String get enter_your_name => 'Enter your name';

  @override
  String get onboarding_welcome_title => 'Welcome to RoamR';

  @override
  String get onboarding_welcome_subtitle => 'Your personal travel companion';

  @override
  String get onboarding_welcome_button => 'Get Started';

  @override
  String get onboarding_select_language => 'Select Language';

  @override
  String get onboarding_features_title => 'Features';

  @override
  String get onboarding_feature_maps => 'Interactive Maps';

  @override
  String get onboarding_feature_calendar => 'Trip Calendar';

  @override
  String get onboarding_feature_social => 'Social Sharing';

  @override
  String get onboarding_feature_personalize => 'Personalized Experience';

  @override
  String get onboarding_features_button => 'Next';

  @override
  String get onboarding_theme_title => 'Choose Theme';

  @override
  String get onboarding_theme_subtitle => 'Select your preferred theme';

  @override
  String get onboarding_theme_button => 'Next';

  @override
  String get features_title => 'Plan Your Perfect Trip';

  @override
  String get features_subtitle => 'Everything you need to organize your travel';

  @override
  String get location_search => 'Smart Location Search';

  @override
  String get location_search_desc => 'Find and add places to your itinerary with ease';

  @override
  String get itinerary_map => 'Interactive Itinerary Map';

  @override
  String get itinerary_map_desc => 'Visualize your entire trip route on an interactive map';

  @override
  String get expense_tracking => 'Expense Tracking';

  @override
  String get expense_tracking_desc => 'Track and categorize all your travel expenses';

  @override
  String get travel_analytics => 'Travel Analytics';

  @override
  String get travel_analytics_desc => 'View insights and charts of your travel patterns';

  @override
  String get trip_attachments => 'Trip Attachments';

  @override
  String get trip_attachments_desc => 'Attach photos, documents, and memories to your trips';

  @override
  String get comprehensive_planning => 'Comprehensive Planning';

  @override
  String get comprehensive_planning_desc => 'Plan flights, hotels, activities, and more with detailed options';

  @override
  String get no_attachments_added_yet => 'No attachments added yet';

  @override
  String get usage_left => 'left';

  @override
  String get usage_details_title => 'Usage Details';

  @override
  String get unlimited => 'Unlimited';

  @override
  String get used => 'Used';

  @override
  String get left => 'left';

  @override
  String get premium_required_message => 'You have reached the free limit. Upgrade to premium to create more trips.';

  @override
  String get upgrade => 'Upgrade';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get notification_title_no_trips => 'Start your adventure!';

  @override
  String get notification_body_no_trips => 'Create your first trip and start planning your journey.';

  @override
  String get notification_title_no_itinerary => 'Plan your itinerary!';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'Add an itinerary to your trip \"$tripName\".';
  }

  @override
  String get notification_title_no_places => 'Add places to visit!';

  @override
  String notification_body_no_places(Object tripName) {
    return 'Enhance your trip \"$tripName\" by adding places to visit.';
  }

  @override
  String get notification_title_keep_planning => 'Keep planning your trip!';

  @override
  String notification_body_keep_planning(Object tripName) {
    return 'Continue building your perfect itinerary for \"$tripName\".';
  }

  @override
  String get ai_chat_title => 'AI Chat';

  @override
  String get ai_chat_input_hint => 'Ask me about your travel plans...';

  @override
  String get ai_chat_loading => 'RoamR AI is thinking...';

  @override
  String get ai_chat_welcome => 'Hi! I\'m RoamR AI, your travel assistant. How can I help you plan your next adventure?';
}
