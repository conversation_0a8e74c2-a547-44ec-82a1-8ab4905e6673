// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Thai (`th`).
class AppLocalizationsTh extends AppLocalizations {
  AppLocalizationsTh([String locale = 'th']) : super(locale);

  @override
  String get language => 'ภาษา';

  @override
  String get language_desc => 'เปลี่ยนภาษาที่ต้องการ';

  @override
  String get all => 'ทั้งหมด';

  @override
  String get grocery => 'ร้านขายของชำ';

  @override
  String get food => 'อาหาร';

  @override
  String get work => 'งาน';

  @override
  String get entertainment => 'ความบันเทิง';

  @override
  String get traveling => 'การเดินทาง';

  @override
  String get other => 'อื่นๆ';

  @override
  String get total_expenses => 'ค่าใช้จ่ายทั้งหมด';

  @override
  String get select_catagory => 'เลือกหมวดหมู่';

  @override
  String get category => 'หมวดหมู่';

  @override
  String get your_trips => 'การเดินทางของคุณ';

  @override
  String get no_trips => 'ยังไม่มีการเดินทาง';

  @override
  String get add_trip => 'เพิ่มการเดินทาง';

  @override
  String get edit_trip => 'แก้ไขการเดินทาง';

  @override
  String get title => 'หัวข้อ';

  @override
  String get enter_title => 'ใส่ชื่อการเดินทาง';

  @override
  String get date_range => 'คุณจะไปเมื่อไหร่?';

  @override
  String get select_date_range => 'กรุณาเลือกช่วงวันที่';

  @override
  String get country => 'ประเทศ';

  @override
  String get select_country => 'เลือกประเทศ';

  @override
  String get country_hint => 'คุณจะไปที่ไหน?';

  @override
  String get city => 'เมือง';

  @override
  String get city_hint => 'ใส่ชื่อเมือง';

  @override
  String get select_city => 'เลือกเมือง';

  @override
  String get failed_to_load_trips => 'ไม่สามารถโหลดการเดินทางได้';

  @override
  String get past_trips => 'การเดินทางที่ผ่านมา';

  @override
  String get trip_name => 'ชื่อการเดินทาง';

  @override
  String get trip_name_hint => 'เช่น ท่องเที่ยวหน้าร้อน 2023';

  @override
  String get amount => 'จำนวนเงิน';

  @override
  String get date => 'วันที่';

  @override
  String get departure_date => 'วันที่ออกเดินทาง';

  @override
  String get edit_itinerary => 'แก้ไขกำหนดการ';

  @override
  String get add_itinerary => 'เพิ่มกำหนดการ';

  @override
  String get itinerary_details => 'รายละเอียดกำหนดการ';

  @override
  String get location => 'สถานที่';

  @override
  String get location_hint => 'เช่น 123 ถนนหลัก, เมือง, ประเทศ';

  @override
  String get pick_location => 'เลือกสถานที่';

  @override
  String get update_location => 'อัปเดตสถานที่';

  @override
  String get itinerary_name => 'เกี่ยวกับอะไร?';

  @override
  String get itinerary_name_hint => 'เช่น วันที่ 1: มาถึงและเที่ยวชมเมือง';

  @override
  String get confirmDeletion => 'ยืนยันการลบ';

  @override
  String get confirmDeletionMessage => 'คุณแน่ใจหรือไม่ที่จะลบรายการนี้?';

  @override
  String get cancel => 'ยกเลิก';

  @override
  String get delete => 'ลบ';

  @override
  String get restaurant => 'ร้านอาหาร';

  @override
  String get accommodation => 'ที่พัก';

  @override
  String get transportation => 'การเดินทาง';

  @override
  String get sightseeing => 'การท่องเที่ยว';

  @override
  String get shopping => 'การช็อปปิ้ง';

  @override
  String get activity => 'กิจกรรม';

  @override
  String get parking => 'ที่จอดรถ';

  @override
  String get note => 'บันทึก';

  @override
  String get movie => 'ภาพยนตร์';

  @override
  String get flight => 'เที่ยวบิน';

  @override
  String get carRental => 'เช่ารถ';

  @override
  String get flight_number => 'หมายเลขเที่ยวบิน';

  @override
  String get flight_number_hint => 'เช่น AA123';

  @override
  String get airline_name => 'ชื่อสายการบิน';

  @override
  String get airline_name_hint => 'เช่น การบินไทย';

  @override
  String get home => 'หน้าแรก';

  @override
  String get trips => 'การเดินทาง';

  @override
  String get saved => 'บุ๊คมาร์ค';

  @override
  String get accommodations => 'ที่พัก';

  @override
  String get pro => 'เร็วๆ นี้';

  @override
  String get account => 'บัญชี';

  @override
  String get settings => 'การตั้งค่า';

  @override
  String get favorites => 'บุ๊คมาร์ค';

  @override
  String get unlock_premium => 'RoamR พรีเมียม';

  @override
  String get premium_subtitle => 'ยกระดับการวางแผนการเดินทางของคุณ';

  @override
  String get coming_soon => 'เร็วๆ นี้';

  @override
  String get ai_trip_planner => 'ตัวช่วยวางแผนการเดินทางด้วย AI';

  @override
  String get ai_trip_planner_desc => 'รับคำแนะนำและกำหนดการเดินทางที่ปรับแต่งด้วย AI';

  @override
  String get trip_gallery => 'แกลเลอรี่การเดินทาง';

  @override
  String get trip_gallery_desc => 'แนบและจัดระเบียบรูปภาพ เอกสาร และความทรงจำสำหรับแต่ละทริป';

  @override
  String get receipt_scanner => 'เครื่องสแกนใบเสร็จ';

  @override
  String get receipt_scanner_desc => 'ติดตามค่าใช้จ่ายโดยอัตโนมัติด้วยการสแกนใบเสร็จ';

  @override
  String get interactive_maps => 'แผนที่แบบโต้ตอบ';

  @override
  String get interactive_maps_desc => 'ดูเส้นทางการเดินทางทั้งหมดด้วยแผนที่แบบโต้ตอบและการนำทาง';

  @override
  String get trip_sharing => 'แชร์การเดินทาง';

  @override
  String get trip_sharing_desc => 'ร่วมมือและวางแผนการเดินทางกับเพื่อนและครอบครัว';

  @override
  String get guest_user => 'เข้าสู่ระบบบัญชีของคุณ';

  @override
  String get account_settings => 'ตั้งค่าบัญชี';

  @override
  String get edit_profile => 'แก้ไขโปรไฟล์';

  @override
  String get profile => 'โปรไฟล์';

  @override
  String get profile_updated_successfully => 'อัปเดตโปรไฟล์สำเร็จ';

  @override
  String get save => 'บันทึก';

  @override
  String get sign_out_confirmation => 'คุณแน่ใจหรือไม่ว่าต้องการออกจากระบบ?';

  @override
  String get please_enter_your_name => 'กรุณาใส่ชื่อของคุณ';

  @override
  String get failed_to_load_profile => 'ไม่สามารถโหลดข้อมูลโปรไฟล์';

  @override
  String get notifications => 'การแจ้งเตือน';

  @override
  String get notifications_desc => 'จัดการการตั้งค่าการแจ้งเตือนของคุณ';

  @override
  String get preferences => 'การตั้งค่า';

  @override
  String get theme => 'ธีม';

  @override
  String get light => 'สว่าง';

  @override
  String get currency => 'สกุลเงิน';

  @override
  String get currency_desc => 'ตั้งค่าสกุลเงินที่ต้องการ';

  @override
  String get about => 'เกี่ยวกับ';

  @override
  String get about_app => 'เกี่ยวกับ RoamR';

  @override
  String get rate_app => 'ให้คะแนนแอป';

  @override
  String get privacy_policy => 'นโยบายความเป็นส่วนตัว';

  @override
  String get sign_in => 'เข้าสู่ระบบ';

  @override
  String get sign_out => 'ออกจากระบบ';

  @override
  String get register => 'ลงทะเบียน';

  @override
  String get create_account => 'สร้างบัญชี';

  @override
  String get continue_with_google => 'ดำเนินการต่อด้วย Google';

  @override
  String get continue_with_apple => 'ดำเนินการต่อด้วย Apple';

  @override
  String get or => 'หรือ';

  @override
  String get email => 'อีเมล';

  @override
  String get password => 'รหัสผ่าน';

  @override
  String get confirm_password => 'ยืนยันรหัสผ่าน';

  @override
  String get name => 'ชื่อ';

  @override
  String get dont_have_account => 'ยังไม่มีบัญชี?';

  @override
  String get already_have_account => 'มีบัญชีแล้ว?';

  @override
  String get signed_out_successfully => 'ออกจากระบบสำเร็จ';

  @override
  String get date_range_example => 'เช่น 25 ม.ค. - 16 ก.พ.';

  @override
  String get add_place => 'เพิ่มสถานที่ที่คุณต้องการไป';

  @override
  String get restaurant_name => 'ชื่อร้านอาหาร';

  @override
  String get restaurant_name_hint => 'เช่น ร้านอาหารญี่ปุ่น';

  @override
  String get accommodation_name => 'ชื่อที่พัก';

  @override
  String get accommodation_name_hint => 'เช่น โรงแรมฮิลตัน';

  @override
  String get transportation_name => 'ชื่อการเดินทาง';

  @override
  String get transportation_name_hint => 'เช่น รถไฟไปโตเกียว';

  @override
  String get sightseeing_name => 'ชื่อสถานที่';

  @override
  String get sightseeing_hint => 'เช่น โตเกียวทาวเวอร์';

  @override
  String get shopping_name => 'ชื่อร้านค้า';

  @override
  String get shopping_hint => 'เช่น ห้างชิบูย่า';

  @override
  String get activity_name => 'ชื่อกิจกรรม';

  @override
  String get activity_hint => 'เช่น คลาสทำอาหาร';

  @override
  String get parking_name => 'สถานที่จอดรถ';

  @override
  String get parking_hint => 'เช่น ที่จอดรถกลาง';

  @override
  String get note_name => 'หัวข้อบันทึก';

  @override
  String get note_hint => 'เช่น การเตือนที่สำคัญ';

  @override
  String get movie_name => 'ชื่อภาพยนตร์/การแสดง';

  @override
  String get movie_hint => 'เช่น การแสดงในโรงละครท้องถิ่น';

  @override
  String get flight_title => 'รายละเอียดเที่ยวบิน';

  @override
  String get flight_title_hint => 'เช่น เที่ยวบินโตเกียวไปโอซาก้า';

  @override
  String get car_rental_name => 'รายละเอียดการเช่ารถ';

  @override
  String get car_rental_hint => 'เช่น โตโยต้า คัมรี่ - เฮิร์ตซ์';

  @override
  String get amount_optional => 'จำนวนเงิน (ถ้ามี)';

  @override
  String get checkin_date => 'วันเช็คอิน';

  @override
  String get checkout_date => 'วันเช็คเอาท์';

  @override
  String get stay_dates => 'วันที่พัก';

  @override
  String get select_checkin_checkout => 'เลือกวันเช็คอินและเช็คเอาท์';

  @override
  String get start_date => 'วันที่เริ่มต้น';

  @override
  String get end_date => 'วันที่สิ้นสุด';

  @override
  String get description => 'คำอธิบาย';

  @override
  String get description_hint => 'เพิ่มบันทึกหรือรายละเอียดเพิ่มเติม';

  @override
  String get about_desc => 'เรียนรู้เพิ่มเติมเกี่ยวกับแอป';

  @override
  String get system => 'ระบบ';

  @override
  String get edit => 'แก้ไข';

  @override
  String get dark => 'มืด';

  @override
  String get date_required => 'ต้องระบุวันที่';

  @override
  String get date_within_trip => 'วันที่ต้องอยู่ในช่วงการเดินทาง';

  @override
  String get no_favorites => 'ยังไม่มีบุ๊คมาร์ค';

  @override
  String get add_favorites_hint => 'กำหนดการเดินทางที่คุณบุ๊คมาร์คไว้จะปรากฏที่นี่';

  @override
  String get premium_title => 'RoamR พรีเมียม';

  @override
  String get premium => 'พรีเมียม';

  @override
  String get premium_features => 'ปลดล็อกฟีเจอร์พรีเมียม';

  @override
  String get unlimited_trip_planning => 'วางแผนการเดินทางไม่จำกัด';

  @override
  String get unlimited_trip_planning_desc => 'สร้างและจัดการการเดินทางได้ไม่จำกัด';

  @override
  String get interactive_map => 'แผนที่การเดินทางแบบโต้ตอบ';

  @override
  String get interactive_map_desc => 'ดูกำหนดการเดินทางทั้งหมดบนแผนที่แบบโต้ตอบพร้อมการนำทาง';

  @override
  String get best_value => 'คุ้มค่าที่สุด';

  @override
  String get yearly => 'รายปี';

  @override
  String get monthly => 'รายเดือน';

  @override
  String get per_year => 'ต่อปี';

  @override
  String get per_month => 'ต่อเดือน';

  @override
  String get save_fifty => 'ประหยัด 50%';

  @override
  String get restore_purchases => 'กู้คืนการซื้อ';

  @override
  String get subscription_details => 'รายละเอียดการสมัครสมาชิก';

  @override
  String get subscription_status => 'สถานะ:';

  @override
  String get subscription_plan => 'แผน:';

  @override
  String get subscription_purchased => 'ซื้อเมื่อ:';

  @override
  String get subscription_expires => 'หมดอายุ:';

  @override
  String get subscription_renews => 'ต่ออายุ:';

  @override
  String get subscription_error => 'ไม่สามารถโหลดรายละเอียดการสมัครสมาชิก';

  @override
  String get loading_premium_options => 'กำลังโหลดตัวเลือกพรีเมียม...';

  @override
  String get premium_features_unlocked => 'ฟีเจอร์พรีเมียมถูกปลดล็อคแล้ว!';

  @override
  String get premium_user_access => 'คุณเป็นผู้ใช้พรีเมียมที่มีสิทธิ์เข้าถึงฟีเจอร์ทั้งหมด';

  @override
  String get no_locations => 'ไม่มีกำหนดการที่มีข้อมูลตำแหน่ง';

  @override
  String get map_not_available => 'แผนที่ไม่พร้อมใช้งาน';

  @override
  String get add_location_details => 'ไม่มีตำแหน่งที่จะแสดงบนแผนที่ โปรดเพิ่มรายละเอียดตำแหน่งในกำหนดการของคุณก่อน';

  @override
  String get ok => 'ตกลง';

  @override
  String get location_permission_denied => 'การอนุญาตตำแหน่งถูกปฏิเสธ';

  @override
  String get location_permission_permanently_denied => 'การอนุญาตตำแหน่งถูกปฏิเสธถาวร';

  @override
  String get location_error => 'เกิดข้อผิดพลาดในการรับตำแหน่ง';

  @override
  String get delete_account => 'ลบบัญชี';

  @override
  String get delete_account_confirmation => 'ลบบัญชีหรือไม่?';

  @override
  String get delete_account_message => 'คุณแน่ใจหรือไม่ว่าต้องการลบบัญชีของคุณ? การกระทำนี้ไม่สามารถยกเลิกได้และข้อมูลทั้งหมดของคุณจะถูกลบอย่างถาวร';

  @override
  String get account_deleted_successfully => 'ลบบัญชีเรียบร้อยแล้ว';

  @override
  String get premium_login_message => 'เข้าสู่ระบบเพื่อเข้าถึงคุณสมบัติพรีเมียม';

  @override
  String get share_app => 'แชร์แอป';

  @override
  String get terms_of_service => 'ข้อกำหนดการใช้บริการ';

  @override
  String get send_feedback => 'ส่งคำติชม';

  @override
  String get statistics => 'สถิติ';

  @override
  String get select_trip => 'เลือกทริป';

  @override
  String get select_trip_to_view_statistics => 'เลือกทริปเพื่อดูสถิติ';

  @override
  String get no_expenses_to_display => 'ไม่มีค่าใช้จ่ายที่จะแสดง';

  @override
  String get expense_over_time => 'ค่าใช้จ่ายตามเวลา';

  @override
  String get expenses_by_category => 'ค่าใช้จ่ายตามหมวดหมู่';

  @override
  String get total => 'รวม';

  @override
  String get category_distribution => 'การกระจายตามหมวดหมู่';

  @override
  String get remove_from_favorites => 'ลบออกจากบุ๊คมาร์ค';

  @override
  String get remove => 'ลบออก';

  @override
  String get remove_from_favorites_confirmation => 'ลบออกจากบุ๊คมาร์ค?';

  @override
  String get remove_from_favorites_message => 'คุณแน่ใจหรือไม่ว่าต้องการลบกำหนดการเดินทางนี้ออกจากบุ๊คมาร์คของคุณ?';

  @override
  String get pie_chart => 'แผนภูมิวงกลม';

  @override
  String get bar_chart => 'แผนภูมิแท่ง';

  @override
  String get trip_summary => 'สรุปการเดินทาง';

  @override
  String get total_trips => 'การเดินทางทั้งหมด';

  @override
  String get total_places_visited => 'สถานที่ที่ไปเยี่ยมชม';

  @override
  String get avg_places_per_trip => 'สถานที่/การเดินทาง';

  @override
  String get total_days_traveled => 'วันเดินทาง';

  @override
  String get shortest_trip => 'การเดินทางที่สั้นที่สุด';

  @override
  String get longest_trip => 'การเดินทางที่ยาวที่สุด';

  @override
  String get day => 'วัน';

  @override
  String get days => 'วัน';

  @override
  String get flights => 'เที่ยวบิน';

  @override
  String get most_frequent => 'กิจกรรมที่ทำบ่อยที่สุด';

  @override
  String get most_activities_day => 'กิจกรรมมากที่สุด/วัน';

  @override
  String get avg_activities_day => 'กิจกรรมเฉลี่ย/วัน';

  @override
  String get avg_spend_per_trip => 'ค่าใช้จ่ายเฉลี่ย/การเดินทาง';

  @override
  String get most_expensive_trip => 'การเดินทางที่แพงที่สุด';

  @override
  String get least_expensive_trip => 'การเดินทางที่ถูกที่สุด';

  @override
  String get top_expense_category => 'หมวดหมู่ที่แพงที่สุด';

  @override
  String get achievements => 'ความสำเร็จ';

  @override
  String get achievements_description => 'ติดตามความสำเร็จในการเดินทางของคุณและปลดล็อกความสำเร็จเมื่อคุณสำรวจโลก!';

  @override
  String get destination_achievements => 'ความสำเร็จด้านจุดหมายปลายทาง';

  @override
  String get expense_achievements => 'ความสำเร็จด้านค่าใช้จ่าย';

  @override
  String get frequency_achievements => 'ความสำเร็จด้านความถี่การเดินทาง';

  @override
  String get duration_achievements => 'ความสำเร็จด้านระยะเวลาการเดินทาง';

  @override
  String get flight_achievements => 'ความสำเร็จด้านเที่ยวบิน';

  @override
  String get novice_traveller_title => 'นักเดินทางมือใหม่';

  @override
  String get novice_traveller_desc => 'เยี่ยมชม 3 ประเทศขึ้นไป';

  @override
  String get world_traveller_title => 'นักเดินทางระดับโลก';

  @override
  String get world_traveller_desc => 'เยี่ยมชม 10 ประเทศขึ้นไป';

  @override
  String get globetrotter_title => 'นักท่องโลก';

  @override
  String get globetrotter_desc => 'เยี่ยมชม 20 ประเทศขึ้นไป';

  @override
  String get continental_explorer_title => 'นักสำรวจทวีป';

  @override
  String get continental_explorer_desc => 'เยี่ยมชม 2 ทวีปขึ้นไปและอย่างน้อย 20 ประเทศ';

  @override
  String get continental_collector_title => 'นักสะสมทวีป';

  @override
  String get continental_collector_desc => 'เยี่ยมชม 4 ทวีปขึ้นไปและอย่างน้อย 25 ประเทศ';

  @override
  String get world_conqueror_title => 'ผู้พิชิตโลก';

  @override
  String get world_conqueror_desc => 'เยี่ยมชมทั้ง 7 ทวีปและอย่างน้อย 30 ประเทศ';

  @override
  String get budget_tracker_title => 'ผู้ติดตามงบประมาณ';

  @override
  String get budget_tracker_desc => 'บันทึกค่าใช้จ่ายรวม 30,000+ บาท';

  @override
  String get expense_manager_title => 'ผู้จัดการค่าใช้จ่าย';

  @override
  String get expense_manager_desc => 'บันทึกค่าใช้จ่ายรวม 150,000+ บาท';

  @override
  String get financial_voyager_title => 'นักเดินทางทางการเงิน';

  @override
  String get financial_voyager_desc => 'บันทึกค่าใช้จ่ายรวม 300,000+ บาท';

  @override
  String get luxury_traveller_title => 'นักเดินทางหรูหรา';

  @override
  String get luxury_traveller_desc => 'บันทึกค่าใช้จ่ายรวม 600,000+ บาท';

  @override
  String get travel_beginner_title => 'ผู้เริ่มต้นเดินทาง';

  @override
  String get travel_beginner_desc => 'เสร็จสิ้นการเดินทาง 3 ครั้งขึ้นไป';

  @override
  String get travel_enthusiast_title => 'ผู้หลงใหลการเดินทาง';

  @override
  String get travel_enthusiast_desc => 'เสร็จสิ้นการเดินทาง 10 ครั้งขึ้นไป';

  @override
  String get travel_addict_title => 'ผู้ติดการเดินทาง';

  @override
  String get travel_addict_desc => 'เสร็จสิ้นการเดินทาง 20 ครั้งขึ้นไป';

  @override
  String get day_tripper_title => 'นักเดินทางวันเดียว';

  @override
  String get day_tripper_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 1 วัน';

  @override
  String get weekend_wanderer_title => 'นักเดินทางวันหยุดสุดสัปดาห์';

  @override
  String get weekend_wanderer_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 3 วัน';

  @override
  String get vacation_voyager_title => 'นักเดินทางวันหยุด';

  @override
  String get vacation_voyager_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 7 วัน';

  @override
  String get extended_explorer_title => 'นักสำรวจระยะยาว';

  @override
  String get extended_explorer_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 14 วัน';

  @override
  String get long_term_traveler_title => 'นักเดินทางระยะยาว';

  @override
  String get long_term_traveler_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 30 วัน';

  @override
  String get nomadic_adventurer_title => 'นักผจญภัยเร่ร่อน';

  @override
  String get nomadic_adventurer_desc => 'เสร็จสิ้นการเดินทางอย่างน้อย 60 วัน';

  @override
  String get first_flight_title => 'ผู้เริ่มต้นการบิน';

  @override
  String get first_flight_desc => 'บันทึกเที่ยวบิน 5 เที่ยวขึ้นไป';

  @override
  String get frequent_flyer_title => 'นักเดินทางประจำ';

  @override
  String get frequent_flyer_desc => 'บันทึกเที่ยวบิน 15 เที่ยวขึ้นไป';

  @override
  String get aviation_enthusiast_title => 'ผู้หลงใหลการบิน';

  @override
  String get aviation_enthusiast_desc => 'บันทึกเที่ยวบิน 30 เที่ยวขึ้นไป';

  @override
  String get progress => 'ความคืบหน้า';

  @override
  String get close => 'ปิด';

  @override
  String get complete_trips_for_achievements => 'เสร็จสิ้นการเดินทางเพื่อปลดล็อกความสำเร็จ!';

  @override
  String get view_all => 'ดูทั้งหมด';

  @override
  String get app_tagline => 'เพื่อนร่วมเดินทางของคุณ';

  @override
  String get website => 'เว็บไซต์';

  @override
  String get facebook => 'เฟซบุ๊ก';

  @override
  String get twitter => 'ทวิตเตอร์/เอ็กซ์';

  @override
  String get instagram => 'อินสตาแกรม';

  @override
  String get feedback_email_subject => 'ข้อเสนอแนะ RoamR';

  @override
  String get feedback_email_body => 'ฉันต้องการให้ข้อเสนอแนะเกี่ยวกับ RoamR:';

  @override
  String get share_app_message_prefix => 'ลองดู RoamR แอปวางแผนการเดินทาง: ';

  @override
  String get share_app_subject => 'RoamR - เพื่อนร่วมเดินทางของคุณ';

  @override
  String could_not_launch_url(Object url) {
    return 'ไม่สามารถเปิด $url';
  }

  @override
  String get could_not_share_app => 'ไม่สามารถแชร์แอป';

  @override
  String get could_not_launch_email => 'ไม่สามารถเปิดไคลเอนต์อีเมล';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'รูปภาพ';

  @override
  String get no_photos_added => 'ยังไม่มีรูปภาพที่เพิ่ม';

  @override
  String get gallery => 'แกลเลอรี';

  @override
  String get camera => 'กล้อง';

  @override
  String get error_loading_image => 'เกิดข้อผิดพลาดในการโหลดรูปภาพ';

  @override
  String get unsupported_attachment_type => 'ประเภทไฟล์แนบไม่รองรับ';

  @override
  String get file_not_found => 'ไม่พบไฟล์';

  @override
  String get see_more => 'ดูเพิ่มเติม';

  @override
  String get select_category => 'เลือกหมวดหมู่';

  @override
  String get done => 'เสร็จสิ้น';

  @override
  String get search_activities_and_places => 'ค้นหากิจกรรมและสถานที่';

  @override
  String get no_results_found => 'ไม่พบผลลัพธ์';

  @override
  String get travel => 'การเดินทาง';

  @override
  String get food_drink => 'อาหารและเครื่องดื่ม';

  @override
  String get art_fun => 'ศิลปะและความสนุก';

  @override
  String get search_results => 'ผลการค้นหา';

  @override
  String get trip_photos => 'ภาพถ่ายการเดินทาง';

  @override
  String get error_loading_photos => 'เกิดข้อผิดพลาดในการโหลดภาพถ่าย';

  @override
  String get no_photos_for_trip => 'ไม่มีภาพถ่ายสำหรับการเดินทางนี้';

  @override
  String get no_audio_for_trip => 'ไม่มีการบันทึกเสียงสำหรับการเดินทางนี้';

  @override
  String get no_documents_for_trip => 'ไม่มีเอกสารสำหรับการเดินทางนี้';

  @override
  String get no_attachments_for_trip => 'ไม่มีไฟล์แนบสำหรับการเดินทางนี้';

  @override
  String get audio => 'เสียง';

  @override
  String get documents => 'เอกสาร';

  @override
  String get open => 'เปิด';

  @override
  String get opening_file => 'กำลังเปิดไฟล์';

  @override
  String get select_document => 'เลือกเอกสาร';

  @override
  String get error_opening_file => 'เกิดข้อผิดพลาดในการเปิดไฟล์';

  @override
  String get no_content_available => 'ไม่มีเนื้อหาให้แสดง';

  @override
  String get error_loading_document => 'เกิดข้อผิดพลาดในการโหลดเอกสาร';

  @override
  String get size => 'ขนาด';

  @override
  String get modified => 'แก้ไขเมื่อ';

  @override
  String get word_document => 'ไม่สามารถแสดงเนื้อหาเอกสาร Word โดยตรงได้';

  @override
  String get excel_spreadsheet => 'ไม่สามารถแสดงเนื้อหาสเปรดชีต Excel โดยตรงได้';

  @override
  String get file_type_not_supported => 'ไม่สามารถแสดงไฟล์ประเภทนี้โดยตรงได้';

  @override
  String get file_available_at => 'ไฟล์มีอยู่ที่';

  @override
  String get attachments => 'ไฟล์แนบ';

  @override
  String get add_attachment => 'เพิ่มไฟล์แนบ';

  @override
  String get choose_attachment_type => 'เลือกประเภทไฟล์แนบ';

  @override
  String get voucher_code => 'รหัสส่วนลด';

  @override
  String get enter_voucher_code => 'ป้อนรหัสส่วนลด';

  @override
  String get enter_voucher_code_hint => 'ป้อนรหัสส่วนลดของคุณ';

  @override
  String get please_enter_voucher_code => 'กรุณาป้อนรหัสส่วนลด';

  @override
  String get voucher_code_applied => 'ใช้รหัสส่วนลดสำเร็จแล้ว!';

  @override
  String get invalid_voucher_code => 'รหัสส่วนลดไม่ถูกต้อง กรุณาลองอีกครั้ง';

  @override
  String get apply => 'ใช้งาน';

  @override
  String get night => 'คืน';

  @override
  String get nights => 'คืน';

  @override
  String get verify_email => 'ยืนยันอีเมล';

  @override
  String get verification_code_sent => 'รหัสยืนยันถูกส่งไปยัง';

  @override
  String get verification_code => 'รหัสยืนยัน';

  @override
  String get please_enter_verification_code => 'กรุณากรอกรหัสยืนยัน';

  @override
  String get verification_code_too_short => 'รหัสยืนยันสั้นเกินไป';

  @override
  String get verify => 'ยืนยัน';

  @override
  String get resend_code => 'ส่งรหัสใหม่';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'กรุณารอ $seconds วินาทีก่อนขอรหัสใหม่';
  }

  @override
  String get please_enter_name => 'กรุณากรอกชื่อของคุณ';

  @override
  String get please_enter_email => 'กรุณากรอกอีเมลของคุณ';

  @override
  String get please_enter_valid_email => 'กรุณากรอกอีเมลที่ถูกต้อง';

  @override
  String get verification_code_login => 'เราจะส่งรหัสยืนยันไปยังอีเมลของคุณ';

  @override
  String get verification_code_register => 'เราจะส่งรหัสยืนยันไปยังอีเมลของคุณเพื่อทำการลงทะเบียนให้เสร็จสมบูรณ์';

  @override
  String get continue_with_email => 'ดำเนินการต่อด้วยอีเมล';

  @override
  String get register_with_email => 'ลงทะเบียนด้วยอีเมล';

  @override
  String get error_invalid_email => 'รูปแบบของที่อยู่อีเมลไม่ถูกต้อง';

  @override
  String get error_user_disabled => 'บัญชีผู้ใช้นี้ถูกปิดการใช้งานแล้ว';

  @override
  String get error_user_not_found => 'บัญชีนี้ไม่มีอยู่หรือถูกลบแล้ว';

  @override
  String get error_invalid_credentials => 'อีเมลหรือรหัสผ่านไม่ถูกต้อง';

  @override
  String get error_email_exists => 'ที่อยู่อีเมลนี้ถูกใช้โดยบัญชีอื่นแล้ว';

  @override
  String get error_weak_password => 'รหัสผ่านไม่แข็งแรงพอ';

  @override
  String get error_email_not_registered => 'อีเมลนี้ยังไม่ได้ลงทะเบียน กรุณาลงทะเบียนก่อน';

  @override
  String get error_otp_disabled => 'การยืนยัน OTP ถูกปิดใช้งาน กรุณาใช้วิธีอื่น';

  @override
  String get error_unknown => 'เกิดข้อผิดพลาดที่ไม่ทราบสาเหตุ กรุณาลองใหม่ภายหลัง';

  @override
  String get account_information => 'ข้อมูลบัญชี';

  @override
  String get account_actions => 'การดำเนินการบัญชี';

  @override
  String get enter_your_name => 'กรอกชื่อของคุณ';

  @override
  String get onboarding_welcome_title => 'ยินดีต้อนรับสู่ Roamr';

  @override
  String get onboarding_welcome_subtitle => 'เพื่อนร่วมเดินทางส่วนตัวของคุณ';

  @override
  String get onboarding_welcome_button => 'เริ่มต้น';

  @override
  String get onboarding_select_language => 'เลือกภาษา';

  @override
  String get onboarding_features_title => 'ฟีเจอร์';

  @override
  String get onboarding_feature_maps => 'แผนที่แบบโต้ตอบ';

  @override
  String get onboarding_feature_calendar => 'ปฏิทินการเดินทาง';

  @override
  String get onboarding_feature_social => 'การแชร์ทางสังคม';

  @override
  String get onboarding_feature_personalize => 'ประสบการณ์ส่วนตัว';

  @override
  String get onboarding_features_button => 'ถัดไป';

  @override
  String get onboarding_theme_title => 'เลือกธีม';

  @override
  String get onboarding_theme_subtitle => 'เลือกธีมที่คุณต้องการ';

  @override
  String get onboarding_theme_button => 'ถัดไป';

  @override
  String get features_title => 'วางแผนการเดินทางที่สมบูรณ์แบบของคุณ';

  @override
  String get features_subtitle => 'ทุกสิ่งที่คุณต้องการเพื่อจัดการการเดินทางของคุณ';

  @override
  String get location_search => 'ค้นหาสถานที่อัจฉริยะ';

  @override
  String get location_search_desc => 'ค้นหาและเพิ่มสถานที่ในแผนการเดินทางของคุณได้อย่างง่ายดาย';

  @override
  String get itinerary_map => 'แผนที่เส้นทางแบบโต้ตอบ';

  @override
  String get itinerary_map_desc => 'ดูเส้นทางการเดินทางทั้งหมดของคุณบนแผนที่แบบโต้ตอบ';

  @override
  String get expense_tracking => 'ติดตามค่าใช้จ่าย';

  @override
  String get expense_tracking_desc => 'ติดตามและจัดหมวดหมู่ค่าใช้จ่ายการเดินทางทั้งหมดของคุณ';

  @override
  String get travel_analytics => 'วิเคราะห์การเดินทาง';

  @override
  String get travel_analytics_desc => 'ดูข้อมูลเชิงลึกและกราฟของรูปแบบการเดินทางของคุณ';

  @override
  String get trip_attachments => 'ไฟล์แนบการเดินทาง';

  @override
  String get trip_attachments_desc => 'แนบรูปภาพ เอกสาร และความทรงจำให้กับการเดินทางของคุณ';

  @override
  String get comprehensive_planning => 'การวางแผนที่ครอบคลุม';

  @override
  String get comprehensive_planning_desc => 'วางแผนเที่ยวบิน โรงแรม กิจกรรม และอื่นๆ พร้อมตัวเลือกที่ละเอียด';

  @override
  String get no_attachments_added_yet => 'ยังไม่มีไฟล์แนบที่เพิ่มเข้ามา';

  @override
  String get usage_left => 'เหลือ';

  @override
  String get usage_details_title => 'รายละเอียดการใช้งาน';

  @override
  String get unlimited => 'ไม่จำกัด';

  @override
  String get used => 'ที่ใช้ไปแล้ว';

  @override
  String get left => 'เหลือ';

  @override
  String get premium_required_message => 'คุณถึงขีดจำกัดฟรีแล้ว อัปเกรดเป็นพรีเมียมเพื่อสร้างทริปเพิ่มเติม';

  @override
  String get upgrade => 'อัปเกรด';

  @override
  String get itineraries => 'กำหนดการเดินทาง';

  @override
  String get notification_title_no_trips => 'เริ่มต้นการผจญภัยของคุณ!';

  @override
  String get notification_body_no_trips => 'สร้างทริปแรกของคุณและเริ่มวางแผนการเดินทางของคุณ';

  @override
  String get notification_title_no_itinerary => 'วางแผนกำหนดการเดินทางของคุณ!';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'เพิ่มกำหนดการเดินทางในทริป \"$tripName\" ของคุณ';
  }

  @override
  String get notification_title_no_places => 'เพิ่มสถานที่ที่ต้องการไป!';

  @override
  String notification_body_no_places(Object tripName) {
    return 'เพิ่มสถานที่ท่องเที่ยวในทริป \"$tripName\" ของคุณเพื่อให้สมบูรณ์ยิ่งขึ้น';
  }

  @override
  String get notification_title_keep_planning => 'วางแผนทริปของคุณต่อไป!';

  @override
  String notification_body_keep_planning(Object tripName) {
    return 'สร้างกำหนดการเดินทางที่สมบูรณ์แบบสำหรับ \"$tripName\" ต่อไป';
  }

  @override
  String get ai_chat_title => 'AI Chat';

  @override
  String get ai_chat_input_hint => 'Ask me about your travel plans...';

  @override
  String get ai_chat_loading => 'RoamR AI is thinking...';

  @override
  String get ai_chat_welcome => 'Hi! I\'m RoamR AI, your travel assistant. How can I help you plan your next adventure?';
}
