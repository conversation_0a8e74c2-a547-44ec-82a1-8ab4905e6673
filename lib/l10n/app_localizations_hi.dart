// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Hindi (`hi`).
class AppLocalizationsHi extends AppLocalizations {
  AppLocalizationsHi([String locale = 'hi']) : super(locale);

  @override
  String get language => 'भाषा';

  @override
  String get language_desc => 'अपनी पसंदीदा भाषा बदलें';

  @override
  String get all => 'सभी';

  @override
  String get grocery => 'किराना';

  @override
  String get food => 'भोजन';

  @override
  String get work => 'काम';

  @override
  String get entertainment => 'मनोरंजन';

  @override
  String get traveling => 'यात्रा';

  @override
  String get other => 'अन्य';

  @override
  String get total_expenses => 'कुल खर्च';

  @override
  String get select_catagory => 'श्रेणी चुनें';

  @override
  String get category => 'श्रेणी';

  @override
  String get your_trips => 'आपकी यात्राएं';

  @override
  String get no_trips => 'अभी तक कोई यात्रा नहीं।';

  @override
  String get add_trip => 'यात्रा जोड़ें';

  @override
  String get edit_trip => 'यात्रा संपादित करें';

  @override
  String get title => 'शीर्षक';

  @override
  String get enter_title => 'यात्रा का नाम दर्ज करें';

  @override
  String get date_range => 'आप कब जा रहे हैं?';

  @override
  String get select_date_range => 'कृपया तिथि सीमा चुनें';

  @override
  String get country => 'देश';

  @override
  String get select_country => 'एक देश चुनें';

  @override
  String get country_hint => 'आप कहां जा रहे हैं?';

  @override
  String get city => 'शहर';

  @override
  String get city_hint => 'एक शहर दर्ज करें';

  @override
  String get select_city => 'एक शहर चुनें';

  @override
  String get failed_to_load_trips => 'यात्राएं लोड करने में विफल';

  @override
  String get past_trips => 'पिछली यात्राएं';

  @override
  String get trip_name => 'यात्रा का नाम';

  @override
  String get trip_name_hint => 'उदा. जापान यात्रा 2025';

  @override
  String get amount => 'राशि';

  @override
  String get date => 'तिथि';

  @override
  String get departure_date => 'प्रस्थान तिथि';

  @override
  String get edit_itinerary => 'यात्रा कार्यक्रम संपादित करें';

  @override
  String get add_itinerary => 'यात्रा कार्यक्रम जोड़ें';

  @override
  String get itinerary_details => 'यात्रा कार्यक्रम विवरण';

  @override
  String get location => 'स्थान';

  @override
  String get location_hint => 'उदा. 123 मुख्य मार्ग, शहर, देश';

  @override
  String get pick_location => 'स्थान चुनें';

  @override
  String get update_location => 'स्थान अपडेट करें';

  @override
  String get itinerary_name => 'यह यात्रा किस बारे में है?';

  @override
  String get itinerary_name_hint => 'उदा. दिन 1: आगमन और शहर भ्रमण';

  @override
  String get confirmDeletion => 'हटाने की पुष्टि करें';

  @override
  String get confirmDeletionMessage => 'क्या आप वाकई इस आइटम को हटाना चाहते हैं?';

  @override
  String get cancel => 'रद्द करें';

  @override
  String get delete => 'हटाएं';

  @override
  String get restaurant => 'रेस्तरां';

  @override
  String get accommodation => 'आवास';

  @override
  String get transportation => 'परिवहन';

  @override
  String get sightseeing => 'पर्यटन';

  @override
  String get shopping => 'खरीदारी';

  @override
  String get activity => 'गतिविधि';

  @override
  String get parking => 'पार्किंग';

  @override
  String get note => 'नोट';

  @override
  String get movie => 'फिल्म';

  @override
  String get flight => 'उड़ान';

  @override
  String get carRental => 'कार किराया';

  @override
  String get flight_number => 'उड़ान संख्या';

  @override
  String get flight_number_hint => 'उदा. AA123';

  @override
  String get airline_name => 'एयरलाइन का नाम';

  @override
  String get airline_name_hint => 'उदा. अमेरिकन एयरलाइंस';

  @override
  String get home => 'होम';

  @override
  String get trips => 'यात्राएं';

  @override
  String get saved => 'बुकमार्क';

  @override
  String get accommodations => 'आवास';

  @override
  String get pro => 'प्रो';

  @override
  String get account => 'खाता';

  @override
  String get settings => 'सेटिंग्स';

  @override
  String get favorites => 'बुकमार्क';

  @override
  String get unlock_premium => 'RoamR प्रीमियम';

  @override
  String get premium_subtitle => 'अपनी यात्रा योजना को अगले स्तर पर ले जाएं';

  @override
  String get coming_soon => 'जल्द आ रहा है';

  @override
  String get ai_trip_planner => 'एआई यात्रा योजनाकार';

  @override
  String get ai_trip_planner_desc => 'एआई द्वारा संचालित व्यक्तिगत यात्रा सुझाव और कार्यक्रम प्राप्त करें';

  @override
  String get trip_gallery => 'यात्रा गैलरी';

  @override
  String get trip_gallery_desc => 'प्रत्येक यात्रा के लिए फोटो, दस्तावेज और यादें संलग्न करें';

  @override
  String get receipt_scanner => 'रसीद स्कैनर';

  @override
  String get receipt_scanner_desc => 'अपने बिल और रसीदें स्कैन करके स्वचालित रूप से खर्च ट्रैक करें';

  @override
  String get interactive_maps => 'इंटरैक्टिव मानचित्र';

  @override
  String get interactive_maps_desc => 'इंटरैक्टिव मानचित्र और नेविगेशन के साथ अपना पूरा यात्रा मार्ग देखें';

  @override
  String get trip_sharing => 'यात्रा साझाकरण';

  @override
  String get trip_sharing_desc => 'दोस्तों और परिवार के साथ यात्रा की योजना बनाएं';

  @override
  String get guest_user => 'अपने खाते में साइन इन करें';

  @override
  String get account_settings => 'खाता सेटिंग्स';

  @override
  String get edit_profile => 'प्रोफ़ाइल संपादित करें';

  @override
  String get profile => 'प्रोफाइल';

  @override
  String get profile_updated_successfully => 'प्रोफाइल सफलतापूर्वक अपडेट किया गया';

  @override
  String get save => 'सहेजें';

  @override
  String get sign_out_confirmation => 'क्या आप वाकई साइन आउट करना चाहते हैं?';

  @override
  String get please_enter_your_name => 'कृपया अपना नाम दर्ज करें';

  @override
  String get failed_to_load_profile => 'प्रोफाइल डेटा लोड करने में विफल';

  @override
  String get notifications => 'सूचनाएं';

  @override
  String get notifications_desc => 'अपनी सूचना प्राथमिकताएं प्रबंधित करें';

  @override
  String get preferences => 'प्राथमिकताएं';

  @override
  String get theme => 'थीम';

  @override
  String get light => 'लाइट';

  @override
  String get currency => 'मुद्रा';

  @override
  String get currency_desc => 'अपनी पसंदीदा मुद्रा सेट करें';

  @override
  String get about => 'के बारे में';

  @override
  String get about_app => 'RoamR के बारे में';

  @override
  String get rate_app => 'ऐप रेट करें';

  @override
  String get privacy_policy => 'गोपनीयता नीति';

  @override
  String get sign_in => 'साइन इन करें';

  @override
  String get sign_out => 'साइन आउट करें';

  @override
  String get register => 'पंजीकरण करें';

  @override
  String get create_account => 'खाता बनाएं';

  @override
  String get continue_with_google => 'Google के साथ जारी रखें';

  @override
  String get continue_with_apple => 'Apple के साथ जारी रखें';

  @override
  String get or => 'या';

  @override
  String get email => 'ईमेल';

  @override
  String get password => 'पासवर्ड';

  @override
  String get confirm_password => 'पासवर्ड की पुष्टि करें';

  @override
  String get name => 'नाम';

  @override
  String get dont_have_account => 'खाता नहीं है?';

  @override
  String get already_have_account => 'पहले से ही खाता है?';

  @override
  String get signed_out_successfully => 'सफलतापूर्वक साइन आउट किया गया';

  @override
  String get date_range_example => 'उदा. 25 जन - 16 फर';

  @override
  String get add_place => 'जाने के लिए स्थान जोड़ें';

  @override
  String get restaurant_name => 'रेस्तरां का नाम';

  @override
  String get restaurant_name_hint => 'उदा. सुशी पैलेस';

  @override
  String get accommodation_name => 'आवास का नाम';

  @override
  String get accommodation_name_hint => 'उदा. हिल्टन होटल';

  @override
  String get transportation_name => 'परिवहन का नाम';

  @override
  String get transportation_name_hint => 'उदा. टोक्यो के लिए ट्रेन';

  @override
  String get sightseeing_name => 'स्थान का नाम';

  @override
  String get sightseeing_hint => 'उदा. टोक्यो टावर';

  @override
  String get shopping_name => 'स्टोर का नाम';

  @override
  String get shopping_hint => 'उदा. शिबुया मॉल';

  @override
  String get activity_name => 'गतिविधि का नाम';

  @override
  String get activity_hint => 'उदा. कुकिंग क्लास';

  @override
  String get parking_name => 'पार्किंग स्थान';

  @override
  String get parking_hint => 'उदा. सेंट्रल पार्किंग';

  @override
  String get note_name => 'नोट शीर्षक';

  @override
  String get note_hint => 'उदा. महत्वपूर्ण रिमाइंडर';

  @override
  String get movie_name => 'फिल्म/शो का नाम';

  @override
  String get movie_hint => 'उदा. स्थानीय थिएटर शो';

  @override
  String get flight_title => 'उड़ान विवरण';

  @override
  String get flight_title_hint => 'उदा. टोक्यो से ओसाका उड़ान';

  @override
  String get car_rental_name => 'कार किराया विवरण';

  @override
  String get car_rental_hint => 'उदा. टोयोटा कैमरी - हर्ट्ज़';

  @override
  String get amount_optional => 'राशि (यदि लागू हो)';

  @override
  String get checkin_date => 'चेक-इन तिथि';

  @override
  String get checkout_date => 'चेक-आउट तिथि';

  @override
  String get stay_dates => 'ठहरने की तिथियां';

  @override
  String get select_checkin_checkout => 'चेक-इन और चेक-आउट तिथियां चुनें';

  @override
  String get start_date => 'प्रारंभ तिथि';

  @override
  String get end_date => 'अंतिम तिथि';

  @override
  String get description => 'विवरण';

  @override
  String get description_hint => 'कोई अतिरिक्त नोट्स या विवरण जोड़ें';

  @override
  String get about_desc => 'ऐप के बारे में अधिक जानें';

  @override
  String get system => 'सिस्टम';

  @override
  String get edit => 'संपादित करें';

  @override
  String get dark => 'डार्क';

  @override
  String get date_required => 'तिथि आवश्यक है';

  @override
  String get date_within_trip => 'तिथि यात्रा अवधि के भीतर होनी चाहिए';

  @override
  String get no_favorites => 'अभी तक कोई बुकमार्क नहीं';

  @override
  String get add_favorites_hint => 'आपके बुकमार्क किए गए यात्रा कार्यक्रम यहां दिखाई देंगे';

  @override
  String get premium_title => 'RoamR प्रीमियम';

  @override
  String get premium => 'प्रीमियम';

  @override
  String get premium_features => 'प्रीमियम सुविधाएं अनलॉक करें';

  @override
  String get unlimited_trip_planning => 'असीमित यात्रा योजना';

  @override
  String get unlimited_trip_planning_desc => 'असीमित यात्राओं को बनाएं और प्रबंधित करें';

  @override
  String get interactive_map => 'इंटरैक्टिव यात्रा मानचित्र';

  @override
  String get interactive_map_desc => 'अपने पूरे यात्रा कार्यक्रम को इंटरैक्टिव मानचित्र पर नेविगेशन के साथ देखें';

  @override
  String get best_value => 'सर्वश्रेष्ठ मूल्य';

  @override
  String get yearly => 'वार्षिक';

  @override
  String get monthly => 'मासिक';

  @override
  String get per_year => 'प्रति वर्ष';

  @override
  String get per_month => 'प्रति माह';

  @override
  String get save_fifty => '50% बचाएं';

  @override
  String get restore_purchases => 'खरीदारी पुनर्स्थापित करें';

  @override
  String get subscription_details => 'सदस्यता विवरण';

  @override
  String get subscription_status => 'स्थिति:';

  @override
  String get subscription_plan => 'प्लान:';

  @override
  String get subscription_purchased => 'खरीदा गया:';

  @override
  String get subscription_expires => 'समाप्ति तिथि:';

  @override
  String get subscription_renews => 'नवीनीकरण तिथि:';

  @override
  String get subscription_error => 'सदस्यता विवरण लोड नहीं किया जा सका';

  @override
  String get loading_premium_options => 'प्रीमियम विकल्प लोड हो रहे हैं...';

  @override
  String get premium_features_unlocked => 'प्रीमियम सुविधाएं अनलॉक कर दी गई हैं!';

  @override
  String get premium_user_access => 'आप एक प्रीमियम उपयोगकर्ता हैं जिसे सभी सुविधाओं तक पहुंच है';

  @override
  String get no_locations => 'स्थान डेटा वाले कोई यात्रा कार्यक्रम नहीं हैं';

  @override
  String get map_not_available => 'मानचित्र उपलब्ध नहीं है';

  @override
  String get add_location_details => 'मानचित्र पर प्रदर्शित करने के लिए कोई स्थान नहीं है। पहले अपने यात्रा कार्यक्रमों में स्थान विवरण जोड़ें।';

  @override
  String get ok => 'ठीक है';

  @override
  String get location_permission_denied => 'स्थान अनुमति अस्वीकृत';

  @override
  String get location_permission_permanently_denied => 'स्थान अनुमति स्थायी रूप से अस्वीकृत';

  @override
  String get location_error => 'स्थान प्राप्त करने में त्रुटि';

  @override
  String get delete_account => 'खाता हटाएं';

  @override
  String get delete_account_confirmation => 'खाता हटाएं?';

  @override
  String get delete_account_message => 'क्या आप वाकई अपना खाता हटाना चाहते हैं? यह कार्यवाई वापस नहीं की जा सकती है और आपका सभी डेटा स्थायी रूप से हटा दिया जाएगा।';

  @override
  String get account_deleted_successfully => 'खाता सफलतापूर्वक हटा दिया गया';

  @override
  String get premium_login_message => 'प्रीमियम सुविधाओं का उपयोग करने के लिए साइन इन करें';

  @override
  String get share_app => 'ऐप शेयर करें';

  @override
  String get terms_of_service => 'सेवा की शर्तें';

  @override
  String get send_feedback => 'प्रतिक्रिया भेजें';

  @override
  String get statistics => 'आंकड़े';

  @override
  String get select_trip => 'यात्रा चुनें';

  @override
  String get select_trip_to_view_statistics => 'आंकड़े देखने के लिए यात्रा चुनें';

  @override
  String get no_expenses_to_display => 'दिखाने के लिए कोई खर्च नहीं';

  @override
  String get expense_over_time => 'समय के साथ खर्च';

  @override
  String get expenses_by_category => 'श्रेणी के अनुसार खर्च';

  @override
  String get total => 'कुल';

  @override
  String get category_distribution => 'श्रेणी वितरण';

  @override
  String get remove_from_favorites => 'बुकमार्क से हटाएं';

  @override
  String get remove => 'हटाएं';

  @override
  String get remove_from_favorites_confirmation => 'बुकमार्क से हटाएं?';

  @override
  String get remove_from_favorites_message => 'क्या आप वाकई इस यात्रा कार्यक्रम को अपने बुकमार्क से हटाना चाहते हैं?';

  @override
  String get pie_chart => 'पाई चार्ट';

  @override
  String get bar_chart => 'बार चार्ट';

  @override
  String get trip_summary => 'यात्रा सारांश';

  @override
  String get total_trips => 'कुल यात्राएं';

  @override
  String get total_places_visited => 'देखे गए स्थान';

  @override
  String get avg_places_per_trip => 'स्थान/यात्रा';

  @override
  String get total_days_traveled => 'यात्रा के दिन';

  @override
  String get shortest_trip => 'सबसे छोटी यात्रा';

  @override
  String get longest_trip => 'सबसे लंबी यात्रा';

  @override
  String get day => 'दिन';

  @override
  String get days => 'दिन';

  @override
  String get flights => 'उड़ानें';

  @override
  String get most_frequent => 'सबसे लोकप्रिय गतिविधि';

  @override
  String get most_activities_day => 'सबसे अधिक गतिविधियां/दिन';

  @override
  String get avg_activities_day => 'औसत गतिविधियां/दिन';

  @override
  String get avg_spend_per_trip => 'औसत खर्च/यात्रा';

  @override
  String get most_expensive_trip => 'सबसे महंगी यात्रा';

  @override
  String get least_expensive_trip => 'सबसे सस्ती यात्रा';

  @override
  String get top_expense_category => 'सबसे महंगी श्रेणी';

  @override
  String get achievements => 'उपलब्धियां';

  @override
  String get achievements_description => 'अपने यात्रा मील के पत्थर ट्रैक करें और दुनिया की खोज करते हुए उपलब्धियां अनलॉक करें!';

  @override
  String get destination_achievements => 'गंतव्य उपलब्धियां';

  @override
  String get expense_achievements => 'खर्च उपलब्धियां';

  @override
  String get frequency_achievements => 'यात्रा आवृत्ति उपलब्धियां';

  @override
  String get duration_achievements => 'यात्रा अवधि उपलब्धियां';

  @override
  String get flight_achievements => 'उड़ान उपलब्धियां';

  @override
  String get novice_traveller_title => 'नौसिखिया यात्री';

  @override
  String get novice_traveller_desc => '3 या अधिक अलग-अलग देशों की यात्रा करें';

  @override
  String get world_traveller_title => 'विश्व यात्री';

  @override
  String get world_traveller_desc => '10 या अधिक अलग-अलग देशों की यात्रा करें';

  @override
  String get globetrotter_title => 'ग्लोबट्रॉटर';

  @override
  String get globetrotter_desc => '20 या अधिक अलग-अलग देशों की यात्रा करें';

  @override
  String get continental_explorer_title => 'महाद्वीपीय अन्वेषक';

  @override
  String get continental_explorer_desc => '2 या अधिक महाद्वीपों और कम से कम 20 देशों की यात्रा करें';

  @override
  String get continental_collector_title => 'महाद्वीपीय संग्राहक';

  @override
  String get continental_collector_desc => '4 या अधिक महाद्वीपों और कम से कम 25 देशों की यात्रा करें';

  @override
  String get world_conqueror_title => 'विश्व विजेता';

  @override
  String get world_conqueror_desc => 'सभी 7 महाद्वीपों और कम से कम 30 देशों की यात्रा करें';

  @override
  String get budget_tracker_title => 'बजट ट्रैकर';

  @override
  String get budget_tracker_desc => 'कुल खर्च में ₹1,00,000+ का हिसाब रखें';

  @override
  String get expense_manager_title => 'व्यय प्रबंधक';

  @override
  String get expense_manager_desc => 'कुल खर्च में ₹5,00,000+ का हिसाब रखें';

  @override
  String get financial_voyager_title => 'वित्तीय यात्री';

  @override
  String get financial_voyager_desc => 'कुल खर्च में ₹10,00,000+ का हिसाब रखें';

  @override
  String get luxury_traveller_title => 'लक्जरी यात्री';

  @override
  String get luxury_traveller_desc => 'कुल खर्च में ₹20,00,000+ का हिसाब रखें';

  @override
  String get travel_beginner_title => 'यात्रा शुरुआती';

  @override
  String get travel_beginner_desc => '3 या अधिक यात्राएं पूरी करें';

  @override
  String get travel_enthusiast_title => 'यात्रा उत्साही';

  @override
  String get travel_enthusiast_desc => '10 या अधिक यात्राएं पूरी करें';

  @override
  String get travel_addict_title => 'यात्रा के आदी';

  @override
  String get travel_addict_desc => '20 या अधिक यात्राएं पूरी करें';

  @override
  String get day_tripper_title => 'एक दिन का यात्री';

  @override
  String get day_tripper_desc => 'कम से कम 1 दिन की यात्रा पूरी करें';

  @override
  String get weekend_wanderer_title => 'वीकेंड घुमक्कड़';

  @override
  String get weekend_wanderer_desc => 'कम से कम 3 दिन की यात्रा पूरी करें';

  @override
  String get vacation_voyager_title => 'छुट्टी यात्री';

  @override
  String get vacation_voyager_desc => 'कम से कम 7 दिन की यात्रा पूरी करें';

  @override
  String get extended_explorer_title => 'विस्तारित अन्वेषक';

  @override
  String get extended_explorer_desc => 'कम से कम 14 दिन की यात्रा पूरी करें';

  @override
  String get long_term_traveler_title => 'दीर्घकालिक यात्री';

  @override
  String get long_term_traveler_desc => 'कम से कम 30 दिन की यात्रा पूरी करें';

  @override
  String get nomadic_adventurer_title => 'घुमंतू साहसी';

  @override
  String get nomadic_adventurer_desc => 'कम से कम 60 दिन की यात्रा पूरी करें';

  @override
  String get first_flight_title => 'उड़ान शुरुआती';

  @override
  String get first_flight_desc => '5 या अधिक उड़ानें रिकॉर्ड करें';

  @override
  String get frequent_flyer_title => 'फ्रीक्वेंट फ्लायर';

  @override
  String get frequent_flyer_desc => '15 या अधिक उड़ानें रिकॉर्ड करें';

  @override
  String get aviation_enthusiast_title => 'विमानन उत्साही';

  @override
  String get aviation_enthusiast_desc => '30 या अधिक उड़ानें रिकॉर्ड करें';

  @override
  String get progress => 'प्रगति';

  @override
  String get close => 'बंद करें';

  @override
  String get complete_trips_for_achievements => 'उपलब्धियां अनलॉक करने के लिए यात्राएं पूरी करें!';

  @override
  String get view_all => 'सभी देखें';

  @override
  String get app_tagline => 'आपका यात्रा साथी';

  @override
  String get website => 'वेबसाइट';

  @override
  String get facebook => 'फेसबुक';

  @override
  String get twitter => 'ट्विटर/एक्स';

  @override
  String get instagram => 'इंस्टाग्राम';

  @override
  String get feedback_email_subject => 'RoamR प्रतिक्रिया';

  @override
  String get feedback_email_body => 'मैं RoamR के बारे में प्रतिक्रिया देना चाहता हूं:';

  @override
  String get share_app_message_prefix => 'RoamR, एक यात्रा योजना ऐप देखें: ';

  @override
  String get share_app_subject => 'RoamR - आपका यात्रा साथी';

  @override
  String could_not_launch_url(Object url) {
    return '$url लॉन्च नहीं कर सका';
  }

  @override
  String get could_not_share_app => 'ऐप साझा नहीं कर सका';

  @override
  String get could_not_launch_email => 'ईमेल क्लाइंट लॉन्च नहीं कर सका';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'तस्वीरें';

  @override
  String get no_photos_added => 'अभी तक कोई तस्वीर नहीं जोड़ी गई';

  @override
  String get gallery => 'गैलरी';

  @override
  String get camera => 'कैमरा';

  @override
  String get error_loading_image => 'छवि लोड करने में त्रुटि';

  @override
  String get unsupported_attachment_type => 'असमर्थित अटैचमेंट प्रकार';

  @override
  String get file_not_found => 'फ़ाइल नहीं मिली';

  @override
  String get see_more => 'और देखें';

  @override
  String get select_category => 'श्रेणी चुनें';

  @override
  String get done => 'हो गया';

  @override
  String get search_activities_and_places => 'गतिविधियों और स्थानों की खोज करें';

  @override
  String get no_results_found => 'कोई परिणाम नहीं मिला';

  @override
  String get travel => 'यात्रा';

  @override
  String get food_drink => 'खाना और पेय';

  @override
  String get art_fun => 'कला और मनोरंजन';

  @override
  String get search_results => 'खोज परिणाम';

  @override
  String get trip_photos => 'यात्रा फोटो';

  @override
  String get error_loading_photos => 'फोटो लोड करने में त्रुटि';

  @override
  String get no_photos_for_trip => 'इस यात्रा के लिए कोई फोटो नहीं';

  @override
  String get no_audio_for_trip => 'इस यात्रा के लिए कोई ऑडियो रिकॉर्डिंग नहीं';

  @override
  String get no_documents_for_trip => 'इस यात्रा के लिए कोई दस्तावेज़ नहीं';

  @override
  String get no_attachments_for_trip => 'इस यात्रा के लिए कोई अटैचमेंट नहीं';

  @override
  String get audio => 'ऑडियो';

  @override
  String get documents => 'दस्तावेज़';

  @override
  String get open => 'खोलें';

  @override
  String get opening_file => 'फ़ाइल खोल रहा है';

  @override
  String get select_document => 'दस्तावेज़ चुनें';

  @override
  String get error_opening_file => 'फ़ाइल खोलने में त्रुटि';

  @override
  String get no_content_available => 'प्रदर्शित करने के लिए कोई सामग्री उपलब्ध नहीं है';

  @override
  String get error_loading_document => 'दस्तावेज़ लोड करने में त्रुटि';

  @override
  String get size => 'आकार';

  @override
  String get modified => 'संशोधित';

  @override
  String get word_document => 'Word दस्तावेज़ सामग्री सीधे प्रदर्शित नहीं की जा सकती';

  @override
  String get excel_spreadsheet => 'Excel स्प्रेडशीट सामग्री सीधे प्रदर्शित नहीं की जा सकती';

  @override
  String get file_type_not_supported => 'इस फ़ाइल प्रकार को सीधे प्रदर्शित नहीं किया जा सकता';

  @override
  String get file_available_at => 'फ़ाइल यहां उपलब्ध है';

  @override
  String get attachments => 'अनुलग्नक';

  @override
  String get add_attachment => 'अनुलग्नक जोड़ें';

  @override
  String get choose_attachment_type => 'अनुलग्नक प्रकार चुनें';

  @override
  String get voucher_code => 'वाउचर कोड';

  @override
  String get enter_voucher_code => 'वाउचर कोड दर्ज करें';

  @override
  String get enter_voucher_code_hint => 'अपना वाउचर कोड दर्ज करें';

  @override
  String get please_enter_voucher_code => 'कृपया एक वाउचर कोड दर्ज करें';

  @override
  String get voucher_code_applied => 'वाउचर कोड सफलतापूर्वक लागू किया गया!';

  @override
  String get invalid_voucher_code => 'अमान्य वाउचर कोड। कृपया पुन: प्रयास करें।';

  @override
  String get apply => 'लागू करें';

  @override
  String get night => 'रात';

  @override
  String get nights => 'रातें';

  @override
  String get verify_email => 'ईमेल सत्यापित करें';

  @override
  String get verification_code_sent => 'सत्यापन कोड भेजा गया है';

  @override
  String get verification_code => 'सत्यापन कोड';

  @override
  String get please_enter_verification_code => 'कृपया सत्यापन कोड दर्ज करें';

  @override
  String get verification_code_too_short => 'सत्यापन कोड बहुत छोटा है';

  @override
  String get verify => 'सत्यापित करें';

  @override
  String get resend_code => 'कोड पुन: भेजें';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'कृपया नया कोड मांगने से पहले $seconds सेकंड प्रतीक्षा करें';
  }

  @override
  String get please_enter_name => 'कृपया अपना नाम दर्ज करें';

  @override
  String get please_enter_email => 'कृपया अपना ईमेल दर्ज करें';

  @override
  String get please_enter_valid_email => 'कृपया एक वैध ईमेल पता दर्ज करें';

  @override
  String get verification_code_login => 'हम आपके ईमेल पर एक सत्यापन कोड भेजेंगे';

  @override
  String get verification_code_register => 'हम पंजीकरण पूरा करने के लिए आपके ईमेल पर एक सत्यापन कोड भेजेंगे';

  @override
  String get continue_with_email => 'ईमेल के साथ जारी रखें';

  @override
  String get register_with_email => 'ईमेल के साथ पंजीकरण करें';

  @override
  String get error_invalid_email => 'ईमेल पता गलत तरीके से फॉर्मेट किया गया है।';

  @override
  String get error_user_disabled => 'यह उपयोगकर्ता खाता अक्षम कर दिया गया है।';

  @override
  String get error_user_not_found => 'यह खाता मौजूद नहीं है या हटा दिया गया है।';

  @override
  String get error_invalid_credentials => 'गलत ईमेल या पासवर्ड प्रदान किया गया।';

  @override
  String get error_email_exists => 'ईमेल पता पहले से ही किसी अन्य खाते द्वारा उपयोग में है।';

  @override
  String get error_weak_password => 'पासवर्ड बहुत कमजोर है।';

  @override
  String get error_email_not_registered => 'यह ईमेल पंजीकृत नहीं है। कृपया पहले पंजीकरण करें।';

  @override
  String get error_otp_disabled => 'OTP सत्यापन अक्षम है। कृपया कोई अन्य विधि आजमाएं।';

  @override
  String get error_unknown => 'एक अज्ञात त्रुटि हुई। कृपया बाद में पुन: प्रयास करें।';

  @override
  String get account_information => 'खाता जानकारी';

  @override
  String get account_actions => 'खाता कार्रवाई';

  @override
  String get enter_your_name => 'अपना नाम दर्ज करें';

  @override
  String get onboarding_welcome_title => 'RoamR में आपका स्वागत है';

  @override
  String get onboarding_welcome_subtitle => 'आपका व्यक्तिगत यात्रा साथी';

  @override
  String get onboarding_welcome_button => 'शुरू करें';

  @override
  String get onboarding_select_language => 'भाषा चुनें';

  @override
  String get onboarding_features_title => 'विशेषताएं';

  @override
  String get onboarding_feature_maps => 'इंटरैक्टिव मानचित्र';

  @override
  String get onboarding_feature_calendar => 'यात्रा कैलेंडर';

  @override
  String get onboarding_feature_social => 'सामाजिक साझाकरण';

  @override
  String get onboarding_feature_personalize => 'व्यक्तिगत अनुभव';

  @override
  String get onboarding_features_button => 'अगला';

  @override
  String get onboarding_theme_title => 'थीम चुनें';

  @override
  String get onboarding_theme_subtitle => 'अपनी पसंदीदा थीम चुनें';

  @override
  String get onboarding_theme_button => 'अगला';

  @override
  String get features_title => 'अपनी सही यात्रा की योजना बनाएं';

  @override
  String get features_subtitle => 'अपनी यात्रा को व्यवस्थित करने के लिए आपको जो कुछ चाहिए वह सब';

  @override
  String get location_search => 'स्मार्ट स्थान खोज';

  @override
  String get location_search_desc => 'आसानी से अपनी यात्रा योजना में स्थान खोजें और जोड़ें';

  @override
  String get itinerary_map => 'इंटरैक्टिव यात्रा मानचित्र';

  @override
  String get itinerary_map_desc => 'एक इंटरैक्टिव मानचित्र पर अपनी पूरी यात्रा मार्ग को देखें';

  @override
  String get expense_tracking => 'खर्च ट्रैकिंग';

  @override
  String get expense_tracking_desc => 'अपने सभी यात्रा खर्चों को ट्रैक और वर्गीकृत करें';

  @override
  String get travel_analytics => 'यात्रा विश्लेषण';

  @override
  String get travel_analytics_desc => 'अपने यात्रा पैटर्न के आंकड़े और ग्राफ देखें';

  @override
  String get trip_attachments => 'यात्रा संलग्नक';

  @override
  String get trip_attachments_desc => 'अपनी यात्राओं में तस्वीरें, दस्तावेज़ और यादें जोड़ें';

  @override
  String get comprehensive_planning => 'व्यापक योजना';

  @override
  String get comprehensive_planning_desc => 'विस्तृत विकल्पों के साथ उड़ानें, होटल, गतिविधियां और अधिक की योजना बनाएं';

  @override
  String get no_attachments_added_yet => 'अभी तक कोई अटैचमेंट नहीं जोड़ा गया है';

  @override
  String get usage_left => 'बाकी';

  @override
  String get usage_details_title => 'उपयोग विवरण';

  @override
  String get unlimited => 'असीमित';

  @override
  String get used => 'प्रयुक्त';

  @override
  String get left => 'बाकी';

  @override
  String get premium_required_message => 'आपने निःशुल्क सीमा पूरी कर ली है। अधिक यात्राएँ बनाने के लिए प्रीमियम में अपग्रेड करें।';

  @override
  String get upgrade => 'अपग्रेड करें';

  @override
  String get itineraries => 'यात्रा कार्यक्रम';

  @override
  String get notification_title_no_trips => 'अपना साहसिक कार्य शुरू करें!';

  @override
  String get notification_body_no_trips => 'अपनी पहली यात्रा बनाएं और अपनी यात्रा की योजना बनाना शुरू करें।';

  @override
  String get notification_title_no_itinerary => 'अपना यात्रा कार्यक्रम बनाएं!';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'अपनी यात्रा \"$tripName\" में एक यात्रा कार्यक्रम जोड़ें।';
  }

  @override
  String get notification_title_no_places => 'घूमने के स्थान जोड़ें!';

  @override
  String notification_body_no_places(Object tripName) {
    return 'अपनी यात्रा \"$tripName\" में घूमने के स्थान जोड़कर उसे और बेहतर बनाएं।';
  }

  @override
  String get notification_title_keep_planning => 'अपनी यात्रा की योजना बनाते रहें!';

  @override
  String notification_body_keep_planning(Object tripName) {
    return '\"$tripName\" के लिए अपनी परफेक्ट यात्रा योजना बनाते रहें।';
  }

  @override
  String get ai_chat_title => 'AI चैट';

  @override
  String get ai_chat_input_hint => 'अपनी यात्रा योजनाओं के बारे में मुझसे पूछें...';

  @override
  String get ai_chat_loading => 'RoamR AI सोच रहा है...';

  @override
  String get ai_chat_welcome => 'नमस्ते! मैं RoamR AI हूं, आपका यात्रा सहायक। मैं आपके अगले रोमांच की योजना बनाने में कैसे मदद कर सकता हूं?';
}
