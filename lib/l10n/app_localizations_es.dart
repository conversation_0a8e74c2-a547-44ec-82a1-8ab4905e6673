// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Spanish Castilian (`es`).
class AppLocalizationsEs extends AppLocalizations {
  AppLocalizationsEs([String locale = 'es']) : super(locale);

  @override
  String get language => 'Idioma';

  @override
  String get language_desc => 'Cambia tu idioma preferido';

  @override
  String get all => 'Todos';

  @override
  String get grocery => 'Supermercado';

  @override
  String get food => 'Comida';

  @override
  String get work => 'Trabajo';

  @override
  String get entertainment => 'Entretenimiento';

  @override
  String get traveling => 'Viajes';

  @override
  String get other => 'Otro';

  @override
  String get total_expenses => 'Gastos Totales';

  @override
  String get select_catagory => 'Seleccionar Categoría';

  @override
  String get category => 'Categoría';

  @override
  String get your_trips => 'Tus Viajes';

  @override
  String get no_trips => 'Aún no hay viajes.';

  @override
  String get add_trip => 'Añadir Viaje';

  @override
  String get edit_trip => 'Editar Viaje';

  @override
  String get title => 'Título';

  @override
  String get enter_title => 'Ingresa el nombre del viaje';

  @override
  String get date_range => '¿Cuándo viajarás?';

  @override
  String get select_date_range => 'Por favor selecciona un rango de fechas';

  @override
  String get country => 'País';

  @override
  String get select_country => 'Selecciona un país';

  @override
  String get country_hint => '¿A dónde vas?';

  @override
  String get city => 'Ciudad';

  @override
  String get city_hint => 'Ingresa una ciudad';

  @override
  String get select_city => 'Selecciona una ciudad';

  @override
  String get failed_to_load_trips => 'Error al cargar los viajes';

  @override
  String get past_trips => 'Viajes Pasados';

  @override
  String get trip_name => 'Nombre del Viaje';

  @override
  String get trip_name_hint => 'ej. Vacaciones de Verano 2023';

  @override
  String get amount => 'Cantidad';

  @override
  String get date => 'Fecha';

  @override
  String get departure_date => 'Fecha de Salida';

  @override
  String get edit_itinerary => 'Editar Itinerario';

  @override
  String get add_itinerary => 'Añadir Itinerario';

  @override
  String get itinerary_details => 'Detalles del Itinerario';

  @override
  String get location => 'Ubicación';

  @override
  String get location_hint => 'Ej: 123 Calle Principal, Ciudad, País';

  @override
  String get pick_location => 'Seleccionar ubicación';

  @override
  String get update_location => 'Actualizar ubicación';

  @override
  String get itinerary_name => '¿De qué se trata?';

  @override
  String get itinerary_name_hint => 'ej. Día 1: Llegada y Tour por la Ciudad';

  @override
  String get confirmDeletion => 'Confirmar Eliminación';

  @override
  String get confirmDeletionMessage => '¿Estás seguro de que quieres eliminar este elemento?';

  @override
  String get cancel => 'Cancelar';

  @override
  String get delete => 'Eliminar';

  @override
  String get restaurant => 'Restaurante';

  @override
  String get accommodation => 'Alojamiento';

  @override
  String get transportation => 'Transporte';

  @override
  String get sightseeing => 'Turismo';

  @override
  String get shopping => 'Compras';

  @override
  String get activity => 'Actividad';

  @override
  String get parking => 'Estacionamiento';

  @override
  String get note => 'Nota';

  @override
  String get movie => 'Película';

  @override
  String get flight => 'Vuelo';

  @override
  String get carRental => 'Alquiler de Coche';

  @override
  String get flight_number => 'Número de Vuelo';

  @override
  String get flight_number_hint => 'ej. AA123';

  @override
  String get airline_name => 'Nombre de Aerolínea';

  @override
  String get airline_name_hint => 'ej. Aerolíneas Americanas';

  @override
  String get home => 'Inicio';

  @override
  String get trips => 'Viajes';

  @override
  String get saved => 'Marcadores';

  @override
  String get accommodations => 'Alojamientos';

  @override
  String get pro => 'Próximamente';

  @override
  String get account => 'Cuenta';

  @override
  String get settings => 'Ajustes';

  @override
  String get favorites => 'Marcadores';

  @override
  String get unlock_premium => 'RoamR Premium';

  @override
  String get premium_subtitle => 'Lleva tu planificación de viajes al siguiente nivel';

  @override
  String get coming_soon => 'Próximamente';

  @override
  String get ai_trip_planner => 'Planificador de Viajes IA';

  @override
  String get ai_trip_planner_desc => 'Obtén sugerencias de viajes personalizadas e itinerarios impulsados por IA';

  @override
  String get trip_gallery => 'Galería de Viajes';

  @override
  String get trip_gallery_desc => 'Adjunta y organiza fotos, documentos y recuerdos para cada viaje';

  @override
  String get receipt_scanner => 'Escáner de Recibos';

  @override
  String get receipt_scanner_desc => 'Rastrea automáticamente los gastos escaneando tus facturas y recibos';

  @override
  String get interactive_maps => 'Mapas Interactivos';

  @override
  String get interactive_maps_desc => 'Visualiza toda tu ruta de viaje con mapas interactivos y navegación';

  @override
  String get trip_sharing => 'Compartir Viajes';

  @override
  String get trip_sharing_desc => 'Colabora y planifica viajes con amigos y familiares';

  @override
  String get guest_user => 'Inicia sesión en tu cuenta';

  @override
  String get account_settings => 'Configuración de Cuenta';

  @override
  String get edit_profile => 'Editar Perfil';

  @override
  String get profile => 'Perfil';

  @override
  String get profile_updated_successfully => 'Perfil actualizado con éxito';

  @override
  String get save => 'Guardar';

  @override
  String get sign_out_confirmation => '¿Estás seguro de que quieres cerrar sesión?';

  @override
  String get please_enter_your_name => 'Por favor ingresa tu nombre';

  @override
  String get failed_to_load_profile => 'Error al cargar los datos del perfil';

  @override
  String get notifications => 'Notificaciones';

  @override
  String get notifications_desc => 'Administra tus preferencias de notificaciones';

  @override
  String get preferences => 'Preferencias';

  @override
  String get theme => 'Tema';

  @override
  String get light => 'Claro';

  @override
  String get currency => 'Moneda';

  @override
  String get currency_desc => 'Establece tu moneda preferida';

  @override
  String get about => 'Acerca de';

  @override
  String get about_app => 'Acerca de RoamR';

  @override
  String get rate_app => 'Calificar App';

  @override
  String get privacy_policy => 'Política de Privacidad';

  @override
  String get sign_in => 'Iniciar Sesión';

  @override
  String get sign_out => 'Cerrar Sesión';

  @override
  String get register => 'Registrarse';

  @override
  String get create_account => 'Crear Cuenta';

  @override
  String get continue_with_google => 'Continuar con Google';

  @override
  String get continue_with_apple => 'Continuar con Apple';

  @override
  String get or => 'o';

  @override
  String get email => 'Correo Electrónico';

  @override
  String get password => 'Contraseña';

  @override
  String get confirm_password => 'Confirmar Contraseña';

  @override
  String get name => 'Nombre';

  @override
  String get dont_have_account => '¿No tienes una cuenta?';

  @override
  String get already_have_account => '¿Ya tienes una cuenta?';

  @override
  String get signed_out_successfully => 'Sesión cerrada con éxito';

  @override
  String get date_range_example => 'ej. 25 Ene - 16 Feb';

  @override
  String get add_place => 'Añadir lugares que quieres visitar';

  @override
  String get restaurant_name => 'Nombre del Restaurante';

  @override
  String get restaurant_name_hint => 'ej. Palacio de Sushi';

  @override
  String get accommodation_name => 'Nombre del Alojamiento';

  @override
  String get accommodation_name_hint => 'ej. Hotel Hilton';

  @override
  String get transportation_name => 'Nombre del Transporte';

  @override
  String get transportation_name_hint => 'ej. Tren a Tokio';

  @override
  String get sightseeing_name => 'Nombre del Lugar';

  @override
  String get sightseeing_hint => 'ej. Torre de Tokio';

  @override
  String get shopping_name => 'Nombre de la Tienda';

  @override
  String get shopping_hint => 'ej. Centro Comercial Shibuya';

  @override
  String get activity_name => 'Nombre de la Actividad';

  @override
  String get activity_hint => 'ej. Clase de Cocina';

  @override
  String get parking_name => 'Ubicación de Estacionamiento';

  @override
  String get parking_hint => 'ej. Estacionamiento Central';

  @override
  String get note_name => 'Título de la Nota';

  @override
  String get note_hint => 'ej. Recordatorios Importantes';

  @override
  String get movie_name => 'Nombre de Película/Show';

  @override
  String get movie_hint => 'ej. Show de Teatro Local';

  @override
  String get flight_title => 'Detalles del Vuelo';

  @override
  String get flight_title_hint => 'ej. Vuelo de Tokio a Osaka';

  @override
  String get car_rental_name => 'Detalles del Alquiler de Coche';

  @override
  String get car_rental_hint => 'ej. Toyota Camry - Hertz';

  @override
  String get amount_optional => 'Cantidad (si aplica)';

  @override
  String get checkin_date => 'Fecha de Entrada';

  @override
  String get checkout_date => 'Fecha de Salida';

  @override
  String get stay_dates => 'Fechas de Estancia';

  @override
  String get select_checkin_checkout => 'Selecciona fechas de entrada y salida';

  @override
  String get start_date => 'Fecha de Inicio';

  @override
  String get end_date => 'Fecha de Fin';

  @override
  String get description => 'Descripción';

  @override
  String get description_hint => 'Añade notas adicionales o detalles';

  @override
  String get about_desc => 'Aprende más sobre la aplicación';

  @override
  String get system => 'Sistema';

  @override
  String get edit => 'Editar';

  @override
  String get dark => 'Oscuro';

  @override
  String get date_required => 'Fecha requerida';

  @override
  String get date_within_trip => 'La fecha debe estar dentro del período del viaje';

  @override
  String get no_favorites => 'Aún no hay marcadores';

  @override
  String get add_favorites_hint => 'Tus itinerarios marcados aparecerán aquí';

  @override
  String get premium_title => 'RoamR Premium';

  @override
  String get premium => 'Premium';

  @override
  String get premium_features => 'Desbloquea Funciones Premium';

  @override
  String get unlimited_trip_planning => 'Planificación de Viajes Ilimitada';

  @override
  String get unlimited_trip_planning_desc => 'Crea y gestiona viajes ilimitados';

  @override
  String get interactive_map => 'Mapas de Viaje Interactivos';

  @override
  String get interactive_map_desc => 'Visualiza todo tu itinerario en mapas interactivos con navegación';

  @override
  String get best_value => 'MEJOR VALOR';

  @override
  String get yearly => 'Anual';

  @override
  String get monthly => 'Mensual';

  @override
  String get per_year => 'por año';

  @override
  String get per_month => 'por mes';

  @override
  String get save_fifty => 'Ahorra 50%';

  @override
  String get restore_purchases => 'Restaurar Compras';

  @override
  String get subscription_details => 'Detalles de Suscripción';

  @override
  String get subscription_status => 'Estado:';

  @override
  String get subscription_plan => 'Plan:';

  @override
  String get subscription_purchased => 'Comprado:';

  @override
  String get subscription_expires => 'Vence:';

  @override
  String get subscription_renews => 'Se renueva:';

  @override
  String get subscription_error => 'No se pudieron cargar los detalles de la suscripción';

  @override
  String get loading_premium_options => 'Cargando opciones premium...';

  @override
  String get premium_features_unlocked => '¡Funciones premium desbloqueadas!';

  @override
  String get premium_user_access => 'Eres un usuario premium con acceso a todas las funciones';

  @override
  String get no_locations => 'No hay itinerarios con datos de ubicación';

  @override
  String get map_not_available => 'Mapa No Disponible';

  @override
  String get add_location_details => 'No hay ubicaciones para mostrar en el mapa. Primero añade detalles de ubicación a tus itinerarios.';

  @override
  String get ok => 'Aceptar';

  @override
  String get location_permission_denied => 'Permiso de ubicación denegado';

  @override
  String get location_permission_permanently_denied => 'Permiso de ubicación denegado permanentemente';

  @override
  String get location_error => 'Error al obtener la ubicación';

  @override
  String get delete_account => 'Eliminar Cuenta';

  @override
  String get delete_account_confirmation => '¿Eliminar Cuenta?';

  @override
  String get delete_account_message => '¿Estás seguro de que deseas eliminar tu cuenta? Esta acción no se puede deshacer y todos tus datos se eliminarán permanentemente.';

  @override
  String get account_deleted_successfully => 'Cuenta eliminada con éxito';

  @override
  String get premium_login_message => 'Inicia sesión para acceder a las funciones premium';

  @override
  String get share_app => 'Compartir App';

  @override
  String get terms_of_service => 'Términos de Servicio';

  @override
  String get send_feedback => 'Enviar Comentarios';

  @override
  String get statistics => 'Estadísticas';

  @override
  String get select_trip => 'Seleccionar viaje';

  @override
  String get select_trip_to_view_statistics => 'Selecciona un viaje para ver estadísticas';

  @override
  String get no_expenses_to_display => 'No hay gastos para mostrar';

  @override
  String get expense_over_time => 'Gastos a lo largo del tiempo';

  @override
  String get expenses_by_category => 'Gastos por categoría';

  @override
  String get total => 'Total';

  @override
  String get category_distribution => 'Distribución por categoría';

  @override
  String get remove_from_favorites => 'Eliminar de Marcadores';

  @override
  String get remove => 'Eliminar';

  @override
  String get remove_from_favorites_confirmation => '¿Eliminar de Marcadores?';

  @override
  String get remove_from_favorites_message => '¿Estás seguro de que deseas eliminar este itinerario de tus marcadores?';

  @override
  String get pie_chart => 'Gráfico circular';

  @override
  String get bar_chart => 'Gráfico de barras';

  @override
  String get trip_summary => 'Resumen de viaje';

  @override
  String get total_trips => 'Total de viajes';

  @override
  String get total_places_visited => 'Lugares visitados';

  @override
  String get avg_places_per_trip => 'Lugares/viaje';

  @override
  String get total_days_traveled => 'Días viajados';

  @override
  String get shortest_trip => 'Viaje más corto';

  @override
  String get longest_trip => 'Viaje más largo';

  @override
  String get day => 'día';

  @override
  String get days => 'días';

  @override
  String get flights => 'Vuelos';

  @override
  String get most_frequent => 'Actividad más frecuente';

  @override
  String get most_activities_day => 'Más actividades/día';

  @override
  String get avg_activities_day => 'Promedio actividades/día';

  @override
  String get avg_spend_per_trip => 'Gasto promedio/viaje';

  @override
  String get most_expensive_trip => 'Viaje más caro';

  @override
  String get least_expensive_trip => 'Viaje más económico';

  @override
  String get top_expense_category => 'Categoría más costosa';

  @override
  String get achievements => 'Logros';

  @override
  String get achievements_description => '¡Sigue tus hitos de viaje y desbloquea logros mientras exploras el mundo!';

  @override
  String get destination_achievements => 'Logros de Destino';

  @override
  String get expense_achievements => 'Logros de Gastos';

  @override
  String get frequency_achievements => 'Logros de Frecuencia de Viajes';

  @override
  String get duration_achievements => 'Logros de Duración de Viajes';

  @override
  String get flight_achievements => 'Logros de Vuelos';

  @override
  String get novice_traveller_title => 'Viajero Novato';

  @override
  String get novice_traveller_desc => 'Visita 3 o más países diferentes';

  @override
  String get world_traveller_title => 'Viajero Mundial';

  @override
  String get world_traveller_desc => 'Visita 10 o más países diferentes';

  @override
  String get globetrotter_title => 'Trotamundos';

  @override
  String get globetrotter_desc => 'Visita 20 o más países diferentes';

  @override
  String get continental_explorer_title => 'Explorador Continental';

  @override
  String get continental_explorer_desc => 'Visita 2 o más continentes y al menos 20 países';

  @override
  String get continental_collector_title => 'Coleccionista Continental';

  @override
  String get continental_collector_desc => 'Visita 4 o más continentes y al menos 25 países';

  @override
  String get world_conqueror_title => 'Conquistador Mundial';

  @override
  String get world_conqueror_desc => 'Visita los 7 continentes y al menos 30 países';

  @override
  String get budget_tracker_title => 'Rastreador de Presupuesto';

  @override
  String get budget_tracker_desc => 'Registra \$1,000+ en gastos totales';

  @override
  String get expense_manager_title => 'Gestor de Gastos';

  @override
  String get expense_manager_desc => 'Registra \$5,000+ en gastos totales';

  @override
  String get financial_voyager_title => 'Viajero Financiero';

  @override
  String get financial_voyager_desc => 'Registra \$10,000+ en gastos totales';

  @override
  String get luxury_traveller_title => 'Viajero de Lujo';

  @override
  String get luxury_traveller_desc => 'Registra \$20,000+ en gastos totales';

  @override
  String get travel_beginner_title => 'Principiante en Viajes';

  @override
  String get travel_beginner_desc => 'Completa 3 o más viajes';

  @override
  String get travel_enthusiast_title => 'Entusiasta de Viajes';

  @override
  String get travel_enthusiast_desc => 'Completa 10 o más viajes';

  @override
  String get travel_addict_title => 'Adicto a los Viajes';

  @override
  String get travel_addict_desc => 'Completa 20 o más viajes';

  @override
  String get day_tripper_title => 'Excursionista de un Día';

  @override
  String get day_tripper_desc => 'Completa un viaje de al menos 1 día';

  @override
  String get weekend_wanderer_title => 'Viajero de Fin de Semana';

  @override
  String get weekend_wanderer_desc => 'Completa un viaje de al menos 3 días';

  @override
  String get vacation_voyager_title => 'Viajero de Vacaciones';

  @override
  String get vacation_voyager_desc => 'Completa un viaje de al menos 7 días';

  @override
  String get extended_explorer_title => 'Explorador Extendido';

  @override
  String get extended_explorer_desc => 'Completa un viaje de al menos 14 días';

  @override
  String get long_term_traveler_title => 'Viajero de Larga Duración';

  @override
  String get long_term_traveler_desc => 'Completa un viaje de al menos 30 días';

  @override
  String get nomadic_adventurer_title => 'Aventurero Nómada';

  @override
  String get nomadic_adventurer_desc => 'Completa un viaje de al menos 60 días';

  @override
  String get first_flight_title => 'Principiante de Vuelo';

  @override
  String get first_flight_desc => 'Registra 5 o más vuelos';

  @override
  String get frequent_flyer_title => 'Viajero Frecuente';

  @override
  String get frequent_flyer_desc => 'Registra 15 o más vuelos';

  @override
  String get aviation_enthusiast_title => 'Entusiasta de la Aviación';

  @override
  String get aviation_enthusiast_desc => 'Registra 30 o más vuelos';

  @override
  String get progress => 'Progreso';

  @override
  String get close => 'Cerrar';

  @override
  String get complete_trips_for_achievements => '¡Completa viajes para desbloquear logros!';

  @override
  String get view_all => 'Ver Todo';

  @override
  String get app_tagline => 'Tu compañero de viaje';

  @override
  String get website => 'Sitio Web';

  @override
  String get facebook => 'Facebook';

  @override
  String get twitter => 'Twitter/X';

  @override
  String get instagram => 'Instagram';

  @override
  String get feedback_email_subject => 'Comentarios sobre RoamR';

  @override
  String get feedback_email_body => 'Me gustaría proporcionar comentarios sobre RoamR:';

  @override
  String get share_app_message_prefix => 'Descubre RoamR, una aplicación de planificación de viajes: ';

  @override
  String get share_app_subject => 'RoamR - Tu compañero de viaje';

  @override
  String could_not_launch_url(Object url) {
    return 'No se pudo abrir $url';
  }

  @override
  String get could_not_share_app => 'No se pudo compartir la aplicación';

  @override
  String get could_not_launch_email => 'No se pudo abrir el cliente de correo electrónico';

  @override
  String copyright(Object year) {
    return '© $year RoamR';
  }

  @override
  String get photos => 'Fotos';

  @override
  String get no_photos_added => 'Aún no se han añadido fotos';

  @override
  String get gallery => 'Galería';

  @override
  String get camera => 'Cámara';

  @override
  String get error_loading_image => 'Error al cargar la imagen';

  @override
  String get unsupported_attachment_type => 'Tipo de adjunto no compatible';

  @override
  String get file_not_found => 'Archivo no encontrado';

  @override
  String get see_more => 'Ver más';

  @override
  String get select_category => 'Seleccionar categoría';

  @override
  String get done => 'Listo';

  @override
  String get search_activities_and_places => 'Buscar actividades y lugares';

  @override
  String get no_results_found => 'No se encontraron resultados';

  @override
  String get travel => 'Viaje';

  @override
  String get food_drink => 'Comida y bebida';

  @override
  String get art_fun => 'Arte y diversión';

  @override
  String get search_results => 'Resultados de búsqueda';

  @override
  String get trip_photos => 'Fotos del Viaje';

  @override
  String get error_loading_photos => 'Error al cargar las fotos';

  @override
  String get no_photos_for_trip => 'No hay fotos para este viaje';

  @override
  String get no_audio_for_trip => 'No hay grabaciones de audio para este viaje';

  @override
  String get no_documents_for_trip => 'No hay documentos para este viaje';

  @override
  String get no_attachments_for_trip => 'No hay archivos adjuntos para este viaje';

  @override
  String get audio => 'Audio';

  @override
  String get documents => 'Documentos';

  @override
  String get open => 'Abrir';

  @override
  String get opening_file => 'Abriendo archivo';

  @override
  String get select_document => 'Seleccionar documento';

  @override
  String get error_opening_file => 'Error al abrir el archivo';

  @override
  String get no_content_available => 'No hay contenido disponible para mostrar';

  @override
  String get error_loading_document => 'Error al cargar el documento';

  @override
  String get size => 'Tamaño';

  @override
  String get modified => 'Modificado';

  @override
  String get word_document => 'El contenido del documento Word no se puede mostrar directamente';

  @override
  String get excel_spreadsheet => 'El contenido de la hoja de cálculo Excel no se puede mostrar directamente';

  @override
  String get file_type_not_supported => 'Este tipo de archivo no se puede mostrar directamente';

  @override
  String get file_available_at => 'El archivo está disponible en';

  @override
  String get attachments => 'Adjuntos';

  @override
  String get add_attachment => 'Añadir adjunto';

  @override
  String get choose_attachment_type => 'Elegir tipo de adjunto';

  @override
  String get voucher_code => 'Código de Cupón';

  @override
  String get enter_voucher_code => 'Ingresar Código de Cupón';

  @override
  String get enter_voucher_code_hint => 'Ingresa tu código de cupón';

  @override
  String get please_enter_voucher_code => 'Por favor ingresa un código de cupón';

  @override
  String get voucher_code_applied => '¡Código de cupón aplicado con éxito!';

  @override
  String get invalid_voucher_code => 'Código de cupón inválido. Por favor intenta de nuevo.';

  @override
  String get apply => 'Aplicar';

  @override
  String get night => 'noche';

  @override
  String get nights => 'noches';

  @override
  String get verify_email => 'Verificar correo electrónico';

  @override
  String get verification_code_sent => 'Se ha enviado un código de verificación a';

  @override
  String get verification_code => 'Código de verificación';

  @override
  String get please_enter_verification_code => 'Por favor, introduce el código de verificación';

  @override
  String get verification_code_too_short => 'El código de verificación es demasiado corto';

  @override
  String get verify => 'Verificar';

  @override
  String get resend_code => 'Reenviar código';

  @override
  String please_wait_before_resend(Object seconds) {
    return 'Por favor, espera $seconds segundos antes de solicitar un nuevo código';
  }

  @override
  String get please_enter_name => 'Por favor, introduce tu nombre';

  @override
  String get please_enter_email => 'Por favor, introduce tu correo electrónico';

  @override
  String get please_enter_valid_email => 'Por favor, introduce un correo electrónico válido';

  @override
  String get verification_code_login => 'Enviaremos un código de verificación a tu correo electrónico';

  @override
  String get verification_code_register => 'Enviaremos un código de verificación a tu correo electrónico para completar el registro';

  @override
  String get continue_with_email => 'Continuar con correo electrónico';

  @override
  String get register_with_email => 'Registrarse con correo electrónico';

  @override
  String get error_invalid_email => 'El correo electrónico no tiene un formato correcto.';

  @override
  String get error_user_disabled => 'Esta cuenta de usuario ha sido desactivada.';

  @override
  String get error_user_not_found => 'Esta cuenta no existe o ha sido eliminada.';

  @override
  String get error_invalid_credentials => 'Correo electrónico o contraseña incorrectos.';

  @override
  String get error_email_exists => 'El correo electrónico ya está siendo utilizado por otra cuenta.';

  @override
  String get error_weak_password => 'La contraseña es demasiado débil.';

  @override
  String get error_email_not_registered => 'Este correo electrónico no está registrado. Por favor, regístrese primero.';

  @override
  String get error_otp_disabled => 'La verificación OTP está desactivada. Por favor, intente otro método.';

  @override
  String get error_unknown => 'Ha ocurrido un error desconocido. Por favor, inténtelo de nuevo más tarde.';

  @override
  String get account_information => 'Información de la cuenta';

  @override
  String get account_actions => 'Acciones de la cuenta';

  @override
  String get enter_your_name => 'Introduce tu nombre';

  @override
  String get onboarding_welcome_title => 'Bienvenido a RoamR';

  @override
  String get onboarding_welcome_subtitle => 'Tu compañero de viaje personal';

  @override
  String get onboarding_welcome_button => 'Comenzar';

  @override
  String get onboarding_select_language => 'Seleccionar Idioma';

  @override
  String get onboarding_features_title => 'Características';

  @override
  String get onboarding_feature_maps => 'Mapas Interactivos';

  @override
  String get onboarding_feature_calendar => 'Calendario de Viajes';

  @override
  String get onboarding_feature_social => 'Compartir en Redes Sociales';

  @override
  String get onboarding_feature_personalize => 'Experiencia Personalizada';

  @override
  String get onboarding_features_button => 'Siguiente';

  @override
  String get onboarding_theme_title => 'Elegir Tema';

  @override
  String get onboarding_theme_subtitle => 'Selecciona tu tema preferido';

  @override
  String get onboarding_theme_button => 'Siguiente';

  @override
  String get features_title => 'Planifica tu viaje perfecto';

  @override
  String get features_subtitle => 'Todo lo que necesitas para organizar tu viaje';

  @override
  String get location_search => 'Búsqueda inteligente de ubicaciones';

  @override
  String get location_search_desc => 'Encuentra y añade lugares a tu itinerario fácilmente';

  @override
  String get itinerary_map => 'Mapa interactivo del itinerario';

  @override
  String get itinerary_map_desc => 'Visualiza toda tu ruta de viaje en un mapa interactivo';

  @override
  String get expense_tracking => 'Seguimiento de gastos';

  @override
  String get expense_tracking_desc => 'Registra y categoriza todos tus gastos de viaje';

  @override
  String get travel_analytics => 'Análisis de viajes';

  @override
  String get travel_analytics_desc => 'Visualiza estadísticas y gráficos de tus patrones de viaje';

  @override
  String get trip_attachments => 'Archivos adjuntos del viaje';

  @override
  String get trip_attachments_desc => 'Adjunta fotos, documentos y recuerdos a tus viajes';

  @override
  String get comprehensive_planning => 'Planificación integral';

  @override
  String get comprehensive_planning_desc => 'Planifica vuelos, hoteles, actividades y más con opciones detalladas';

  @override
  String get no_attachments_added_yet => 'Aún no se han añadido archivos adjuntos';

  @override
  String get usage_left => 'restante';

  @override
  String get usage_details_title => 'Detalles de uso';

  @override
  String get unlimited => 'Ilimitado';

  @override
  String get used => 'Usado';

  @override
  String get left => 'restante';

  @override
  String get premium_required_message => 'Has alcanzado el límite gratuito. Actualiza a premium para crear más viajes.';

  @override
  String get upgrade => 'Actualizar';

  @override
  String get itineraries => 'Itineraries';

  @override
  String get notification_title_no_trips => '¡Comienza tu aventura!';

  @override
  String get notification_body_no_trips => 'Crea tu primer viaje y comienza a planificar tu aventura.';

  @override
  String get notification_title_no_itinerary => '¡Planifica tu itinerario!';

  @override
  String notification_body_no_itinerary(Object tripName) {
    return 'Agrega un itinerario a tu viaje \"$tripName\".';
  }

  @override
  String get notification_title_no_places => '¡Agrega lugares para visitar!';

  @override
  String notification_body_no_places(Object tripName) {
    return 'Mejora tu viaje \"$tripName\" agregando lugares para visitar.';
  }

  @override
  String get notification_title_keep_planning => '¡Sigue planificando tu viaje!';

  @override
  String notification_body_keep_planning(Object tripName) {
    return 'Continúa construyendo el itinerario perfecto para \"$tripName\".';
  }
}
