import 'dart:ui' show PlatformDispatcher;

import 'package:flutter/foundation.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_performance/firebase_performance.dart';
import 'package:flutter/material.dart';
import 'package:roamr/config/api_keys.dart';
import 'package:roamr/firebase/analytics_service.dart';
import 'package:roamr/firebase/firebase_options.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'trip_planner_app.dart';
import 'package:roamr/di/service_locator.dart';
import 'package:roamr/services/onboarding_assets_service.dart';
import 'package:roamr/services/common_prompt_service.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    // Enable debug mode for Firebase Analytics
    await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    if (kDebugMode) {
      // Enable debug logging for Firebase Analytics
      await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
      await FirebaseAnalytics.instance.setSessionTimeoutDuration(const Duration(minutes: 30));
      
      // Log the app instance ID for debugging
      final appInstanceId = await FirebaseAnalytics.instance.appInstanceId;
      debugPrint('Firebase Analytics App Instance ID: $appInstanceId');
    }
    
    // Initialize Firebase Performance Monitoring
    await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
    
    // Initialize Firebase Crashlytics
    FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;
    PlatformDispatcher.instance.onError = (error, stack) {
      FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
      return true;
    };
    
    // Enable offline persistence for Firestore (if you use it in the future)
    // await FirebaseFirestore.instance.enablePersistence();
  } catch (e) {
    debugPrint('Failed to initialize Firebase: $e');
    rethrow; // Rethrow to fail fast in development
  }

  // Initialize Supabase using credentials from ApiKeys class
  await Supabase.initialize(
    url: ApiKeys.supabaseUrl,
    anonKey: ApiKeys.supabaseAnonKey,
  );

  // Initialize OnboardingAssetsService after Supabase
  OnboardingAssetsService.initialize(supabaseClient: Supabase.instance.client);

  // Initialize RevenueCat after Supabase
  final revenueCatService = RevenueCatService();
  await revenueCatService.initialize();
  
  // Initialize Analytics Service
  final analyticsService = AnalyticsService.instance;
  
  // Set user properties if user is already logged in
  final currentUser = Supabase.instance.client.auth.currentUser;
  if (currentUser != null) {
    analyticsService.setUserProperty(
      name: 'user_id', 
      value: currentUser.id,
    );
    if (currentUser.email != null) {
      analyticsService.setUserProperty(
        name: 'email', 
        value: currentUser.email,
      );
    }
  }

  // Check if user is already logged in and identify them with RevenueCat
  final supabaseUser = Supabase.instance.client.auth.currentUser;
  if (supabaseUser != null) {
    // User is logged in, identify them with RevenueCat using their email
    final email = supabaseUser.email;
    if (email != null) {
      await revenueCatService.identifyUser(email);
    }
  }

  // Initialize service locator before running the app
  await setupServiceLocator();

  // Load prompts from Supabase into SharedPreferences
  try {
    final promptService = getIt<CommonPromptService>();
    await promptService.loadAllPrompts();
  } catch (e) {
    debugPrint('Failed to load prompts: $e');
  }

  runApp(TripPlannerApp());
}
