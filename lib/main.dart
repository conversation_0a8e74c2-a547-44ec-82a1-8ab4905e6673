import 'package:flutter/material.dart';
import 'package:roamr/config/api_keys.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'trip_planner_app.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize RevenueCat before running the app
  final revenueCatService = RevenueCatService();
  await revenueCatService.initialize();

  // Initialize Supabase using credentials from ApiKeys class
  await Supabase.initialize(
    url: ApiKeys.supabaseUrl,
    anonKey: ApiKeys.supabaseAnonKey,
  );

  // Check if user is already logged in and identify them with RevenueCat
  final currentUser = Supabase.instance.client.auth.currentUser;
  if (currentUser != null) {
    // User is logged in, identify them with RevenueCat using their email
    final email = currentUser.email;
    if (email != null) {
      debugPrint('User already logged in, identifying with RevenueCat: $email');
      await revenueCatService.identifyUser(email);
    }
  }

  runApp(TripPlannerApp());
}
