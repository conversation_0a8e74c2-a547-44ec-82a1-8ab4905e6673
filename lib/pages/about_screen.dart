import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:roamr/constants/app_links.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:roamr/widgets/themed_background.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:roamr/utils/rating_utils.dart';
import 'dart:ui';

class AboutScreen extends StatefulWidget {
  const AboutScreen({super.key});

  @override
  State<AboutScreen> createState() => _AboutScreenState();
}

class _AboutScreenState extends State<AboutScreen> {
  String _version = '';
  String _buildNumber = '';
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPackageInfo();
  }

  Future<void> _loadPackageInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      if (mounted) {
        setState(() {
          _version = packageInfo.version;
          _buildNumber = packageInfo.buildNumber;
          _isLoading = false;
        });
      }
    } catch (e) {
      // Handle MissingPluginException or any other errors
      // Error loading package info
      if (mounted) {
        setState(() {
          _version = '1.0.0'; // Default version
          _buildNumber = '1';  // Default build number
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _launchURL(String url) async {
    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        final errorMessage = 'Could not launch $url'; // Fallback message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
          ),
        );
      }
    }
  }

  Future<void> _shareApp() async {
    final localizations = AppLocalizations.of(context)!;
    try {
      // Use localized strings with concatenation
      final String shareMessage = localizations.share_app_message_prefix + AppLinks.appUrl;
      final String shareSubject = localizations.share_app_subject;

      await Share.share(
        shareMessage,
        subject: shareSubject,
      );
    } catch (e) {
      // Error sharing app
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.could_not_share_app)),
        );
      }
    }
  }

  Future<void> _rateApp() async {
    if (Theme.of(context).platform == TargetPlatform.iOS) {
      await RatingUtils.requestReview();
    } else {
      await _launchURL(AppLinks.androidAppStore);
    }
  }

  Future<void> _sendFeedback() async {
    final localizations = AppLocalizations.of(context)!;
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: AppLinks.feedbackEmail,
      query: 'subject=${Uri.encodeComponent(localizations.feedback_email_subject)}&body=${Uri.encodeComponent(localizations.feedback_email_body)}',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.could_not_launch_email),
          ),
        );
      }
    }
  }

  Widget _buildSocialMediaLinks() {
    final localizations = AppLocalizations.of(context)!;
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Website is not available for now
        // _buildSocialButton(
        //   icon: FontAwesomeIcons.globe,
        //   onTap: () => _launchURL(AppLinks.website),
        //   tooltip: localizations.website,
        // ),
        // Facebook is not available for now
        // _buildSocialButton(
        //   icon: FontAwesomeIcons.facebookF,
        //   onTap: () => _launchURL(AppLinks.facebook),
        //   tooltip: localizations.facebook,
        // ),
        _buildSocialButton(
          icon: FontAwesomeIcons.xTwitter,
          onTap: () => _launchURL(AppLinks.twitter),
          tooltip: localizations.twitter,
        ),
        _buildSocialButton(
          icon: FontAwesomeIcons.instagram,
          onTap: () => _launchURL(AppLinks.instagram),
          tooltip: localizations.instagram,
        ),
      ],
    );
  }

  Widget _buildSocialButton({
    required IconData icon,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Tooltip(
        message: tooltip,
        child: Material(
          color: isDarkMode
              ? theme.cardColor.withAlpha(150)
              : theme.colorScheme.primary.withAlpha(15),
          borderRadius: BorderRadius.circular(30),
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(30),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: FaIcon(
                icon,
                size: 24,
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSettingItem({
    required IconData icon,
    required String title,
    String? subtitle,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(30),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Container(
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(150),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListTile(
                leading: Icon(icon, color: theme.colorScheme.primary),
                title: Text(title),
                subtitle: subtitle != null ? Text(subtitle) : null,
                trailing: const Icon(Icons.chevron_right),
                onTap: onTap,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        appBar: AppBar(
          title: Text(localizations.about),
          backgroundColor: ThemedBackground.getAppBarColor(context),
          elevation: 0,
        ),
        body: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : Center(
                child: ConstrainedBox(
                  constraints: const BoxConstraints(maxWidth: 600), // Set maximum width for tablet mode
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // App logo
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Image.asset(
                        'assets/banner.png',
                        width: 120,
                        height: 60,
                      ),
                    ),
      
                    // App name and version
                    const SizedBox(height: 8),
                    Text(
                      'Version $_version ($_buildNumber)',
                      style: theme.textTheme.bodyMedium,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      localizations.app_tagline,
                      style: theme.textTheme.bodyLarge,
                      textAlign: TextAlign.center,
                    ),
      
                    const SizedBox(height: 24),
      
                    // Social media links
                    _buildSocialMediaLinks(),
      
                    const SizedBox(height: 32),
      
                    // Action items
                    _buildSettingItem(
                      icon: Icons.privacy_tip_outlined,
                      title: localizations.privacy_policy,
                      onTap: () => _launchURL(AppLinks.privacyPolicy),
                    ),
                    _buildSettingItem(
                      icon: Icons.feedback_outlined,
                      title: localizations.send_feedback,
                      onTap: _sendFeedback,
                    ),
                    _buildSettingItem(
                      icon: Icons.star_outline,
                      title: localizations.rate_app,
                      onTap: _rateApp,
                    ),
                    _buildSettingItem(
                      icon: Icons.share_outlined,
                      title: localizations.share_app,
                      onTap: _shareApp,
                    ),
                    _buildSettingItem(
                      icon: Icons.description_outlined,
                      title: localizations.terms_of_service,
                      onTap: () => _launchURL(AppLinks.termsOfService),
                    ),
      
                    const SizedBox(height: 24),
      
                    // Copyright
                    Text(
                      '© ${DateTime.now().year} RoamR',
                      style: theme.textTheme.bodySmall,
                    ),
                    const SizedBox(height: 40),
                  ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}
