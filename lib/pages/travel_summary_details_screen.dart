import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/l10n/app_localizations.dart';


class TravelSummaryDetailsScreen extends StatefulWidget {
  const TravelSummaryDetailsScreen({super.key});

  @override
  State<TravelSummaryDetailsScreen> createState() => _TravelSummaryDetailsScreenState();
}

class _TravelSummaryDetailsScreenState extends State<TravelSummaryDetailsScreen> {
  // Statistics data
  int _totalTrips = 0;
  int _totalPlacesVisited = 0;
  double _avgPlacesPerTrip = 0;
  double _avgActivitiesPerDay = 0;
  int _totalDaysTraveled = 0;
  int _totalFlights = 0;
  String _mostFrequentActivity = '';
  int _mostActivitiesInDay = 0;
  DateTime? _busiestDay;
  Trip? _shortestTrip;
  Trip? _longestTrip;
  double _totalExpenses = 0;
  double _avgSpendPerTrip = 0;
  Trip? _mostExpensiveTrip;
  double _mostExpensiveTripCost = 0;
  Trip? _leastExpensiveTrip;
  double _leastExpensiveTripCost = 0;

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all trips from the AddTripBloc
      final tripsState = context.read<AddTripBloc>().state;
      final trips = tripsState.trips;

      if (trips.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Calculate total trips
      _totalTrips = trips.length;

      // Calculate total places visited (itineraries)
      _totalPlacesVisited = 0;
      _totalFlights = 0;
      _mostActivitiesInDay = 0;
      _totalExpenses = 0;
      int tripsProcessed = 0;
      Map<String, int> activityCounts = {};
      Map<DateTime, int> activitiesByDay = {};
      Map<String, double> expensesByCategory = {};
      Map<String, double> tripExpenses = {};

      for (final trip in trips) {
        final repository = context.read<TripRepository>();
        repository.getTripItineraries(trip.id).listen((itineraries) {
          setState(() {
            _totalPlacesVisited += itineraries.length;

            // Count flights and activities
            for (final itinerary in itineraries) {
              // Count flights
              if (itinerary.category.name == 'flight') {
                _totalFlights++;
              }

              // Count activities by category
              final categoryName = itinerary.category.name;
              activityCounts[categoryName] = (activityCounts[categoryName] ?? 0) + 1;

              // Count activities by day (using date without time)
              final activityDate = DateTime(itinerary.date.year, itinerary.date.month, itinerary.date.day);
              activitiesByDay[activityDate] = (activitiesByDay[activityDate] ?? 0) + 1;

              // Sum expenses by category
              if (itinerary.amount != null && itinerary.amount! > 0) {
                expensesByCategory[categoryName] = (expensesByCategory[categoryName] ?? 0) + itinerary.amount!;
                _totalExpenses += itinerary.amount!;
              }
            }

            // Find most frequent activity
            if (activityCounts.isNotEmpty) {
              _mostFrequentActivity = activityCounts.entries
                  .reduce((a, b) => a.value > b.value ? a : b)
                  .key;
            }

            // Find day with most activities
            if (activitiesByDay.isNotEmpty) {
              final busiestDayEntry = activitiesByDay.entries
                  .reduce((a, b) => a.value > b.value ? a : b);
              _mostActivitiesInDay = busiestDayEntry.value;
              _busiestDay = busiestDayEntry.key;
            }

            // Calculate average places per trip
            _avgPlacesPerTrip = _totalTrips > 0 ? _totalPlacesVisited / _totalTrips : 0;

            // Calculate average activities per day
            _avgActivitiesPerDay = _totalDaysTraveled > 0 ? activityCounts.values.fold(0, (sum, count) => sum + count) / _totalDaysTraveled : 0;

            // Calculate average spend per trip
            _avgSpendPerTrip = _totalTrips > 0 ? _totalExpenses / _totalTrips : 0;

            // Track most and least expensive trips
            double tripTotalExpense = itineraries.fold(0, (sum, itinerary) => sum + (itinerary.amount ?? 0));
            tripExpenses[trip.id] = tripTotalExpense;

            // Update most expensive trip
            if (_mostExpensiveTrip == null || tripTotalExpense > _mostExpensiveTripCost) {
              _mostExpensiveTrip = trip;
              _mostExpensiveTripCost = tripTotalExpense;
            }

            // Update least expensive trip (only if it has expenses)
            if (tripTotalExpense > 0 && (_leastExpensiveTrip == null || tripTotalExpense < _leastExpensiveTripCost)) {
              _leastExpensiveTrip = trip;
              _leastExpensiveTripCost = tripTotalExpense;
            }

            // Mark as loaded when all trips are processed
            tripsProcessed++;
            if (tripsProcessed >= trips.length) {
              _isLoading = false;
            }
          });
        });
      }

      // Calculate total days traveled, shortest and longest trips
      _totalDaysTraveled = 0;
      _shortestTrip = trips.first;
      _longestTrip = trips.first;

      for (final trip in trips) {
        // Calculate days for this trip
        final tripDays = trip.endDate.difference(trip.startDate).inDays + 1;
        _totalDaysTraveled += tripDays;

        // Check if this is the shortest trip
        final shortestDays = _shortestTrip!.endDate.difference(_shortestTrip!.startDate).inDays + 1;
        if (tripDays < shortestDays) {
          _shortestTrip = trip;
        }

        // Check if this is the longest trip
        final longestDays = _longestTrip!.endDate.difference(_longestTrip!.startDate).inDays + 1;
        if (tripDays > longestDays) {
          _longestTrip = trip;
        }
      }

      setState(() {});
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    final colorScheme = theme.colorScheme;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(
          localizations.trip_summary,
          style: theme.textTheme.titleLarge?.copyWith(
            color: CustomTheme.getPrimaryTextColor(context),
          ),
        ),
        backgroundColor: theme.colorScheme.primary.withAlpha(20),
        elevation: 0,
        iconTheme: IconThemeData(
          color: CustomTheme.getPrimaryTextColor(context),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withAlpha(20),
        ),
        width: double.infinity,
        height: double.infinity,
        child: BlocListener<AddTripBloc, AddTripState>(
          listener: (context, state) {
            if (state.status == AddTripStatus.success) {
              _loadStatistics();
            }
          },
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: EdgeInsets.fromLTRB(16.0, MediaQuery.of(context).padding.top + 56.0, 16.0, 16.0),
                  child: _buildAllStatsContent(context, theme, colorScheme, localizations),
                ),
        ),
      ),
    );
  }

  Widget _buildAllStatsContent(BuildContext context, ThemeData theme, ColorScheme colorScheme, AppLocalizations localizations) {

    return Column(
      children: [
        // All stats in a single grid
        _buildStatsGrid([
          // Trip stats
          _buildStatItem(
            context,
            Icons.card_travel,
            _totalTrips.toString(),
            localizations.total_trips,
            Colors.teal, // Teal
          ),

          // Places stats
          _buildStatItem(
            context,
            Icons.place,
            _totalPlacesVisited.toString(),
            localizations.total_places_visited,
            Colors.orange, // Orange
          ),

          // Flight stats
          _buildStatItem(
            context,
            Icons.flight,
            _totalFlights.toString(),
            localizations.flights,
            Colors.purple, // Purple
          ),

          // Average places stats
          _buildStatItem(
            context,
            Icons.analytics_outlined,
            _avgPlacesPerTrip.toStringAsFixed(1),
            localizations.avg_places_per_trip,
            Colors.blue, // Blue
          ),

          // Days traveled stats
          _buildStatItem(
            context,
            Icons.calendar_month,
            _totalDaysTraveled.toString(),
            localizations.total_days_traveled,
            Colors.indigo, // Indigo
          ),

          // Activity stats
          _buildStatItem(
            context,
            Icons.directions_run,
            _mostFrequentActivity,
            localizations.most_frequent,
            Colors.deepOrange, // Deep Orange
          ),
          _buildStatItem(
            context,
            Icons.star,
            _mostActivitiesInDay.toString(),
            localizations.most_activities_day,
            Colors.pink, // Pink
          ),
          _buildStatItem(
            context,
            Icons.bar_chart,
            _avgActivitiesPerDay.toStringAsFixed(1),
            localizations.avg_activities_day,
            Colors.cyan, // Cyan
          ),

          // Expense stats
          _buildStatItem(
            context,
            Icons.attach_money,
            CurrencyUtils.formatTotalExpenses(context, _totalExpenses),
            localizations.total_expenses,
            Colors.red, // Red
          ),
          _buildStatItem(
            context,
            Icons.attach_money,
            CurrencyUtils.formatTotalExpenses(context, _avgSpendPerTrip),
            localizations.avg_spend_per_trip,
            Colors.green, // Green
          ),

          // Trip expense stats
          if (_mostExpensiveTrip != null)
            _buildTripStatItem(
              context,
              Icons.trending_up,
              _mostExpensiveTrip!.title,
              localizations.most_expensive_trip,
              Colors.amber, // Amber
              CurrencyUtils.formatTotalExpenses(context, _mostExpensiveTripCost),
            ),
          if (_leastExpensiveTrip != null)
            _buildTripStatItem(
              context,
              Icons.trending_down,
              _leastExpensiveTrip!.title,
              localizations.least_expensive_trip,
              Colors.lightBlue, // Light Blue
              CurrencyUtils.formatTotalExpenses(context, _leastExpensiveTripCost),
            ),

          // Trip duration stats
          if (_shortestTrip != null)
            _buildTripStatItem(
              context,
              Icons.timelapse,
              _shortestTrip!.title,
              localizations.shortest_trip,
              Colors.deepPurple, // Deep Purple
              _getDurationText(_shortestTrip!, localizations),
            ),
          if (_longestTrip != null)
            _buildTripStatItem(
              context,
              Icons.timeline,
              _longestTrip!.title,
              localizations.longest_trip,
              Colors.brown, // Brown
              _getDurationText(_longestTrip!, localizations),
            ),
        ]),
      ],
    );
  }

  Widget _buildStatsGrid(List<Widget> items) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      childAspectRatio: 1.5,
      crossAxisSpacing: 4,
      mainAxisSpacing: 4,
      padding: EdgeInsets.zero,
      children: items,
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTripStatItem(
    BuildContext context,
    IconData icon,
    String title,
    String label,
    Color color,
    String subtitle,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 4),
          Text(
            label,
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getDurationText(Trip trip, AppLocalizations localizations) {
    final days = trip.endDate.difference(trip.startDate).inDays + 1;
    return days == 1
        ? '$days ${localizations.day}'
        : '$days ${localizations.days}';
  }


}
