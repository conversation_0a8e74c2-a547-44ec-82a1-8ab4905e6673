import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/blocs/purchase_bloc/purchase_bloc.dart';

import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/utils/premium_utils.dart';
import 'package:roamr/theme/custom_theme.dart';

class PremiumScreen extends StatefulWidget {
  const PremiumScreen({super.key});

  @override
  State<PremiumScreen> createState() => _PremiumScreenState();
}

class _PremiumScreenState extends State<PremiumScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        title: Text(localizations.premium_title),
        backgroundColor: theme.brightness == Brightness.dark
            ? theme.scaffoldBackgroundColor
            : theme.cardColor,
        elevation: 0,
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main content
          Expanded(
            child: BlocBuilder<PurchaseCubit, PurchaseState>(
              builder: (context, state) {
                if (state is PurchaseLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // Check if user is premium
                final isPremium = state.isPremium;

                if (isPremium) {
                  // User is premium - show subscription details
                  return _buildPremiumUserContent(context);
                } else {
                  // User is not premium - show paywall or login prompt
                  return _buildNonPremiumUserContent(context);
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPremiumUserContent(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context)!;

    return FutureBuilder<Map<String, String>?>(
      future: RevenueCatService().getSubscriptionDetails(),
      builder: (context, snapshot) {
        return Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600), // Set maximum width for tablet mode
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(
                  'assets/banner.png',
                  height: 120,
                  fit: BoxFit.contain,
                ),
                const SizedBox(height: 24),
                Text(
                  localizations.premium_features_unlocked,
                  style: textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: CustomTheme.getPrimaryTextColor(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  localizations.premium_user_access,
                  style: textTheme.bodyLarge?.copyWith(
                    color: CustomTheme.getSecondaryTextColor(context),
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // Subscription details card
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: colorScheme.secondaryContainer.withAlpha(50),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: colorScheme.secondary.withAlpha(50)),
                  ),
                  child: snapshot.connectionState == ConnectionState.waiting
                    ? const Center(child: CircularProgressIndicator())
                    : snapshot.hasData
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              localizations.subscription_details,
                              style: textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: CustomTheme.getPrimaryTextColor(context),
                              ),
                            ),
                            const SizedBox(height: 16),
                            _buildSubscriptionDetailRow(
                              context,
                              localizations.subscription_status,
                              snapshot.data!['status'] ?? "Active",
                              Icons.check_circle,
                              colorScheme.secondary,
                            ),
                            const SizedBox(height: 8),
                            _buildSubscriptionDetailRow(
                              context,
                              localizations.subscription_plan,
                              "${snapshot.data!['product'] ?? "Premium"} ${snapshot.data!['plan_type'] != null ? "(${snapshot.data!['plan_type']})" : ""}",
                              Icons.star,
                              colorScheme.secondary,
                            ),
                            if (snapshot.data!['purchased'] != null) ...[
                              const SizedBox(height: 8),
                              _buildSubscriptionDetailRow(
                                context,
                                localizations.subscription_purchased,
                                snapshot.data!['purchased'] ?? "",
                                Icons.date_range,
                                colorScheme.secondary,
                              ),
                            ],

                            if (snapshot.data!['expires'] != null) ...[
                              const SizedBox(height: 8),
                              _buildSubscriptionDetailRow(
                                context,
                                snapshot.data!['is_recurring'] == 'true'
                                  ? localizations.subscription_renews
                                  : localizations.subscription_expires,
                                snapshot.data!['expires'] ?? "Never",
                                Icons.calendar_today,
                                colorScheme.secondary,
                              ),
                            ],
                          ],
                        )
                      : Text(
                          localizations.subscription_error,
                          style: textTheme.bodyLarge?.copyWith(
                            color: CustomTheme.getSecondaryTextColor(context),
                          ),
                        ),
                ),

                const SizedBox(height: 32),
                FilledButton.icon(
                  onPressed: () async {
                    final revenueCatService = RevenueCatService();
                    await revenueCatService.restorePurchases();
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text(localizations.restore_purchases)),
                      );
                    }
                  },
                  icon: const Icon(Icons.restore),
                  label: Text(localizations.restore_purchases),
                ),
              ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSubscriptionDetailRow(BuildContext context, String label, String value, IconData icon, Color iconColor) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return Row(
      children: [
        Icon(icon, size: 20, color: iconColor),
        const SizedBox(width: 8),
        Text(
          label,
          style: textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: CustomTheme.getPrimaryTextColor(context),
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: textTheme.bodyLarge?.copyWith(
              color: CustomTheme.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.right,
          ),
        ),
      ],
    );
  }

  Widget _buildNonPremiumUserContent(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    return Center(
      child: ConstrainedBox(
        constraints: const BoxConstraints(maxWidth: 600), // Set maximum width for tablet mode
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/banner.png',
              height: 120,
              fit: BoxFit.contain,
            ),
            const SizedBox(height: 24),
            Text(
              localizations.premium_features,
              style: textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: CustomTheme.getPrimaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              localizations.premium_subtitle,
              style: textTheme.bodyLarge?.copyWith(
                color: CustomTheme.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            _buildFeatureItem(
              context,
              Icons.calendar_month,
              localizations.unlimited_trip_planning,
              localizations.unlimited_trip_planning_desc,
            ),
            const SizedBox(height: 16),
            _buildFeatureItem(
              context,
              Icons.map,
              localizations.interactive_map,
              localizations.interactive_map_desc,
            ),
            const SizedBox(height: 32),
            // Create a stateful button with loading indicator
            StatefulBuilder(
              builder: (context, setState) {
                return FilledButton.icon(
                  onPressed: () async {
                    // Show loading state
                    setState(() => _isLoading = true);

                    try {
                      // Use the premium utils to check status and show paywall
                      await PremiumUtils.checkPremiumStatus(context);
                    } finally {
                      // Reset loading state if widget is still mounted
                      if (context.mounted) {
                        setState(() => _isLoading = false);
                      }
                    }
                  },
                  icon: _isLoading
                    ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2))
                    : const Icon(Icons.flight),
                  label: Text(_isLoading ? localizations.loading_premium_options : localizations.unlock_premium),
                );
              },
            ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, IconData icon, String title, String description) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: colorScheme.secondary.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: colorScheme.secondary,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: CustomTheme.getPrimaryTextColor(context),
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: textTheme.bodyMedium?.copyWith(
                  color: CustomTheme.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
