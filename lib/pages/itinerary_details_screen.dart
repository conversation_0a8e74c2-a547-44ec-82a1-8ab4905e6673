import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/pages/add_edit_itinerary_screen.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/utils/category_icons_utils.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_state.dart';
import 'package:roamr/widgets/attachments_section.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:ui';

class ItineraryDetailsScreen extends StatelessWidget {
  const ItineraryDetailsScreen({
    super.key,
    this.trip,
    required this.itinerary,
    this.isFromFavorites = false,
  });

  final Trip? trip;
  final TripItinerary itinerary;
  final bool isFromFavorites;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.itinerary_details),
        actions: [
          // No special actions for favorites screen
          // Edit button - only show if we have a trip context
          if (trip != null)
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () {
                // Capture the navigator before the async gap
                final navigator = Navigator.of(context);

                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => AddEditItineraryScreen(
                      tripId: trip!.id,
                      startDate: trip!.startDate,
                      endDate: trip!.endDate,
                      itinerary: itinerary,
                    ),
                  ),
                ).then((_) {
                  // Pop back to the timeline screen using the captured navigator
                  navigator.pop();
                });
              },
            ),
          const SizedBox(width: 8),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          color: colorScheme.primary.withAlpha(20),
        ),
        width: double.infinity,
        height: double.infinity,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Category info and favorite button
              Padding(
                padding: const EdgeInsets.all(16),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.cardColor.withAlpha(150),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.brightness == Brightness.dark
                              ? Colors.white.withAlpha(30)
                              : Colors.white.withAlpha(150),
                          width: 0.5,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Category row with favorite button
                          Row(
                            children: [
                              // Category icon
                              Container(
                                padding: const EdgeInsets.all(10),
                                decoration: BoxDecoration(
                                  color: colorScheme.primary.withAlpha(25),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  IconUtils.getIconForCategory(itinerary.category),
                                  color: colorScheme.primary,
                                  size: 22,
                                ),
                              ),
                              const SizedBox(width: 12),
                              // Title and category
                              Expanded(
                                flex: 8, // Give more space to the title
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Title
                                    Text(
                                      itinerary.title,
                                      style: textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                    const SizedBox(height: 4),
                                    // Category name (subtle)
                                    Text(
                                      itinerary.category.getName(context),
                                      style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.onSurface.withAlpha(150),
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ],
                                ),
                              ),
                              // Favorite button - only show if not coming from favorites screen
                              if (!isFromFavorites)
                                BlocBuilder<FavoritesBloc, FavoritesState>(
                                  builder: (context, state) {
                                    final isFavorite = state.favoriteStatus[itinerary.id] ?? false;

                                    // Check favorite status when widget is built
                                    if (!state.favoriteStatus.containsKey(itinerary.id)) {
                                      context.read<FavoritesBloc>().add(CheckFavoriteStatus(itinerary.id));
                                    }

                                    return Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(30),
                                        onTap: () {
                                          if (isFavorite) {
                                            context.read<FavoritesBloc>().add(RemoveFromFavorites(itinerary.id));
                                          } else {
                                            context.read<FavoritesBloc>().add(AddToFavorites(itinerary));
                                          }
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Icon(
                                            isFavorite ? Icons.bookmark : Icons.bookmark_border,
                                            color: isFavorite ? theme.colorScheme.primary : Colors.grey,
                                            size: 32,
                                          ),
                                        ),
                                      ),
                                    );
                                  },
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Details section
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: BackdropFilter(
                    filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: theme.cardColor.withAlpha(150),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: theme.brightness == Brightness.dark
                              ? Colors.white.withAlpha(30)
                              : Colors.white.withAlpha(150),
                          width: 0.5,
                        ),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Section title
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              AppLocalizations.of(context)!.itinerary_details,
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                          // Date
                          _buildSimpleDetailItem(
                            context,
                            AppLocalizations.of(context)!.date,
                            DateFormat.yMMMMd().format(itinerary.date),
                          ),

                          // Checkout date for accommodation
                          if (itinerary.category == Category.accommodation &&
                              itinerary.checkoutDate != null)
                            _buildSimpleDetailItem(
                              context,
                              AppLocalizations.of(context)!.checkout_date,
                              DateFormat.yMMMMd().format(itinerary.checkoutDate!),
                            ),

                          // Location placeholder - we'll show it near the map instead
                          // if location exists but no map coordinates
                          if (itinerary.locationText != null &&
                              itinerary.locationText!.isNotEmpty &&
                              itinerary.location == null)
                            _buildSimpleDetailItem(
                              context,
                              AppLocalizations.of(context)!.location,
                              itinerary.locationText!,
                            ),

                          // Airline name for flights
                          if (itinerary.airlineName != null &&
                              itinerary.airlineName!.isNotEmpty)
                            _buildSimpleDetailItem(
                              context,
                              AppLocalizations.of(context)!.airline_name,
                              itinerary.airlineName!,
                            ),

                          // Flight number for flights
                          if (itinerary.flightNumber != null &&
                              itinerary.flightNumber!.isNotEmpty)
                            _buildSimpleDetailItem(
                              context,
                              AppLocalizations.of(context)!.flight_number,
                              itinerary.flightNumber!,
                            ),

                          // Description
                          if (itinerary.description != null &&
                              itinerary.description!.isNotEmpty)
                            _buildSimpleDetailItem(
                              context,
                              AppLocalizations.of(context)!.description,
                              itinerary.description!,
                            ),

                          // Amount
                          if (itinerary.amount != null && itinerary.amount! > 0)
                            Padding(
                              padding: const EdgeInsets.only(bottom: 12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.amount,
                                    style: textTheme.titleSmall?.copyWith(
                                      color: colorScheme.onSurface.withAlpha(153),
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  const SizedBox(height: 6),
                                  Text(
                                    NumberFormat.currency(
                                      symbol: CurrencyUtils.getCurrencySymbol(context),
                                    ).format(itinerary.amount),
                                    style: textTheme.titleLarge?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: colorScheme.primary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              // Attachments section
              FutureBuilder<List<AttachmentModel>>(
                future: context.read<AttachmentRepository>().getAttachmentsByItineraryId(itinerary.id),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  final attachments = snapshot.data ?? [];

                  if (attachments.isEmpty) {
                    return const SizedBox.shrink();
                  }

                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: AttachmentsSection(
                      savedAttachments: attachments,
                      itineraryTitle: itinerary.title,
                      isEditMode: false,
                    ),
                  );
                },
              ),

              // Map section if location exists
              if (itinerary.location != null)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.cardColor.withAlpha(150),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: theme.brightness == Brightness.dark
                                ? Colors.white.withAlpha(30)
                                : Colors.white.withAlpha(150),
                            width: 0.5,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Section title
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16),
                              child: Text(
                                AppLocalizations.of(context)!.location,
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: colorScheme.primary,
                                ),
                              ),
                            ),

                            SizedBox(
                              height: 200,
                              child: GoogleMap(
                                initialCameraPosition: CameraPosition(
                                  target: itinerary.location!,
                                  zoom: 15,
                                ),
                                markers: {
                                  Marker(
                                    markerId: MarkerId(itinerary.id),
                                    position: itinerary.location!,
                                    icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueGreen),
                                    infoWindow: InfoWindow(
                                      title: itinerary.title,
                                      snippet: itinerary.locationText,
                                    ),
                                  ),
                                },
                                myLocationEnabled: false,
                                myLocationButtonEnabled: false,
                                zoomControlsEnabled: false,
                                mapToolbarEnabled: false,
                                compassEnabled: false,
                                rotateGesturesEnabled: false,
                                scrollGesturesEnabled: true,
                                tiltGesturesEnabled: false,
                                zoomGesturesEnabled: true,
                              ),
                            ),

                            // Show location text if available (below map)
                            if (itinerary.locationText != null && itinerary.locationText!.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 12),
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.location_on,
                                      color: Colors.grey,
                                      size: 16,
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        itinerary.locationText!,
                                        style: textTheme.bodyMedium?.copyWith(
                                          color: Colors.grey,
                                          fontWeight: FontWeight.w400,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  // Simple detail item without icon
  Widget _buildSimpleDetailItem(
    BuildContext context,
    String label,
    String value,
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.onSurface.withAlpha(153),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }
}
