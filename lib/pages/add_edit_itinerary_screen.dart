import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_event.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_state.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/currency_utils.dart';

import 'package:roamr/widgets/attachments_section.dart';
import 'package:roamr/widgets/base_form_field.dart';
import 'package:roamr/widgets/category_selection_widget.dart';
import 'package:roamr/widgets/date_field_widget.dart';
import 'package:roamr/widgets/date_range_controller.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'dart:ui';
import 'package:roamr/widgets/map_picker_widget.dart';
import 'package:roamr/utils/location_utils.dart';
import 'package:roamr/widgets/table_calendar_date_range_picker.dart';

class AddEditItineraryScreen extends StatefulWidget {
  const AddEditItineraryScreen({
    super.key,
    required this.tripId,
    required this.startDate,
    required this.endDate,
    this.itinerary,
  });

  final String tripId;
  final DateTime startDate;
  final DateTime endDate;
  final TripItinerary? itinerary;

  @override
  State<AddEditItineraryScreen> createState() => _AddEditItineraryScreenState();
}

class _AddEditItineraryScreenState extends State<AddEditItineraryScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController titleController = TextEditingController();
  final TextEditingController airlineNameController = TextEditingController();
  final TextEditingController flightNumberController = TextEditingController();
  final DateRangeController accommodationDateRangeController = DateRangeController();

  @override
  void initState() {
    super.initState();
    // Initialize controllers if editing an existing itinerary
    if (widget.itinerary != null) {
      titleController.text = widget.itinerary!.title;
      airlineNameController.text = widget.itinerary!.airlineName ?? '';
      flightNumberController.text = widget.itinerary!.flightNumber ?? '';

      // Initialize date range for accommodation
      if (widget.itinerary!.category == Category.accommodation) {
        DateTime startDate;
        DateTime endDate;

        // If we have existing dates from the itinerary, use them
        if (widget.itinerary!.checkoutDate != null) {
          startDate = widget.itinerary!.date;
          endDate = widget.itinerary!.checkoutDate!;

          // Make sure dates are within trip bounds
          if (startDate.isBefore(widget.startDate)) {
            startDate = widget.startDate;
          }
          if (startDate.isAfter(widget.endDate)) {
            startDate = widget.endDate;
          }

          if (endDate.isBefore(widget.startDate)) {
            endDate = widget.startDate.add(const Duration(days: 1));
          }
          if (endDate.isAfter(widget.endDate)) {
            endDate = widget.endDate;
          }

          // Ensure end date is after start date
          if (!endDate.isAfter(startDate)) {
            DateTime nextDay = startDate.add(const Duration(days: 1));
            endDate = nextDay.isAfter(widget.endDate) ? widget.endDate : nextDay;
          }
        } else {
          // For new accommodation itineraries, use first day of trip as default
          startDate = widget.startDate;

          // Set end date to day after start date, or trip end date if that would be out of bounds
          DateTime nextDay = startDate.add(const Duration(days: 1));
          endDate = nextDay.isAfter(widget.endDate) ? widget.endDate : nextDay;
        }

        accommodationDateRangeController.setDateRange(startDate, endDate);
      }
    }
  }

  @override
  void dispose() {
    titleController.dispose();
    airlineNameController.dispose();
    flightNumberController.dispose();
    accommodationDateRangeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditMode = widget.itinerary != null;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return BlocProvider(
      create: (context) => ItineraryFormBloc(
        tripId: widget.tripId,
        initialItinerary: widget.itinerary,
        repository: context.read<TripRepository>(),
        attachmentRepository: context.read<AttachmentRepository>(),
      ),
      child: BlocBuilder<ItineraryFormBloc, ItineraryFormState>(
        builder: (context, state) {
          return Scaffold(
            appBar: AppBar(
              title: Text(
                isEditMode
                  ? AppLocalizations.of(context)!.edit_itinerary
                  : AppLocalizations.of(context)!.add_itinerary
              ),
              actions: [
                if (state.status != ItineraryFormStatus.loading)
                  TextButton(
                    onPressed: () {
                      if (_formKey.currentState?.validate() ?? false) {
                        // Get the current state to ensure we have the latest data
                        final bloc = context.read<ItineraryFormBloc>();
                        final state = bloc.state;

                        // If this is an accommodation, make sure the dates from the controller are applied
                        if (state.category == Category.accommodation &&
                            accommodationDateRangeController.startDate != null &&
                            accommodationDateRangeController.endDate != null) {
                          // Update the bloc with the dates from the controller
                          bloc.add(ItineraryDateChanged(accommodationDateRangeController.startDate!));
                          bloc.add(ItineraryCheckoutDateChanged(accommodationDateRangeController.endDate!));
                        }

                        // Submit the form with all current data
                        bloc.add(ItinerarySubmitted());

                        // Return to previous screen
                        Navigator.pop(context);
                      }
                    },
                    child: Text(
                      AppLocalizations.of(context)!.save,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
              ],
            ),
            body: state.status == ItineraryFormStatus.loading
                ? const Center(child: CircularProgressIndicator())
                : Container(
                    width: double.infinity,
                    height: double.infinity,
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withAlpha(20),
                    ),
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Category section
                            _buildCategorySection(context, state),
                            const SizedBox(height: 16),

                            // Details section
                            _buildDetailsSection(context, state),
                            const SizedBox(height: 16),

                            // Location section if needed
                            if (_shouldShowLocationField(state.category))
                              _buildLocationSection(context, state),

                            // Photo attachments section
                            const SizedBox(height: 16),
                            _buildPhotoAttachmentsSection(context, state),
                          ],
                        ),
                      ),
                    ),
                  ),
          );
        },
      ),
    );
  }

  Widget _buildCategorySection(BuildContext context, ItineraryFormState state) {
    final theme = Theme.of(context);
    final isEditMode = widget.itinerary != null;

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.cardColor.withAlpha(150),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.brightness == Brightness.dark
                  ? Colors.white.withAlpha(30)
                  : Colors.white.withAlpha(150),
              width: 0.5,
            ),
          ),
          child: CategorySelectionWidget(
            selectedCategory: state.category,
            isEditMode: isEditMode,
            onCategorySelected: (category) {
              if (isEditMode) return;

              // Clear all text controllers
              titleController.clear();
              airlineNameController.clear();
              flightNumberController.clear();

              // Change the category
              context.read<ItineraryFormBloc>().add(ItineraryCategoryChanged(category));
            },
          ),
        ),
      ),
    );
  }

  Widget _buildDetailsSection(BuildContext context, ItineraryFormState state) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final category = state.category;

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.cardColor.withAlpha(150),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.brightness == Brightness.dark
                  ? Colors.white.withAlpha(30)
                  : Colors.white.withAlpha(150),
              width: 0.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  AppLocalizations.of(context)!.itinerary_details,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.primary,
                  ),
                ),
              ),

              // Date field for non-accommodation categories (moved to top)
              if (category != Category.accommodation) ...[
                DateFieldWidget(
                  label:
                      category == Category.flight
                          ? AppLocalizations.of(context)!.departure_date
                          : AppLocalizations.of(context)!.date,
                  isRequired: true,
                  initialDate: state.date,
                  firstDate: widget.startDate,
                  lastDate: widget.endDate,
                  onDateChanged: (date) {
                    if (date != null) {
                      context.read<ItineraryFormBloc>().add(ItineraryDateChanged(date));
                    }
                  },
                ),
                const SizedBox(height: 16),
              ],

              // Date range for accommodation (moved to top)
              if (category == Category.accommodation) ...[
                Builder(
                  builder: (context) {
                    // Update the controller with current state values
                    DateTime startDate;
                    DateTime endDate;

                    // If we have existing dates, use them
                    if (state.checkoutDate != null) {
                      startDate = state.date;
                      endDate = state.checkoutDate!;
                    } else {
                      // Otherwise use first day of trip as default
                      startDate = widget.startDate;

                      // Set end date to day after start date, or trip end date if that would be out of bounds
                      DateTime nextDay = startDate.add(const Duration(days: 1));
                      endDate = nextDay.isAfter(widget.endDate) ? widget.endDate : nextDay;
                    }

                    accommodationDateRangeController.setDateRange(startDate, endDate);

                    // Date range field with new style
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Label with required indicator
                        RichText(
                          text: TextSpan(
                            text: AppLocalizations.of(context)!.stay_dates,
                            style: textTheme.bodySmall,
                            children: const [
                              TextSpan(
                                text: ' *',
                                style: TextStyle(color: Colors.red),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 8),

                        // Date range selector that looks like a button
                        InkWell(
                          onTap: () async {
                            // Capture the bloc before any async operations
                            final bloc = context.read<ItineraryFormBloc>();

                            // Use the dates from the controller to ensure consistency
                            // between what's displayed and what's selected in the calendar
                            DateTime initialStartDate = accommodationDateRangeController.startDate ?? widget.startDate;
                            DateTime initialEndDate;

                            if (accommodationDateRangeController.endDate != null) {
                              initialEndDate = accommodationDateRangeController.endDate!;
                            } else {
                              // Set end date to day after start date, or trip end date if that would be out of bounds
                              DateTime nextDay = initialStartDate.add(const Duration(days: 1));
                              initialEndDate = nextDay.isAfter(widget.endDate) ? widget.endDate : nextDay;
                            }

                            // Use our new table calendar date range picker with very distinct disabled dates
                            DateTimeRange? picked = await showTableCalendarDateRangePicker(
                              context: context,
                              initialStartDate: initialStartDate,
                              initialEndDate: initialEndDate,
                              firstDate: widget.startDate,
                              lastDate: widget.endDate,
                            );

                            if (picked != null && mounted) {
                              // The date range picker already constrains dates to be within firstDate and lastDate,
                              // but we'll validate again just to be safe
                              DateTime validStartDate = picked.start;
                              DateTime validEndDate = picked.end;

                              // Ensure dates are within trip bounds
                              if (validStartDate.isBefore(widget.startDate)) {
                                validStartDate = widget.startDate;
                              }
                              if (validStartDate.isAfter(widget.endDate)) {
                                validStartDate = widget.endDate;
                              }

                              if (validEndDate.isBefore(widget.startDate)) {
                                validEndDate = widget.startDate.add(const Duration(days: 1));
                              }
                              if (validEndDate.isAfter(widget.endDate)) {
                                validEndDate = widget.endDate;
                              }

                              // Ensure end date is after start date
                              if (validEndDate.isBefore(validStartDate) || validEndDate == validStartDate) {
                                validEndDate = validStartDate.add(const Duration(days: 1));
                                if (validEndDate.isAfter(widget.endDate)) {
                                  validEndDate = widget.endDate;
                                  // If this makes end date <= start date, adjust start date
                                  if (!validEndDate.isAfter(validStartDate)) {
                                    validStartDate = validEndDate.subtract(const Duration(days: 1));
                                    if (validStartDate.isBefore(widget.startDate)) {
                                      validStartDate = widget.startDate;
                                    }
                                  }
                                }
                              }

                              // Update the controller and the bloc
                              accommodationDateRangeController.setDateRange(validStartDate, validEndDate);

                              // Update both check-in and check-out dates in the bloc
                              bloc.add(ItineraryDateChanged(validStartDate));
                              bloc.add(ItineraryCheckoutDateChanged(validEndDate));
                            }
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                            decoration: BoxDecoration(
                              color: theme.brightness == Brightness.dark
                                  ? Colors.black.withAlpha(50)
                                  : Colors.white,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: accommodationDateRangeController.text.isEmpty
                                    ? Colors.red
                                    : colorScheme.onSurface.withAlpha(50),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  accommodationDateRangeController.text.isNotEmpty
                                      ? accommodationDateRangeController.text
                                      : AppLocalizations.of(context)!.select_checkin_checkout,
                                  style: textTheme.bodyLarge?.copyWith(
                                    color: accommodationDateRangeController.text.isNotEmpty
                                        ? colorScheme.onSurface
                                        : theme.hintColor.withAlpha(150),
                                  ),
                                ),
                                Icon(
                                  Icons.date_range,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),

                        // Error message if needed
                        if (accommodationDateRangeController.text.isEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 6, left: 12),
                            child: Text(
                              AppLocalizations.of(context)!.date_required,
                              style: TextStyle(
                                color: Colors.red,
                                fontSize: 12,
                              ),
                            ),
                          ),

                        const SizedBox(height: 16),
                      ],
                    );
                  }
                ),
              ],

              // Title field (moved after date)
              ItineraryFormField(
                label: _getTitleLabel(category, context),
                hint: _getTitleHint(category, context),
                controller: titleController,
                isRequired: true,
                onChanged: (value) {
                  context.read<ItineraryFormBloc>().add(ItineraryTitleChanged(value));
                },
              ),

              // Flight specific fields
              if (category == Category.flight) ...[
                const SizedBox(height: 16),
                ItineraryFormField(
                  label: AppLocalizations.of(context)!.airline_name,
                  hint: AppLocalizations.of(context)!.airline_name_hint,
                  controller: airlineNameController,
                  isRequired: true,
                  onChanged: (value) {
                    context.read<ItineraryFormBloc>().add(
                      ItineraryAirlineNameChanged(value),
                    );
                  },
                ),
                const SizedBox(height: 16),
                ItineraryFormField(
                  label: AppLocalizations.of(context)!.flight_number,
                  hint: AppLocalizations.of(context)!.flight_number_hint,
                  controller: flightNumberController,
                  isRequired: true,
                  onChanged: (value) {
                    context.read<ItineraryFormBloc>().add(
                      ItineraryFlightNumberChanged(value),
                    );
                  },
                ),
              ],

              // Description field for notes and other category
              if (category == Category.note || category == Category.other) ...[
                const SizedBox(height: 16),
                ItineraryFormField(
                  label: AppLocalizations.of(context)!.description,
                  hint: AppLocalizations.of(context)!.description_hint,
                  maxLines: 3,
                  initialValue: state.initialItinerary?.description,
                  onChanged: (value) {
                    context.read<ItineraryFormBloc>().add(
                      ItineraryDescriptionChanged(value),
                    );
                  },
                ),
              ],

              // Amount field for all categories except note
              if (category != Category.note) ...[
                const SizedBox(height: 16),
                ItineraryFormField(
                  label: AppLocalizations.of(context)!.amount_optional,
                  hint: '0.00',
                  initialValue: state.initialItinerary?.amount?.toString(),
                  prefixText: CurrencyUtils.getCurrencySymbol(context),
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  onChanged: (value) {
                    context.read<ItineraryFormBloc>().add(
                      ItineraryAmountChanged(value.isEmpty ? '0' : value),
                    );
                  },
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationSection(BuildContext context, ItineraryFormState state) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.cardColor.withAlpha(150),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.brightness == Brightness.dark
                  ? Colors.white.withAlpha(30)
                  : Colors.white.withAlpha(150),
              width: 0.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  AppLocalizations.of(context)!.location,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: colorScheme.primary,
                  ),
                ),
              ),

              // Location field
              GestureDetector(
                onTap: () async {
                  // Capture the bloc before any async operations
                  final bloc = context.read<ItineraryFormBloc>();

                  final LatLng? selectedLocation = await Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => MapPickerWidget(
                        initialLocation: state.location,
                      ),
                    ),
                  );

                  if (mounted) {
                    if (selectedLocation != null) {
                      // User selected a location
                      final locationDetails = await getLocationDetails(selectedLocation);
                      final locationText = '${locationDetails['name'] ?? ''}, ${locationDetails['address'] ?? ''}';

                      if (mounted) {
                        bloc.add(ItineraryLocationChanged(selectedLocation, locationText));
                      }
                    } else {
                      // User cancelled location selection, don't change anything
                      // This ensures we don't accidentally clear the location
                    }
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  decoration: BoxDecoration(
                    color: colorScheme.surface.withAlpha(180),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.primary.withAlpha(80),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.add_location_alt_outlined,
                        color: colorScheme.primary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        state.location != null
                            ? AppLocalizations.of(context)!.update_location
                            : AppLocalizations.of(context)!.pick_location,
                        style: textTheme.labelMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              // Show a preview of the map if location is set
              if (state.location != null)
                Padding(
                  padding: const EdgeInsets.only(top: 12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 150,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: colorScheme.outline.withAlpha(128),
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: GoogleMap(
                            initialCameraPosition: CameraPosition(
                              target: state.location!,
                              zoom: 15,
                            ),
                            markers: {
                              Marker(
                                markerId: const MarkerId('selected_location'),
                                position: state.location!,
                              ),
                            },
                            zoomControlsEnabled: false,
                            mapToolbarEnabled: false,
                            myLocationButtonEnabled: false,
                            myLocationEnabled: false,
                            scrollGesturesEnabled: false,
                            rotateGesturesEnabled: false,
                            tiltGesturesEnabled: false,
                          ),
                        ),
                      ),
                      // Location text display
                      if (state.locationText.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(top: 8),
                          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(
                            color: colorScheme.surface.withAlpha(150),
                            borderRadius: BorderRadius.circular(6),
                            border: Border.all(
                              color: colorScheme.primary.withAlpha(40),
                              width: 0.5,
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.location_on,
                                size: 14,
                                color: colorScheme.primary,
                              ),
                              const SizedBox(width: 6),
                              Expanded(
                                child: Text(
                                  state.locationText,
                                  style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.onSurface.withAlpha(220),
                                    height: 1.2,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  bool _shouldShowLocationField(Category category) {
    switch (category) {
      case Category.restaurant:
      case Category.accommodation:
      case Category.sightseeing:
      case Category.shopping:
      case Category.parking:
        return true;
      case Category.transportation:
      case Category.flight:
      case Category.note:
        return false;
      case Category.activity:
      case Category.movie:
      case Category.carRental:
        return true; // Optional: you can make these false if you prefer
      default:
        return true;
    }
  }

  // This method is no longer used but kept for reference
  // bool _isLocationRequired(Category category) {
  //   switch (category) {
  //     case Category.restaurant:
  //     case Category.accommodation:
  //     case Category.sightseeing:
  //       return true; // These categories definitely need a location
  //     case Category.shopping:
  //     case Category.activity:
  //     case Category.movie:
  //     case Category.carRental:
  //     case Category.parking: // Moved parking to optional location group
  //       return false; // These categories can optionally have a location
  //     default:
  //       return false;
  //   }
  // }

  String _getTitleLabel(Category category, BuildContext context) {
    switch (category) {
      case Category.restaurant:
        return AppLocalizations.of(context)!.restaurant_name;
      case Category.accommodation:
        return AppLocalizations.of(context)!.accommodation_name;
      case Category.transportation:
        return AppLocalizations.of(context)!.transportation_name;
      case Category.sightseeing:
        return AppLocalizations.of(context)!.sightseeing_name;
      case Category.shopping:
        return AppLocalizations.of(context)!.shopping_name;
      case Category.activity:
        return AppLocalizations.of(context)!.activity_name;
      case Category.parking:
        return AppLocalizations.of(context)!.parking_name;
      case Category.note:
        return AppLocalizations.of(context)!.note_name;
      case Category.movie:
        return AppLocalizations.of(context)!.movie_name;
      case Category.flight:
        return AppLocalizations.of(context)!.flight_title;
      case Category.carRental:
        return AppLocalizations.of(context)!.car_rental_name;
      default:
        return AppLocalizations.of(context)!.itinerary_name;
    }
  }

  String _getTitleHint(Category category, BuildContext context) {
    switch (category) {
      case Category.restaurant:
        return AppLocalizations.of(context)!.restaurant_name_hint;
      case Category.accommodation:
        return AppLocalizations.of(context)!.accommodation_name_hint;
      case Category.transportation:
        return AppLocalizations.of(context)!.transportation_name_hint;
      case Category.sightseeing:
        return AppLocalizations.of(context)!.sightseeing_hint;
      case Category.shopping:
        return AppLocalizations.of(context)!.shopping_hint;
      case Category.activity:
        return AppLocalizations.of(context)!.activity_hint;
      case Category.parking:
        return AppLocalizations.of(context)!.parking_hint;
      case Category.note:
        return AppLocalizations.of(context)!.note_hint;
      case Category.movie:
        return AppLocalizations.of(context)!.movie_hint;
      case Category.flight:
        return AppLocalizations.of(context)!.flight_title_hint;
      case Category.carRental:
        return AppLocalizations.of(context)!.car_rental_hint;
      default:
        return AppLocalizations.of(context)!.itinerary_name_hint;
    }
  }

  Widget _buildPhotoAttachmentsSection(BuildContext context, ItineraryFormState state) {
    // Get the current itinerary title
    final title = widget.itinerary?.title ?? AppLocalizations.of(context)!.itinerary_details;

    return AttachmentsSection(
      savedAttachments: state.savedAttachments,
      pendingAttachments: [...state.pendingPhotoAttachments, ...state.pendingDocumentAttachments],
      itineraryTitle: title,
      isEditMode: true,
      onAddPhoto: (file) => _addPhoto(context, file),
      onAddDocument: (file) => _addDocument(context, file),
      onRemovePhoto: (file) => _removePhoto(context, file),
      onRemoveDocument: (file) => _removeDocument(context, file),
      onRemoveAttachment: (attachment) => _removeAttachment(context, attachment),
    );
  }



  void _addPhoto(BuildContext context, File file) {
    // This method is now called directly with the picked file
    final bloc = context.read<ItineraryFormBloc>();
    bloc.add(ItineraryPhotoAdded(file));
  }

  void _removePhoto(BuildContext context, File file) {
    context.read<ItineraryFormBloc>().add(ItineraryPhotoRemoved(file));
  }

  void _addDocument(BuildContext context, File file) {
    // This method is now called directly with the picked file
    final bloc = context.read<ItineraryFormBloc>();
    bloc.add(ItineraryDocumentAdded(file));
  }

  void _removeDocument(BuildContext context, File file) {
    context.read<ItineraryFormBloc>().add(ItineraryDocumentRemoved(file));
  }

  void _removeAttachment(BuildContext context, AttachmentModel attachment) {
    context.read<ItineraryFormBloc>().add(ItineraryAttachmentRemoved(attachment));
  }
}
