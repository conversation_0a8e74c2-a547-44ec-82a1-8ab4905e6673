import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/currency_bloc/currency_bloc.dart';
import 'package:roamr/blocs/theme_bloc/theme_bloc.dart';
import 'package:roamr/blocs/language_bloc/language_bloc.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/pages/auth/login_register_page.dart';
import 'package:roamr/pages/profile_page.dart';
import 'package:roamr/pages/about_screen.dart';
import 'package:roamr/pages/premium_screen.dart';
import 'package:roamr/pages/badge_example_screen.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/debug_util.dart';
import 'package:roamr/utils/debug_settings_util.dart';
import 'package:roamr/utils/voucher_utils.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/widgets/translucent_dialog.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 60;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: false,
            automaticallyImplyLeading: false,
            backgroundColor: theme.cardColor,
            expandedHeight: 64,
            title:
                _showTitle
                    ? Text(
                      localizations.settings,
                      style: theme.textTheme.titleLarge,
                    )
                    : null,
            titleSpacing: 0,
            flexibleSpace: FlexibleSpaceBar(
              expandedTitleScale: 1.0,
              titlePadding: EdgeInsets.zero,
              background: Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 16,
                    right: 16,
                    bottom: 0,
                  ),
                  child: Text(
                    localizations.settings,
                    style: theme.textTheme.headlineLarge,
                  ),
                ),
              ),
            ),
          ),
          SliverList(
            delegate: SliverChildListDelegate([
              FutureBuilder<Widget>(
                future: _buildAccountSection(context),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(child: CircularProgressIndicator());
                  }
                  return snapshot.data ?? Container();
                },
              ),
              _buildSection(
                context,
                title: AppLocalizations.of(context)!.preferences,
                items: [
                  SettingItem(
                    icon: Icons.language_outlined,
                    title: AppLocalizations.of(context)!.language,
                    subtitle: _getLanguageText(context),
                    onTap: () => _showLanguageDialog(context),
                  ),
                  SettingItem(
                    icon: Icons.color_lens_outlined,
                    title: AppLocalizations.of(context)!.theme,
                    subtitle: _getThemeText(context),
                    onTap: () => _showThemeDialog(context),
                  ),
                  SettingItem(
                    icon: Icons.currency_exchange,
                    title: AppLocalizations.of(context)!.currency,
                    subtitle: _getCurrencyText(context),
                    onTap: () => _showCurrencyDialog(context),
                  ),
                  // Only show premium option for non-Android platforms
                  if (defaultTargetPlatform != TargetPlatform.android)
                    SettingItem(
                      icon: Icons.star_outline,
                      title: AppLocalizations.of(context)!.premium,
                      onTap: () => _navigateToPremiumScreen(context),
                    ),
                  // Voucher code option
                  SettingItem(
                    icon: Icons.card_giftcard,
                    title: AppLocalizations.of(context)!.voucher_code,
                    onTap: () => _showVoucherCodeDialog(context),
                  ),
                  SettingItem(
                    icon: Icons.info_outline,
                    title: AppLocalizations.of(context)!.about,
                    onTap: () => _navigateToAboutScreen(context),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Debug section - only visible in debug mode
              if (DebugUtil.isDebugMode)
                FutureBuilder<Widget>(
                  future: _buildDebugSection(context),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    return snapshot.data ?? Container();
                  },
                ),
              if (DebugUtil.isDebugMode) const SizedBox(height: 120),
            ]),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required List<SettingItem> items,
  }) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              color: CustomTheme.getPrimaryTextColor(context),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          elevation: 8,
          shadowColor: CustomTheme.getShadowColor(context, opacity: 0.5),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children:
                items.map((item) {
                  return Column(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                          color: Theme.of(context).cardColor,
                          borderRadius:
                              items.last == item && items.first == item
                                  ? BorderRadius.circular(12)
                                  : items.first == item
                                  ? const BorderRadius.only(
                                    topLeft: Radius.circular(12),
                                    topRight: Radius.circular(12),
                                  )
                                  : items.last == item
                                  ? const BorderRadius.only(
                                    bottomLeft: Radius.circular(12),
                                    bottomRight: Radius.circular(12),
                                  )
                                  : null,
                          boxShadow: CustomTheme.getCardShadows(context),
                        ),
                        child: ListTile(
                          leading: Icon(
                            item.icon,
                            color: CustomTheme.getPrimaryIconColor(context),
                          ),
                          title: Text(
                            item.title,
                            style: TextStyle(
                              color: CustomTheme.getPrimaryTextColor(context),
                            ),
                          ),
                          subtitle:
                              item.subtitle != null
                                  ? Text(
                                    item.subtitle!,
                                    style: TextStyle(
                                      color: CustomTheme.getSecondaryTextColor(
                                        context,
                                      ),
                                    ),
                                  )
                                  : null,
                          trailing: Icon(
                            Icons.chevron_right,
                            color: CustomTheme.getSecondaryTextColor(context),
                          ),
                          onTap: item.onTap,
                        ),
                      ),
                      if (items.last != item)
                        const Divider(height: 1, indent: 8, endIndent: 16),
                    ],
                  );
                }).toList(),
          ),
        ),
      ],
    );
  }

  String _getThemeText(BuildContext context) {
    final themeMode = context.watch<ThemeBloc>().state.themeMode;
    switch (themeMode) {
      case ThemeMode.system:
        return 'System';
      case ThemeMode.light:
        return AppLocalizations.of(context)!.light;
      case ThemeMode.dark:
        return 'Dark';
    }
  }

  void _showThemeDialog(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) {
        return TranslucentDialog(
          title: AppLocalizations.of(context)!.theme,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                ThemeMode.values.map((themeMode) {
                  return RadioListTile<ThemeMode>(
                    title: Text(_getThemeModeText(context, themeMode)),
                    value: themeMode,
                    groupValue: context.watch<ThemeBloc>().state.themeMode,
                    onChanged: (ThemeMode? newThemeMode) {
                      if (newThemeMode != null) {
                        context.read<ThemeBloc>().add(
                          ChangeThemeEvent(newThemeMode),
                        );
                        Navigator.pop(context);
                      }
                    },
                    activeColor: colorScheme.primary,
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  String _getThemeModeText(BuildContext context, ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return AppLocalizations.of(context)!.system;
      case ThemeMode.light:
        return AppLocalizations.of(context)!.light;
      case ThemeMode.dark:
        return AppLocalizations.of(context)!.dark;
    }
  }

  void _showLanguageDialog(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) {
        return TranslucentDialog(
          title: AppLocalizations.of(context)!.language,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<String>(
                title: const Text('English'),
                value: 'en',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
              RadioListTile<String>(
                title: const Text('Español'),
                value: 'es',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
              RadioListTile<String>(
                title: const Text('हिंदी'),
                value: 'hi',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
              RadioListTile<String>(
                title: const Text('ไทย'),
                value: 'th',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
              RadioListTile<String>(
                title: const Text('Deutsch'),
                value: 'de',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
              RadioListTile<String>(
                title: const Text('Français'),
                value: 'fr',
                groupValue:
                    context.watch<LanguageBloc>().state.locale.languageCode,
                onChanged: (value) {
                  if (value != null) {
                    context.read<LanguageBloc>().add(
                      ChangeLanguageEvent(Locale(value)),
                    );
                    Navigator.pop(context);
                  }
                },
                activeColor: colorScheme.primary,
              ),
            ],
          ),
        );
      },
    );
  }

  String _getLanguageText(BuildContext context) {
    final languageCode =
        context.watch<LanguageBloc>().state.locale.languageCode;
    switch (languageCode) {
      case 'en':
        return 'English';
      case 'es':
        return 'Español';
      case 'hi':
        return 'हिंदी';
      case 'th':
        return 'ไทย';
      case 'de':
        return 'Deutsch';
      case 'fr':
        return 'Français';
      default:
        return 'English';
    }
  }

  void _showCurrencyDialog(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    showDialog(
      context: context,
      builder: (context) {
        final currentCurrency =
            context.watch<CurrencyCubit>().state.selectedCurrency;
        return TranslucentDialog(
          title: AppLocalizations.of(context)!.currency,
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children:
                CurrencyUtils.getAvailableCurrencies().map((currency) {
                  return RadioListTile<String>(
                    title: Text(currency),
                    value: currency,
                    groupValue: currentCurrency,
                    onChanged: (value) {
                      if (value != null) {
                        context.read<CurrencyCubit>().changeCurrency(value);
                        Navigator.pop(context);
                      }
                    },
                    activeColor: colorScheme.primary,
                  );
                }).toList(),
          ),
        );
      },
    );
  }

  String _getCurrencyText(BuildContext context) {
    final selectedCurrency =
        context.watch<CurrencyCubit>().state.selectedCurrency;
    return selectedCurrency;
  }

  // Navigate to the About screen
  void _navigateToAboutScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  // Navigate to the Premium screen
  void _navigateToPremiumScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const PremiumScreen()),
    );
  }

  // Show voucher code dialog
  void _showVoucherCodeDialog(BuildContext context) {
    VoucherUtils.showVoucherCodeDialog(context);
  }

  // Navigate to the Badge Example screen
  void _navigateToBadgeExampleScreen(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BadgeExampleScreen()),
    );
  }

  Future<Widget> _buildDebugSection(BuildContext context) async {
    // Store context values before async operations
    final debugTitle = 'Developer Options';
    final tripRepository = RepositoryProvider.of<TripRepository>(context);

    // Create items list
    final items = <SettingItem>[
      SettingItem(
        icon: Icons.science_outlined,
        title: 'Generate Random Trips',
        subtitle: 'Create 5 trips with 15-20 itineraries each',
        onTap: () => _generateRandomTrips(context, tripRepository),
      ),
      // Achievement Badges Example
      SettingItem(
        icon: Icons.emoji_events_outlined,
        title: 'Achievement Badges',
        onTap: () => _navigateToBadgeExampleScreen(context),
      ),
    ];

    // Check if context is still valid
    if (!context.mounted) return Container();

    // Build and return the section
    return _buildSection(context, title: debugTitle, items: items);
  }

  /// Generate random trips with itineraries
  Future<void> _generateRandomTrips(
    BuildContext context,
    TripRepository tripRepository,
  ) async {
    // Show loading indicator
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Generating random trips...')),
      );
    }

    // Generate random trips
    try {
      final String? error = await DebugSettingsUtil.generateRandomTrips(
        tripRepository,
      );

      // Show appropriate message based on success/failure
      if (context.mounted) {
        if (error == null) {
          // No error means success
          // Reload trips in the AddTripBloc to update the UI
          context.read<AddTripBloc>().add(const LoadTrips());

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Random trips generated successfully'),
            ),
          );
        } else {
          // Show the specific error message
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(error)));
        }

        // Force rebuild
        setState(() {});
      }
    } catch (e) {
      // Show error message for unexpected exceptions
      if (context.mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error generating trips: $e')));
      }
    }
  }

  Future<Widget> _buildAccountSection(BuildContext context) async {
    // Only show account section in debug mode AND only for Android platform
    if (!DebugUtil.isDebugMode && defaultTargetPlatform == TargetPlatform.android) {
      return Container(); // Return empty container when not in debug mode or not on Android
    }

    // Store context values before async operations
    final accountTitle = AppLocalizations.of(context)!.account;
    final guestUserText = AppLocalizations.of(context)!.guest_user;
    final contextRef = context;

    final auth = Auth();
    // Refresh the session to ensure we have the latest user data
    // This is particularly important for Android
    final user = await auth.getLatestUser();

    final items = <SettingItem>[];

    if (user != null) {
      // Get user name from auth user
      String userName = 'User';

      // Try to get display name from user
      if (user.userMetadata != null && user.userMetadata!['name'] != null) {
        userName = user.userMetadata!['name'];
      }
      // If no name in metadata, try to get from email (before the @ symbol)
      else if (user.email != null && user.email!.contains('@')) {
        userName = user.email!.split('@')[0];
      }

      items.add(
        SettingItem(
          icon: Icons.account_circle,
          title: userName,
          onTap: () {
            Navigator.push(
              contextRef,
              MaterialPageRoute(builder: (context) => const ProfilePage()),
            ).then((_) {
              // Force rebuild when returning from profile page
              if (contextRef.mounted) {
                (contextRef as Element).markNeedsBuild();
              }
            });
          },
        ),
      );
    } else {
      items.add(
        SettingItem(
          icon: Icons.account_circle,
          title: guestUserText,
          onTap: () {
            Navigator.push(
              contextRef,
              MaterialPageRoute(
                builder: (context) => const LoginRegisterPage(),
              ),
            ).then((result) {
              // Force rebuild when returning from login page with successful result
              if (result == true && contextRef.mounted) {
                debugPrint('Login successful, rebuilding settings screen');
                (contextRef as Element).markNeedsBuild();
              }
            });
          },
        ),
      );
    }

    if (!contextRef.mounted) {
      return Container(); // Return empty container if context is no longer valid
    }

    return _buildSection(contextRef, title: accountTitle, items: items);
  }
}

class SettingItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final VoidCallback onTap;

  const SettingItem({
    required this.icon,
    required this.title,
    this.subtitle,
    required this.onTap,
  });
}
