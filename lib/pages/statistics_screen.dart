import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/widgets/achievements_widget.dart';
import 'package:roamr/widgets/expense_category_pie_chart_widget.dart';
import 'package:roamr/widgets/expense_chart_widget.dart';
import 'package:roamr/widgets/trip_statistics_widget.dart';
import 'package:roamr/widgets/trip_summary_stats_widget.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 60;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: false,
            automaticallyImplyLeading: false,
            backgroundColor: theme.cardColor,
            expandedHeight: 64,
            title: _showTitle ? Text(
              localizations.statistics,
              style: theme.textTheme.titleLarge,
            ) : null,
            titleSpacing: 0,
            flexibleSpace: FlexibleSpaceBar(
              expandedTitleScale: 1.0,
              titlePadding: EdgeInsets.zero,
              background: Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 16,
                    right: 16,
                    bottom: 8,
                  ),
                  child: Text(
                    localizations.statistics,
                    style: theme.textTheme.headlineLarge?.copyWith(
                      color: CustomTheme.getPrimaryTextColor(context),
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Achievements widget (first widget)
          SliverPadding(
            padding: const EdgeInsets.only(top: 16.0),
            sliver: SliverToBoxAdapter(
              child: AchievementsWidget(),
            ),
          ),

          // Trip summary statistics widget
          SliverPadding(
            padding: const EdgeInsets.only(top: 16.0),
            sliver: SliverToBoxAdapter(
              child: TripSummaryStatsWidget(),
            ),
          ),

          // Pie chart statistics widget
          SliverPadding(
            padding: const EdgeInsets.only(top: 16.0),
            sliver: SliverToBoxAdapter(
              child: TripStatisticsWidget(
                childBuilder: (List<TripItinerary?> itineraries, double totalExpenses, Widget tripDropdown) {
                  return ExpenseCategoryPieChartWidget(
                    itineraries: itineraries,
                    totalExpenses: totalExpenses,
                    tripDropdown: tripDropdown,
                  );
                },
              ),
            ),
          ),

          // Line chart statistics widget
          SliverPadding(
            padding: const EdgeInsets.only(top: 16.0),
            sliver: SliverToBoxAdapter(
              child: TripStatisticsWidget(
                childBuilder: (List<TripItinerary?> itineraries, double totalExpenses, Widget tripDropdown) {
                  return ExpenseChartWidget(
                    itineraries: itineraries,
                    totalExpenses: totalExpenses,
                    tripDropdown: tripDropdown,
                  );
                },
              ),
            ),
          ),

          // Bottom padding
          const SliverToBoxAdapter(
            child: SizedBox(height: 40),
          ),
        ],
      ),
    );
  }


}
