import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/widgets/achievements_widget.dart';
import 'package:roamr/widgets/expense_category_pie_chart_widget.dart';
import 'package:roamr/widgets/expense_chart_widget.dart';
import 'package:roamr/widgets/trip_statistics_widget.dart';
import 'package:roamr/widgets/trip_summary_stats_widget.dart';
import 'package:roamr/widgets/themed_background.dart';

class StatisticsScreen extends StatefulWidget {
  const StatisticsScreen({super.key});

  @override
  State<StatisticsScreen> createState() => _StatisticsScreenState();
}

class _StatisticsScreenState extends State<StatisticsScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  // Add GlobalKeys for each statistics widget
  final GlobalKey<AchievementsWidgetState> _achievementsKey = GlobalKey<AchievementsWidgetState>();
  final GlobalKey<TripSummaryStatsWidgetState> _summaryStatsKey = GlobalKey<TripSummaryStatsWidgetState>();
  final GlobalKey<TripStatisticsWidgetState> _pieChartStatsKey = GlobalKey<TripStatisticsWidgetState>();
  final GlobalKey<TripStatisticsWidgetState> _lineChartStatsKey = GlobalKey<TripStatisticsWidgetState>();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 60;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return ThemedBackground(
      child: NestedScrollView(
        controller: _scrollController,
        headerSliverBuilder: (context, innerBoxIsScrolled) => [
          SliverAppBar(
            pinned: true,
            floating: false,
            automaticallyImplyLeading: false,
            backgroundColor: ThemedBackground.getAppBarColor(context),
            expandedHeight: 64,
            title: _showTitle
                ? Text(
                    localizations.statistics,
                    style: theme.textTheme.titleLarge,
                  )
                : null,
            titleSpacing: 0,
            flexibleSpace: FlexibleSpaceBar(
              expandedTitleScale: 1.0,
              titlePadding: EdgeInsets.zero,
              background: Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 16,
                    right: 16,
                    bottom: 8,
                  ),
                  child: Text(
                    localizations.statistics,
                    style: theme.textTheme.headlineLarge?.copyWith(
                      color: CustomTheme.getPrimaryTextColor(context),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
        body: RefreshIndicator(
          onRefresh: () async {
            await Future.wait([
              _achievementsKey.currentState?.refresh() ?? Future.value(),
              _summaryStatsKey.currentState?.refresh() ?? Future.value(),
              _pieChartStatsKey.currentState?.refresh() ?? Future.value(),
              _lineChartStatsKey.currentState?.refresh() ?? Future.value(),
            ]);
          },
          child: ListView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.zero,
            children: [
              const SizedBox(height: 16),
              AchievementsWidget(key: _achievementsKey),
              TripSummaryStatsWidget(key: _summaryStatsKey),
              const SizedBox(height: 16),
              TripStatisticsWidget(
                key: _pieChartStatsKey,
                childBuilder: (List<TripItinerary?> itineraries, double totalExpenses, Widget tripDropdown) {
                  return ExpenseCategoryPieChartWidget(
                    itineraries: itineraries,
                    totalExpenses: totalExpenses,
                    tripDropdown: tripDropdown,
                  );
                },
              ),
              const SizedBox(height: 16),
              TripStatisticsWidget(
                key: _lineChartStatsKey,
                childBuilder: (List<TripItinerary?> itineraries, double totalExpenses, Widget tripDropdown) {
                  return ExpenseChartWidget(
                    itineraries: itineraries,
                    totalExpenses: totalExpenses,
                    tripDropdown: tripDropdown,
                  );
                },
              ),
              const SizedBox(height: 40),
            ],
          ),
        ),
      ),
    );
  }
}
