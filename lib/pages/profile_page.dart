import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/widgets/themed_background.dart'; // Added import
import 'package:supabase_flutter/supabase_flutter.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  User? _user;
  final Auth _auth = Auth();

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    if (!mounted) return;

    setState(() {
      _isLoading = true;
    });

    try {
      // Refresh the session to ensure we have the latest user data
      // This is particularly important for Android
      _user = await _auth.getLatestUser();

      if (_user != null && mounted) {
        // Get name from user metadata
        if (_user!.userMetadata != null && _user!.userMetadata!['name'] != null) {
          _nameController.text = _user!.userMetadata!['name'];
        }
        // If no name in metadata, use email prefix
        else if (_user!.email != null && _user!.email!.contains('@')) {
          _nameController.text = _user!.email!.split('@')[0];

          // If no name is set yet, let's set it to the email prefix
          // This ensures new users have a display name set
          try {
            await Supabase.instance.client.auth.updateUser(
              UserAttributes(
                data: {
                  'name': _nameController.text,
                },
              ),
            );
            // Refresh user after update
            if (mounted) {
              _user = Supabase.instance.client.auth.currentUser;
            }
          } catch (e) {
            // Silently handle error - not critical if this fails
          }
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = '${AppLocalizations.of(context)!.failed_to_load_profile}: ${e.toString()}';
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_formKey.currentState!.validate() || !mounted) return;

    // Store localized message and scaffold messenger before any async operations
    final successMessage = AppLocalizations.of(context)!.profile_updated_successfully;
    final messenger = ScaffoldMessenger.of(context);

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      if (_user != null) {
        // Update user with new display name
        await Supabase.instance.client.auth.updateUser(
          UserAttributes(
            // Use the proper field for display name
            data: {
              'name': _nameController.text.trim(),
            },
          ),
        );

        // Refresh the user to get updated metadata if still mounted
        if (mounted) {
          _user = await _auth.getLatestUser();

          // Show success message in a post-frame callback
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              messenger.showSnackBar(
                SnackBar(content: Text(successMessage)),
              );
            }
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString();
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    if (_isLoading && _user == null) {
      return ThemedBackground( // Wrapped with ThemedBackground
        child: Scaffold(
          backgroundColor: Colors.transparent, // Changed
          extendBodyBehindAppBar: true, // Added
          appBar: AppBar(
            backgroundColor: ThemedBackground.getAppBarColor(context), // Changed
            elevation: 0,
            title: Text(
              localizations.profile,
              style: textTheme.titleLarge,
            ),
            leading: IconButton(
              icon: Icon(Icons.arrow_back, color: CustomTheme.getPrimaryIconColor(context)),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          body: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return ThemedBackground( // Wrapped with ThemedBackground
      child: Scaffold(
        backgroundColor: Colors.transparent, // Changed
        extendBodyBehindAppBar: true, // Added
        appBar: AppBar(
          backgroundColor: ThemedBackground.getAppBarColor(context), // Changed
          elevation: 0,
          title: Text(
            localizations.profile,
            style: textTheme.titleLarge,
          ),
          leading: IconButton(
            icon: Icon(Icons.arrow_back_ios, color: CustomTheme.getPrimaryIconColor(context)),
            onPressed: () => Navigator.of(context).pop(),
          ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateProfile,
            child: Text(
              localizations.save,
              style: TextStyle(
                color: _isLoading ? colorScheme.secondary.withAlpha(128) : colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 600), // Set maximum width for tablet mode
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // Profile header with avatar and name
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withAlpha(15),
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Column(
                        children: [
                          // Profile picture
                          CircleAvatar(
                            radius: 60,
                            backgroundColor: colorScheme.primary.withAlpha(40),
                            child: Text(
                              _getInitials(),
                              style: TextStyle(
                                fontSize: 42,
                                fontWeight: FontWeight.bold,
                                color: colorScheme.primary,
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // User email
                          Text(
                            _user?.email ?? '',
                            style: textTheme.bodyLarge?.copyWith(
                              color: CustomTheme.getSecondaryTextColor(context),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Account Information Section Header
                    Padding(
                      padding: const EdgeInsets.only(top: 24, bottom: 0),
                      child: Text(
                        localizations.account_information,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: CustomTheme.getPrimaryTextColor(context),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Name field with card styling
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: CustomTheme.getCardShadows(context),
                      ),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(left: 16, top: 16, right: 16),
                            child: Text(
                              localizations.name,
                              style: textTheme.titleSmall?.copyWith(
                                color: CustomTheme.getSecondaryTextColor(context),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: TextFormField(
                              controller: _nameController,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                prefixIcon: const Icon(Icons.person_outline),
                                hintText: localizations.enter_your_name,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return localizations.please_enter_your_name;
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 32),

                    // Account Actions Section Header
                    Padding(
                      padding: const EdgeInsets.only(top: 16, bottom: 16),
                      child: Text(
                        localizations.account_actions,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: CustomTheme.getPrimaryTextColor(context),
                        ),
                      ),
                    ),

                    // Sign out button
                    Container(
                      decoration: BoxDecoration(
                        color: theme.cardColor,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: CustomTheme.getCardShadows(context),
                      ),
                      margin: const EdgeInsets.only(bottom: 16),
                      child: ListTile(
                        leading: Icon(
                          Icons.logout,
                          color: CustomTheme.getPrimaryIconColor(context),
                        ),
                        title: Text(localizations.sign_out),
                        trailing: Icon(
                          Icons.arrow_forward_ios,
                          size: 16,
                          color: CustomTheme.getSecondaryIconColor(context),
                        ),
                        onTap: _isLoading ? null : () => _signOut(context),
                      ),
                    ),

                    // Subtle delete account option
                    Align(
                      alignment: Alignment.center,
                      child: Padding(
                        padding: const EdgeInsets.only(top: 32),
                        child: TextButton(
                          onPressed: _isLoading ? null : () => _deleteAccount(context),
                          child: Text(
                            localizations.delete_account,
                            style: TextStyle(
                              color: Colors.red.withAlpha(180),
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    ), // Closes Scaffold
  ); // Closes ThemedBackground
}

  String _getInitials() {
    if (_nameController.text.isEmpty) {
      return _user?.email?.substring(0, 1).toUpperCase() ?? '';
    }

    final nameParts = _nameController.text.split(' ');
    if (nameParts.length > 1) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    }

    return nameParts[0].substring(0, 1).toUpperCase();
  }

  // Simple sign out method that shows a confirmation dialog
  void _signOut(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    // Get the scaffold messenger and navigator before any async operations
    final messenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);
    final message = localizations.signed_out_successfully;

    // Show confirmation dialog using the utility function
    showSignOutConfirmationDialog(context).then((confirmed) {
      if (confirmed == true) {
        // Show loading indicator
        setState(() {
          _isLoading = true;
        });

        // Sign out and navigate back
        _auth.signOut().then((_) {
          if (mounted) {
            // Reset loading state
            setState(() {
              _isLoading = false;
            });

            // Do NOT navigate to root. Just show success message.
            messenger.showSnackBar(
              SnackBar(content: Text(message)),
            );
          }
        }).catchError((error) {
          // Handle error and reset loading state
          if (mounted) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Sign out failed. Please try again.';
            });
          }
        });
      }
    });
  }

  // Delete account method with confirmation dialog
  void _deleteAccount(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    // Get the scaffold messenger and navigator before any async operations
    final messenger = ScaffoldMessenger.of(context);
    final navigator = Navigator.of(context);
    final message = localizations.account_deleted_successfully;

    // Show confirmation dialog
    showDeleteAccountConfirmationDialog(context).then((confirmed) {
      if (confirmed == true) {
        setState(() {
          _isLoading = true;
        });

        // Delete account and navigate back
        _auth.deleteAccount().then((result) {
          if (mounted) {
            setState(() {
              _isLoading = false;
            });

            final (success, errorMsg) = result;

            if (success) {
              // Go back to settings
              navigator.pop();

              // Show success message
              messenger.showSnackBar(
                SnackBar(content: Text(message)),
              );
            } else {
              // Show error message with details if available
              setState(() {
                _errorMessage = errorMsg ?? 'Failed to delete account. Please try again.';
              });

              // Log the error for debugging
              debugPrint('Account deletion failed: $errorMsg');
            }
          }
        });
      }
    });
  }
}
