import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_state.dart';

import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/widgets/trip_timeline_item_tile_widget.dart';
import 'package:roamr/widgets/themed_background.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 60;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return BlocBuilder<FavoritesBloc, FavoritesState>(
      builder: (context, state) {
        // Load favorites when the screen is first shown
        if (state.status == FavoritesStatus.initial) {
          context.read<FavoritesBloc>().add(const LoadFavorites());
          return const ThemedBackground(
            child: Center(child: CircularProgressIndicator()),
          );
        }

        if (state.status == FavoritesStatus.loading) {
          return const ThemedBackground(
            child: Center(child: CircularProgressIndicator()),
          );
        }

        final favoriteItineraries = state.favorites;

        return ThemedBackground(
          child: CustomScrollView(
            controller: _scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            slivers: [
              SliverAppBar(
                pinned: true,
                floating: false,
                automaticallyImplyLeading: false,
                backgroundColor: ThemedBackground.getAppBarColor(context),
                expandedHeight: 64,
                title: _showTitle ? Text(
                  localizations.saved,
                  style: theme.textTheme.titleLarge,
                ) : null,
                titleSpacing: 0,
                flexibleSpace: FlexibleSpaceBar(
                  expandedTitleScale: 1.0,
                  titlePadding: EdgeInsets.zero,
                  background: Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: 16,
                        left: 16,
                        right: 16,
                        bottom: 0,
                      ),
                      child: Text(
                        localizations.saved,
                        style: theme.textTheme.headlineLarge,
                      ),
                    ),
                  ),
                ),
              ),

              if (favoriteItineraries.isEmpty)
                SliverFillRemaining(
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.bookmark_border,
                          size: 64,
                          color: CustomTheme.getSecondaryIconColor(context),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          "No saved items yet",
                          style: theme.textTheme.titleLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 32),
                          child: Text(
                            "Your saved itineraries will appear here",
                            style: theme.textTheme.bodyMedium,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                )
              else if (state.status == FavoritesStatus.failure)
                SliverFillRemaining(
                  child: Center(
                    child: Text(localizations.failed_to_load_trips),
                  ),
                )
              else
                SliverPadding(
                  padding: const EdgeInsets.only(top: 16),
                  sliver: SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        final itinerary = favoriteItineraries[index];
                        return TripTimelineItemTileWidget(
                          itinerary: itinerary,
                          showDivider: false,
                          isFavoriteScreen: true,
                        );
                      },
                      childCount: favoriteItineraries.length,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }
}
