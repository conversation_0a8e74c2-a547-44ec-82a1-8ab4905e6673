import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/pages/add_trip_screen.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/utils/premium_utils.dart';
import '../blocs/add_trip_bloc/add_trip_bloc.dart';
import 'trip_timeline_screen.dart';
import 'package:roamr/utils/date_utils.dart' as custom_date_utils;
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/country_utils.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _showTitle = false;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.hasClients) {
      final showTitle = _scrollController.offset > 60;
      if (showTitle != _showTitle) {
        setState(() {
          _showTitle = showTitle;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        slivers: [
          SliverAppBar(
            pinned: true,
            floating: false,
            automaticallyImplyLeading: false,
            backgroundColor: theme.cardColor,
            expandedHeight: 64,
            title: _showTitle ? Text(
              AppLocalizations.of(context)!.trips,
              style: theme.textTheme.titleLarge,
            ) : null,
            titleSpacing: 0,
            actions: [
              Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: colorScheme.secondary,
                    shape: BoxShape.circle,
                  ),
                  child: IconButton(
                    padding: EdgeInsets.zero,
                    iconSize: 20,
                    icon: const Icon(Icons.add, color: Colors.white),
                    onPressed: () async {
                      final state = context.read<AddTripBloc>().state;
                      if (state.trips.length >= 2) {
                        // Show premium prompt if user has 2 or more trips
                        final isPremium = await PremiumUtils.checkPremiumStatus(context);
                        if (!isPremium) return;
                      }
                      if (context.mounted) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => AddTripScreen(),
                          ),
                        );
                      }
                    },
                  ),
                ),
              ),
            ],
            flexibleSpace: FlexibleSpaceBar(
              expandedTitleScale: 1.0,
              titlePadding: EdgeInsets.zero,
              background: Padding(
                padding: EdgeInsets.only(
                  top: MediaQuery.of(context).padding.top,
                ),
                child: Padding(
                  padding: const EdgeInsets.only(
                    top: 16,
                    left: 16,
                    right: 16,
                    bottom: 8,
                  ),
                  child: Image.asset(
                    'assets/roamlogo.png',
                    height: 40,
                    fit: BoxFit.contain,
                    alignment: Alignment.centerLeft,
                  ),
                ),
              ),
            ),
          ),

          // Main content
          BlocBuilder<AddTripBloc, AddTripState>(
            builder: (context, state) {
              switch (state.status) {
                case AddTripStatus.loading:
                  return const SliverFillRemaining(
                    child: Center(child: CircularProgressIndicator()),
                  );

                case AddTripStatus.success:
                  if (state.trips.isEmpty) {
                    return SliverFillRemaining(
                      child: Center(
                        child: Text(AppLocalizations.of(context)!.no_trips),
                      ),
                    );
                  }
                  final now = DateTime.now();
                  final upcomingTrips =
                      state.trips
                          .where((trip) => trip.endDate.isAfter(now))
                          .toList()
                        ..sort((a, b) => a.startDate.compareTo(b.startDate));
                  final pastTrips =
                      state.trips
                          .where((trip) => trip.endDate.isBefore(now))
                          .toList()
                        ..sort((a, b) => b.startDate.compareTo(a.startDate));

                  final combinedList = [
                    ...upcomingTrips,
                    'separator',
                    ...pastTrips,
                  ];
                  return SliverPadding(
                    padding: const EdgeInsets.only(top: 16, bottom: 48),
                    sliver: SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                        if (combinedList[index] is String &&
                            combinedList[index] == 'separator') {
                          return Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              AppLocalizations.of(context)!.past_trips,
                              style: Theme.of(context).textTheme.titleMedium
                                  ?.copyWith(fontWeight: FontWeight.bold),
                            ),
                          );
                        } else {
                          final trip = combinedList[index] as Trip;
                          final startDate = trip.startDate.toLocal();
                          final endDate = trip.endDate.toLocal();

                          return BlocProvider(
                            key: ValueKey(trip.id),
                            create:
                                (context) => TripTimelineBloc(
                                  repository: context.read<TripRepository>(),
                                  tripId: trip.id,
                                )..add(TripTimelineSubscriptionRequested()),
                            child: Builder(
                              builder: (context) {
                                final timelineState =
                                    context.watch<TripTimelineBloc>().state;

                                return Slidable(
                                  key: Key(trip.id),
                                  endActionPane: ActionPane(
                                    motion: const ScrollMotion(),
                                    extentRatio: 0.5,
                                    dismissible: DismissiblePane(
                                      onDismissed: () {
                                        context.read<AddTripBloc>().add(
                                          DeleteTrip(tripId: trip.id),
                                        );
                                      },
                                      closeOnCancel: true,
                                      confirmDismiss: () async {
                                        return await showDeletionConfirmationDialog(
                                              context,
                                            ) ??
                                            false;
                                      },
                                    ),
                                    children: [
                                      SlidableAction(
                                        onPressed: (context) async {
                                          await Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder:
                                                  (context) =>
                                                      AddTripScreen(trip: trip),
                                            ),
                                          );
                                        },
                                        backgroundColor:
                                            Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                        foregroundColor: Colors.white,
                                        icon: Icons.edit,
                                        label:
                                            AppLocalizations.of(context)!.edit,
                                      ),
                                      SlidableAction(
                                        flex: 1,
                                        onPressed: (context) async {
                                          final bloc =
                                              context.read<AddTripBloc>();
                                          final shouldDelete =
                                              await showDeletionConfirmationDialog(
                                                context,
                                              ) ??
                                              false;
                                          if (shouldDelete) {
                                            bloc.add(
                                              DeleteTrip(tripId: trip.id),
                                            );
                                          }
                                        },
                                        backgroundColor: Colors.red,
                                        foregroundColor: Colors.white,
                                        icon: Icons.delete,
                                        label:
                                            AppLocalizations.of(
                                              context,
                                            )!.delete,
                                      ),
                                    ],
                                  ),
                                  child: SizedBox(
                                    height: 110,
                                    child: Container(
                                      margin: EdgeInsets.symmetric(
                                        vertical: 4,
                                        horizontal: 8,
                                      ),
                                      decoration: BoxDecoration(
                                        color:
                                            CustomTheme.getCardBackgroundColor(
                                              context,
                                            ),
                                        borderRadius: BorderRadius.circular(
                                          12.0,
                                        ),
                                        boxShadow: CustomTheme.getCardShadows(
                                          context,
                                        ),
                                      ),
                                      child: Column(
                                        children: [
                                          Spacer(),
                                          ListTile(
                                            leading: CountryUtils.buildFlagAvatar(
                                              trip.countryCode,
                                              trip.title,
                                              size: 44,
                                              backgroundColor: Theme.of(context).colorScheme.primary.withAlpha(40),
                                            ),
                                            title: Text(
                                              trip.title,
                                              style: Theme.of(
                                                context,
                                              ).textTheme.titleLarge?.copyWith(
                                                letterSpacing: -0.5,
                                                color:
                                                    CustomTheme.getPrimaryTextColor(
                                                      context,
                                                    ),
                                              ),
                                            ),
                                            subtitle: Text(
                                              custom_date_utils
                                                  .DateUtils.formatDateRange(
                                                startDate,
                                                endDate,
                                                context: context,
                                              ),
                                              style: Theme.of(
                                                context,
                                              ).textTheme.bodyMedium?.copyWith(
                                                color:
                                                    CustomTheme.getSecondaryTextColor(
                                                      context,
                                                    ),
                                              ),
                                            ),
                                            trailing: Text(
                                              CurrencyUtils.formatTotalExpenses(
                                                context,
                                                timelineState.totalExpenses,
                                              ),
                                              style: Theme.of(
                                                context,
                                              ).textTheme.titleMedium?.copyWith(
                                                color:
                                                    Theme.of(
                                                      context,
                                                    ).colorScheme.secondary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                            contentPadding:
                                                const EdgeInsets.symmetric(
                                                  horizontal: 16,
                                                  vertical: 12,
                                                ),
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder:
                                                      (context) =>
                                                          TripTimelineScreen(
                                                            trip: trip,
                                                          ),
                                                ),
                                              );
                                            },
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                          );
                        }
                      },
                      childCount: combinedList.length,
                    ),
                  ),
                );

                case AddTripStatus.failure:
                  return SliverFillRemaining(
                    child: Center(
                      child: Text(
                        AppLocalizations.of(context)!.failed_to_load_trips,
                      ),
                    ),
                  );

                default:
                  return SliverFillRemaining(
                    child: Center(
                      child: Text(AppLocalizations.of(context)!.no_trips),
                    ),
                  );
              }
            },
          ),
        ],
      ),
    );
  }
}
