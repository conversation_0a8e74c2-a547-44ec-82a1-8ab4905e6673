import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/document_attachment_utils.dart';
import 'package:roamr/widgets/attachment_viewer.dart';
import 'package:roamr/widgets/themed_background.dart'; // Added import

// Delegate for the persistent header (segmented control)
class _AttachmentFilterHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double minExtent;
  final double maxExtent;
  final Widget child;

  _AttachmentFilterHeaderDelegate({
    required this.minExtent,
    required this.maxExtent,
    required this.child,
  });

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material( // Ensures correct theming and touch handling
      color: Colors.transparent, // ThemedBackground will provide the actual background
      child: child,
    );
  }

  @override
  bool shouldRebuild(_AttachmentFilterHeaderDelegate oldDelegate) {
    return maxExtent != oldDelegate.maxExtent ||
           minExtent != oldDelegate.minExtent ||
           child != oldDelegate.child;
  }
}

/// A screen that displays all attachments for a trip
class TripAttachmentsScreen extends StatefulWidget {
  final Trip trip;

  const TripAttachmentsScreen({
    super.key,
    required this.trip,
  });

  @override
  State<TripAttachmentsScreen> createState() => _TripAttachmentsScreenState();
}

class _TripAttachmentsScreenState extends State<TripAttachmentsScreen> {
  late Future<List<AttachmentModel>> _attachmentsFuture;

  @override
  void initState() {
    super.initState();
    _loadAttachments();
  }

  void _loadAttachments() {
    _attachmentsFuture = _getAllTripAttachments();
  }

  /// Gets all attachments for the trip by fetching all itineraries and then all attachments for each itinerary
  Future<List<AttachmentModel>> _getAllTripAttachments() async {
    final tripRepository = context.read<TripRepository>();
    final attachmentRepository = context.read<AttachmentRepository>();

    // Get all itineraries for the trip
    final itineraries = await tripRepository.getTripItinerariesFuture(widget.trip.id);

    // Get all attachments for each itinerary
    final List<AttachmentModel> allAttachments = [];
    for (final itinerary in itineraries) {
      final attachments = await attachmentRepository.getAttachmentsByItineraryId(itinerary.id);
      allAttachments.addAll(attachments);
    }

    return allAttachments;
  }

  // Currently selected attachment type
  String _selectedType = 'photos';

  /// Builds a button for the attachment type selector
  Widget _buildTypeButton({
    required BuildContext context,
    required String type,
    required String label,
    required ColorScheme colorScheme,
  }) {
    final isSelected = _selectedType == type;
    final theme = Theme.of(context);

    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedType = type;
          });
        },
        child: Container(
          decoration: BoxDecoration(
            color: isSelected ? colorScheme.primary : Colors.transparent,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Center(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? colorScheme.onPrimary : theme.textTheme.bodyMedium?.color,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    Widget segmentedControlWidget = Container(
      height: 40,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: theme.cardColor.withOpacity(0.9), // Make slightly transparent for overlap
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: theme.dividerColor,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          _buildTypeButton(
            context: context,
            type: 'photos',
            label: localizations.photos,
            colorScheme: colorScheme,
          ),
          _buildTypeButton(
            context: context,
            type: 'audio',
            label: localizations.audio,
            colorScheme: colorScheme,
          ),
          _buildTypeButton(
            context: context,
            type: 'documents',
            label: localizations.documents,
            colorScheme: colorScheme,
          ),
        ],
      ),
    );

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        body: CustomScrollView(
          slivers: <Widget>[
            SliverAppBar(
              title: Text(widget.trip.title),
              backgroundColor: ThemedBackground.getAppBarColor(context),
              pinned: true,
              floating: false,
              elevation: 0, // To blend with ThemedBackground
            ),
            SliverPersistentHeader(
              pinned: true,
              delegate: _AttachmentFilterHeaderDelegate(
                minExtent: 56.0, // height 40 + vertical margin 16
                maxExtent: 56.0,
                child: segmentedControlWidget,
              ),
            ),
            FutureBuilder<List<AttachmentModel>>(
              future: _attachmentsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const SliverFillRemaining(
                    child: Center(child: CircularProgressIndicator()),
                  );
                }

                if (snapshot.hasError) {
                  return SliverFillRemaining(
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          localizations.error_loading_photos, // Consider a more generic error
                          style: textTheme.bodyLarge?.copyWith(color: theme.colorScheme.error),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  );
                }

                final attachments = snapshot.data ?? [];
                final filteredAttachments = attachments.where((a) {
                  if (_selectedType == 'photos') {
                    return a.type == AttachmentType.photo;
                  } else if (_selectedType == 'audio') {
                    return false;
                  } else if (_selectedType == 'documents') {
                    return a.type == AttachmentType.document;
                  }
                  return false;
                }).toList();

                if (filteredAttachments.isEmpty) {
                  String message;
                  if (_selectedType == 'photos') {
                    message = localizations.no_photos_for_trip;
                  } else if (_selectedType == 'audio') {
                    message = localizations.no_audio_for_trip;
                  } else if (_selectedType == 'documents') {
                    message = localizations.no_documents_for_trip;
                  } else {
                    message = localizations.no_attachments_for_trip;
                  }
                  return SliverFillRemaining(
                    hasScrollBody: false,
                    child: Center(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Text(
                          message,
                          style: textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  );
                }

                return SliverPadding(
                  padding: const EdgeInsets.all(8.0),
                  sliver: SliverGrid.builder(
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: filteredAttachments.length,
                    itemBuilder: (context, index) {
                      final attachment = filteredAttachments[index];
                      final fileExists = attachment.exists;

                      return GestureDetector(
                        onTap: fileExists ? () => _openAttachmentViewer(context, filteredAttachments, index) : null,
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.transparent,
                          ),
                          child: !fileExists
                              ? const Center(
                                  child: Icon(Icons.broken_image, color: Colors.grey),
                                )
                              : attachment.type == AttachmentType.photo
                                  ? Container(
                                      decoration: BoxDecoration(
                                        image: DecorationImage(
                                          image: FileImage(attachment.file),
                                          fit: BoxFit.cover,
                                        ),
                                      ),
                                    )
                                  : Container( // Document thumbnail
                                      padding: const EdgeInsets.all(8),
                                      child: Stack(
                                        children: [
                                          Container(
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.circular(4),
                                              gradient: _getDocumentGradient(attachment.file.path),
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black.withAlpha(51),
                                                  blurRadius: 3,
                                                  offset: const Offset(1, 1),
                                                ),
                                              ],
                                            ),
                                          ),
                                          Column(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              Expanded(
                                                flex: 3,
                                                child: Center(
                                                  child: Icon(
                                                    _getDocumentIcon(attachment.file.path),
                                                    color: Colors.white,
                                                    size: 50,
                                                  ),
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  alignment: Alignment.center,
                                                  padding: const EdgeInsets.symmetric(horizontal: 6),
                                                  decoration: BoxDecoration(
                                                    color: Colors.black.withAlpha(153),
                                                    borderRadius: const BorderRadius.only(
                                                      bottomLeft: Radius.circular(4),
                                                      bottomRight: Radius.circular(4),
                                                    ),
                                                  ),
                                                  child: Text(
                                                    attachment.file.path.split('/').last,
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontSize: 10,
                                                      fontWeight: FontWeight.bold,
                                                    ),
                                                    maxLines: 1,
                                                    overflow: TextOverflow.ellipsis,
                                                    textAlign: TextAlign.center,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                    ),
                        ),
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// Get the appropriate icon for a document based on its file extension
  IconData _getDocumentIcon(String filePath) {
    return DocumentAttachmentUtils.getFileIcon(filePath);
  }

  /// Get a gradient for the document background based on file type
  LinearGradient _getDocumentGradient(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();

    // Different gradients for different file types
    switch (extension) {
      case 'pdf':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFE53935), Color(0xFFC62828)], // Red gradient for PDFs
        );
      case 'doc':
      case 'docx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1565C0), Color(0xFF0D47A1)], // Blue gradient for Word docs
        );
      case 'xls':
      case 'xlsx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2E7D32), Color(0xFF1B5E20)], // Green gradient for Excel
        );
      case 'ppt':
      case 'pptx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF8F00), Color(0xFFEF6C00)], // Orange gradient for PowerPoint
        );
      default:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF455A64), Color(0xFF263238)], // Grey gradient for other files
        );
      }
    }

  /// Opens the attachment viewer for the selected attachment
  Future<void> _openAttachmentViewer(
    BuildContext context,
    List<AttachmentModel> attachments,
    int index,
  ) async {
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AttachmentViewer(
          attachments: attachments,
          initialIndex: index,
          itineraryTitle: widget.trip.title,
          allowDeletion: false, // Don't allow deletion in this view
        ),
      ),
    );
  }
}
