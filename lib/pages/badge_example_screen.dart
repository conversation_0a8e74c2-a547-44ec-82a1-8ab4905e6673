import 'package:flutter/material.dart';
import 'package:roamr/models/achievement.dart';
import 'package:roamr/utils/achievement_utils.dart';

class BadgeExampleScreen extends StatelessWidget {
  const BadgeExampleScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Achievement Badges'),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Achievement Badges',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 24),

              // Destination badge
              _buildBadgeSection(
                'Destination Achievements',
                AchievementType.destination,
              ),

              const Divider(height: 40),

              // Expense badge
              _buildBadgeSection(
                'Expense Achievements',
                AchievementType.expense,
              ),

              const Divider(height: 40),

              // Frequency badge
              _buildBadgeSection(
                'Frequency Achievements',
                AchievementType.frequency,
              ),

              const Divider(height: 40),

              // Duration badge
              _buildBadgeSection(
                'Duration Achievements',
                AchievementType.duration,
              ),

              const Divider(height: 40),

              // Flight badge
              _buildBadgeSection(
                'Flight Achievements',
                AchievementType.flight,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build a section with title and badges
  Widget _buildBadgeSection(String title, AchievementType type) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            AchievementUtils.buildBadgeWithStar(type, 1),
            AchievementUtils.buildBadgeWithStar(type, 3),
            AchievementUtils.buildBadgeWithStar(type, 6),
          ],
        ),
      ],
    );
  }
}
