import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/pages/auth/verify_otp_page.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/widgets/themed_background.dart'; // Added import

class LoginRegisterPage extends StatefulWidget {
  final bool isOnboarding;

  const LoginRegisterPage({
    super.key,
    this.isOnboarding = false,
  });

  @override
  State<LoginRegisterPage> createState() => _LoginRegisterPageState();
}

class _LoginRegisterPageState extends State<LoginRegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isLogin = true;
  bool _isLoading = false;
  String? _errorMessage;

  final Auth _auth = Auth();

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Get email (required for both login and registration)
      final email = _emailController.text.trim();

      // Get name (only used for registration)
      final name = _isLogin ? null : _nameController.text.trim();

      // Send OTP to the user's email
      await _auth.sendOtp(
        email: email,
        data: (name != null && name.isNotEmpty) ? {'name': name} : null,
        isLogin: _isLogin,
      );

      if (mounted) {
        // Navigate to OTP verification page
        final result = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => VerifyOtpPage(
              email: email,
              name: name,
            ),
          ),
        );

        // If verification was successful
        if (result == true && mounted) {
          try {
            // Refresh user data
            await _auth.getLatestUser();

            // Add a small delay to ensure state propagation on Android
            if (defaultTargetPlatform == TargetPlatform.android) {
              await Future.delayed(const Duration(milliseconds: 300));
            }
          } catch (e) {
            debugPrint('Error refreshing auth session: $e');
          }

          // Return to previous screen after successful authentication
          if (mounted) {
            Navigator.of(context).pop(true);
          }
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString();
        final localizations = AppLocalizations.of(context)!;

        // Remove 'Exception: ' prefix from error message
        if (errorMessage.startsWith('Exception: ')) {
          errorMessage = errorMessage.substring('Exception: '.length);
        }

        // Check if the error message is a localization key
        if (errorMessage.startsWith('error_')) {
          // Use reflection to get the localized string
          final localizedMessage = _getLocalizedError(localizations, errorMessage);
          if (localizedMessage != null) {
            errorMessage = localizedMessage;
          }
        }

        setState(() {
          _errorMessage = errorMessage;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// Helper method to get localized error messages
  String? _getLocalizedError(AppLocalizations localizations, String errorKey) {
    switch (errorKey) {
      case 'error_invalid_email':
        return localizations.error_invalid_email;
      case 'error_user_disabled':
        return localizations.error_user_disabled;
      case 'error_user_not_found':
        return localizations.error_user_not_found;
      case 'error_invalid_credentials':
        return localizations.error_invalid_credentials;
      case 'error_email_exists':
        return localizations.error_email_exists;
      case 'error_weak_password':
        return localizations.error_weak_password;
      case 'error_email_not_registered':
        return localizations.error_email_not_registered;
      case 'error_otp_disabled':
        return localizations.error_otp_disabled;
      case 'error_unknown':
        return localizations.error_unknown;
      default:
        return null;
    }
  }

  Future<void> _signInWithGoogle() async {
    // Unfocus to hide keyboard before starting Google Sign-In
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _auth.signInWithGoogle();
      
      if (mounted) {
        if (widget.isOnboarding) {
          // If on onboarding, pop to let OnboardingScreen handle completion
          Navigator.of(context).pop(true);
        } else {
          // If not on onboarding, navigate to home page directly
          Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceAll('Exception: ', '');
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _signInWithApple() async {
    // Unfocus to hide keyboard before starting Apple Sign-In
    FocusScope.of(context).unfocus();

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _auth.signInWithApple();
      
      if (mounted) {
        if (widget.isOnboarding) {
          // If on onboarding, pop to let OnboardingScreen handle completion
          Navigator.of(context).pop(true);
        } else {
          // If not on onboarding, navigate to home page directly
          Navigator.of(context).pushNamedAndRemoveUntil('/home', (route) => false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = e.toString().replaceAll('Exception: ', '');
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    return ThemedBackground( // Wrapped with ThemedBackground
      child: Scaffold(
        backgroundColor: Colors.transparent, // Changed
        extendBodyBehindAppBar: true, // Added
        appBar: AppBar(
          backgroundColor: ThemedBackground.getAppBarColor(context), // Changed
          elevation: 0,
          automaticallyImplyLeading: !widget.isOnboarding,
          title: Text(
            _isLogin ? localizations.sign_in : localizations.create_account,
            style: textTheme.titleLarge,
          ),
          leading: widget.isOnboarding ? null : IconButton(
            icon: Icon(
              Icons.arrow_back_ios,
              color: CustomTheme.getPrimaryIconColor(context)
            ),
            onPressed: () => Navigator.of(context).pop(),
          ),
          actions: widget.isOnboarding ? null : [
            IconButton(
              icon: Icon(
                Icons.close,
                color: CustomTheme.getPrimaryIconColor(context)
              ),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
        body: SafeArea(
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 450), // Set maximum width for tablet mode
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      if (_errorMessage != null)
                        Container(
                          padding: const EdgeInsets.all(12),
                          margin: const EdgeInsets.only(bottom: 16),
                          decoration: BoxDecoration(
                            color: Colors.red.withAlpha(25),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            _errorMessage!,
                            style: const TextStyle(color: Colors.red),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      if (!_isLogin)
                        TextFormField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: localizations.name,
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            prefixIcon: const Icon(Icons.person_outline),
                          ),
                          textInputAction: TextInputAction.next,
                          validator: (value) {
                            if (!_isLogin && (value == null || value.isEmpty)) {
                              return localizations.please_enter_name;
                            }
                            return null;
                          },
                        ),
                      if (!_isLogin)
                        const SizedBox(height: 16),
                      TextFormField(
                        controller: _emailController,
                        decoration: InputDecoration(
                          labelText: localizations.email,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          prefixIcon: const Icon(Icons.email_outlined),
                        ),
                        keyboardType: TextInputType.emailAddress,
                        autofocus: _isLogin,
                        textInputAction: TextInputAction.next,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return localizations.please_enter_email;
                          }
                          if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                            return localizations.please_enter_valid_email;
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Colors.white.withAlpha(20)
                              : colorScheme.primary.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: Theme.of(context).brightness == Brightness.dark
                                  ? Colors.white
                                  : colorScheme.primary,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                _isLogin
                                    ? localizations.verification_code_login
                                    : localizations.verification_code_register,
                                style: TextStyle(
                                  color: Theme.of(context).brightness == Brightness.dark
                                      ? Colors.white
                                      : colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      FilledButton(
                        onPressed: _isLoading ? null : _submitForm,
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : Text(_isLogin ? localizations.continue_with_email : localizations.register_with_email),
                      ),
                      const SizedBox(height: 16),
                        // OR Divider
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 16.0),
                          child: Row(
                            children: [
                              const Expanded(child: Divider()),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                                child: Text(
                                  AppLocalizations.of(context)!.or,
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ),
                              const Expanded(child: Divider()),
                            ],
                          ),
                        ),
                        // Google Sign-In Button
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton(
                            onPressed: _isLoading ? null : _signInWithGoogle,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(
                                color: Theme.of(context).dividerColor,
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SvgPicture.asset(
                                        'assets/icons/google.svg',
                                        height: 24,
                                        width: 24,
                                        colorFilter: null, // Remove any color filtering
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        AppLocalizations.of(context)!.continue_with_google,
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        // Apple Sign-In Button
                      if (defaultTargetPlatform != TargetPlatform.android) ...[
                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton(
                            onPressed: _isLoading ? null : _signInWithApple,
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              side: BorderSide(
                                color: Theme.of(context).dividerColor,
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.apple,
                                        size: 24,
                                        color: Theme.of(context).textTheme.bodyLarge?.color,
                                      ),
                                      const SizedBox(width: 12),
                                      Text(
                                        AppLocalizations.of(context)!.continue_with_apple,
                                        style: Theme.of(context).textTheme.bodyLarge,
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ],
                      const SizedBox(height: 24),
                      // Toggle between login and register
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _isLogin ? localizations.dont_have_account : localizations.already_have_account,
                            style: textTheme.bodyMedium,
                          ),
                          TextButton(
                            onPressed: _isLoading
                                ? null
                                : () {
                                    setState(() {
                                      _isLogin = !_isLogin;
                                      _errorMessage = null;
                                    });
                                  },
                            child: Text(
                              _isLogin ? localizations.register : localizations.sign_in,
                              style: TextStyle(
                                color: colorScheme.secondary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ), // Closes SafeArea (body of Scaffold)
    ); // Closes ThemedBackground
  }
}