import 'dart:async';
import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/theme/custom_theme.dart';

class VerifyOtpPage extends StatefulWidget {
  final String email;
  final String? name;

  const VerifyOtpPage({
    super.key,
    required this.email,
    this.name,
  });

  @override
  State<VerifyOtpPage> createState() => _VerifyOtpPageState();
}

class _VerifyOtpPageState extends State<VerifyOtpPage> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  final Auth _auth = Auth();
  bool _isLoading = false;
  String? _errorMessage;
  int _resendCountdown = 60;
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startResendTimer();
  }

  void _startResendTimer() {
    _timer?.cancel();
    setState(() {
      _resendCountdown = 60;
    });
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_resendCountdown > 0) {
        setState(() {
          _resendCountdown--;
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _verifyOtp() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      await _auth.verifyOtp(
        email: widget.email,
        token: _otpController.text.trim(),
        name: widget.name,
        isLogin: widget.name == null, // If no name, it's a login
      );

      if (mounted) {
        // Pop with result=true to indicate successful verification and registration
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString();

        // Remove 'Exception: ' prefix from error message
        if (errorMessage.startsWith('Exception: ')) {
          errorMessage = errorMessage.substring('Exception: '.length);
        }

        setState(() {
          _errorMessage = errorMessage;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _resendOtp() async {

    // Security measure: Only allow resend after the full countdown period
    if (_resendCountdown > 0) {
      // Show a message that they need to wait
      setState(() {
        _errorMessage = 'Please wait $_resendCountdown seconds before requesting a new code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Resend OTP for the same email
      await _auth.sendOtp(
        email: widget.email,
        data: widget.name != null ? {'name': widget.name} : null,
        isLogin: widget.name == null, // If no name, it's a login
      );

      _startResendTimer();
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString();

        // Remove 'Exception: ' prefix from error message
        if (errorMessage.startsWith('Exception: ')) {
          errorMessage = errorMessage.substring('Exception: '.length);
        }

        setState(() {
          _errorMessage = errorMessage;
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _otpController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          localizations.verify_email,
          style: textTheme.titleLarge,
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: CustomTheme.getPrimaryIconColor(context)),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 450),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(24.0),
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      localizations.verification_code_sent,
                      style: textTheme.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.email,
                      style: textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 24),
                    if (_errorMessage != null)
                      Container(
                        padding: const EdgeInsets.all(12),
                        margin: const EdgeInsets.only(bottom: 16),
                        decoration: BoxDecoration(
                          color: Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(color: Colors.red),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    TextFormField(
                      controller: _otpController,
                      decoration: InputDecoration(
                        labelText: localizations.verification_code,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        prefixIcon: const Icon(Icons.security),
                      ),
                      keyboardType: TextInputType.number,
                      autofocus: true,
                      textInputAction: TextInputAction.done,
                      onFieldSubmitted: (_) => _verifyOtp(),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return localizations.please_enter_verification_code;
                        }
                        if (value.length < 6) {
                          return localizations.verification_code_too_short;
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                    FilledButton(
                      onPressed: _isLoading ? null : _verifyOtp,
                      child: _isLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Text(localizations.verify),
                    ),
                    const SizedBox(height: 16),
                    TextButton(
                      // Always enable the button, but handle the countdown in the _resendOtp method
                      onPressed: _isLoading ? null : _resendOtp,
                      child: Text(
                        _resendCountdown > 0
                            ? '${localizations.resend_code} (${_resendCountdown}s)'
                            : localizations.resend_code,
                        style: TextStyle(
                          color: _resendCountdown > 0 || _isLoading
                              ? colorScheme.onSurface.withAlpha(128)
                              : colorScheme.secondary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
