import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/widgets/trip_timeline_item_tile_widget.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:roamr/widgets/location_button_widget.dart';

class TripMapViewScreen extends StatefulWidget {
  const TripMapViewScreen({
    super.key,
    required this.trip,
    required this.itineraries,
  });

  final Trip trip;
  final List<TripItinerary> itineraries;

  @override
  State<TripMapViewScreen> createState() => _TripMapViewScreenState();
}

class _TripMapViewScreenState extends State<TripMapViewScreen> {
  GoogleMapController? _mapController;
  String? _selectedItineraryId;
  final Map<String, Marker> _markers = {};
  final CarouselSliderController _carouselController = CarouselSliderController();

  @override
  void initState() {
    super.initState();
    // Set initial selected itinerary when the screen loads
    if (widget.itineraries.isNotEmpty) {
      setState(() {
        _selectedItineraryId = widget.itineraries.first.id;
      });
    }
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.trip.title),
        backgroundColor: colorScheme.primary.withAlpha(75),
      ),
      body: Builder(builder: (context) {
        // Create markers for each itinerary with location
        _updateMarkers(widget.itineraries);

        // Default camera position (use first itinerary with location or a default)
        final initialCameraPosition = widget.itineraries.isNotEmpty && widget.itineraries.first.location != null
            ? CameraPosition(
                target: widget.itineraries.first.location!,
                zoom: 12,
              )
            : const CameraPosition(
                target: LatLng(13.7625554, 100.4342089), // Default location
                zoom: 10,
              );

        return Stack(
            children: [
              // Google Map
              GoogleMap(
                initialCameraPosition: initialCameraPosition,
                onMapCreated: (controller) {
                  _mapController = controller;
                },
                markers: Set<Marker>.of(_markers.values),
                myLocationEnabled: true,
                myLocationButtonEnabled: false, // Disable built-in button as we'll add our own
                mapToolbarEnabled: false,
                compassEnabled: true,
                zoomControlsEnabled: false,
              ),

              // Custom location button (positioned above the carousel)
              LocationButtonWidget(
                mapController: _mapController,
                bottom: 174, // Position above the carousel (adjusted for new carousel height)
                right: 16,
                backgroundColor: colorScheme.surface.withAlpha(204),
                foregroundColor: colorScheme.onSurface,
              ),

              // Bottom horizontal list of itineraries
              Positioned(
                bottom: 16,
                left: 0,
                right: 0,
                child: SizedBox(
                  height: 144, // Increased from 120 to accommodate the content + margins
                  child: _buildItineraryList(widget.itineraries),
                ),
              ),
            ],
          );
      }),
    );
  }

  void _updateMarkers(List<TripItinerary> itineraries) {
    _markers.clear();
    for (final itinerary in itineraries) {
      if (itinerary.location != null) {
        final markerId = MarkerId(itinerary.id);
        final marker = Marker(
          markerId: markerId,
          position: itinerary.location!,
          infoWindow: InfoWindow(
            title: itinerary.title,
            snippet: itinerary.locationText,
          ),
          onTap: () {
            setState(() {
              _selectedItineraryId = itinerary.id;
            });
          },
          icon: BitmapDescriptor.defaultMarkerWithHue(
            _selectedItineraryId == itinerary.id
                ? BitmapDescriptor.hueGreen
                : BitmapDescriptor.hueRed,
          ),
        );
        _markers[itinerary.id] = marker;
      }
    }
  }

  Widget _buildItineraryList(List<TripItinerary> itinerariesWithLocation) {
    if (widget.itineraries.isEmpty) {
      return Center(
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor.withAlpha(200),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 10,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            AppLocalizations.of(context)!.no_locations,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
      );
    }

    // Use CarouselSlider for a simpler carousel implementation
    return CarouselSlider.builder(
    carouselController: _carouselController,
    itemCount: widget.itineraries.length,
    options: CarouselOptions(
      viewportFraction: 0.85, // Further reduced to show more of adjacent items on both sides
      enlargeCenterPage: true,
      enlargeFactor: 0.1, // Slightly reduced to allow more visibility of adjacent items
      enableInfiniteScroll: false,
      padEnds: true, // Enable padding to ensure first and last items are properly positioned
      pageSnapping: true,
      disableCenter: false,
      initialPage: 0,
      clipBehavior: Clip.none,
      onPageChanged: (index, reason) {
        setState(() {
          _selectedItineraryId = widget.itineraries[index].id;
        });

        // Animate map to the selected location
        final itinerary = widget.itineraries[index];
        if (itinerary.location != null && _mapController != null) {
          _mapController!.animateCamera(
            CameraUpdate.newLatLngZoom(
              itinerary.location!,
              15,
            ),
          );
        }
      },
    ),
    itemBuilder: (context, index, realIndex) {
      final itinerary = widget.itineraries[index];

      return Container(
        width: 350,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4), // Reduced vertical margin
        child: TripTimelineItemTileWidget(
          trip: widget.trip,
          itinerary: itinerary,
          showDivider: false,
          enableSwipeActions: false,
        ),
      );
    },
        );
  }
}
