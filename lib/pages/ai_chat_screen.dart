import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/blocs/ai_chat_cubit.dart';

class AIChatScreen extends StatelessWidget {
  const AIChatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    return BlocProvider(
      create: (_) => AIChatCubit(),
      child: Scaffold(
        appBar: AppBar(
          title: Row(
            children: [
              Image.asset('assets/plane.png', width: 28, height: 28),
              const SizedBox(width: 8),
              Text(localizations.ai_chat_title),
            ],
          ),
          backgroundColor: theme.colorScheme.surface,
          elevation: 0,
        ),
        body: SafeArea(
          child: Column(
            children: [
              Expanded(
                child: BlocBuilder<AIChatCubit, AIChatState>(
                  builder: (context, state) {
                    return ListView.builder(
                      padding: const EdgeInsets.all(16),
                      itemCount: state.messages.length,
                      itemBuilder: (context, index) {
                        final msg = state.messages[index];
                        return Align(
                          alignment: msg.isUser ? Alignment.centerRight : Alignment.centerLeft,
                          child: Container(
                            margin: const EdgeInsets.symmetric(vertical: 4),
                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                            decoration: BoxDecoration(
                              color: msg.isUser ? theme.colorScheme.primary : theme.cardColor,
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: msg.isLoading
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(localizations.ai_chat_loading),
                                    ],
                                  )
                                : Text(
                                    msg.text,
                                    style: theme.textTheme.bodyLarge?.copyWith(
                                      color: msg.isUser ? Colors.white : theme.textTheme.bodyLarge?.color,
                                    ),
                                  ),
                          ),
                        );
                      },
                    );
                  },
                ),
              ),
              _AIChatInputBar(),
            ],
          ),
        ),
      ),
    );
  }
}

class _AIChatInputBar extends StatefulWidget {
  @override
  State<_AIChatInputBar> createState() => _AIChatInputBarState();
}

class _AIChatInputBarState extends State<_AIChatInputBar> {
  final TextEditingController _controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    return BlocBuilder<AIChatCubit, AIChatState>(
      builder: (context, state) {
        return Padding(
          padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _controller,
                  decoration: InputDecoration(
                    hintText: localizations.ai_chat_input_hint,
                    filled: true,
                    fillColor: theme.cardColor.withAlpha(120),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(16),
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  ),
                  onSubmitted: (_) => _send(context),
                  enabled: !state.isAwaitingAI,
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                icon: Icon(Icons.send, color: theme.colorScheme.primary),
                onPressed: state.isAwaitingAI ? null : () => _send(context),
              ),
            ],
          ),
        );
      },
    );
  }

  void _send(BuildContext context) {
    final text = _controller.text.trim();
    if (text.isEmpty) return;
    context.read<AIChatCubit>().sendMessage(text);
    _controller.clear();
  }
} 