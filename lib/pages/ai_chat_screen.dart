import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/services/gemini_chat_service.dart';
import 'package:get_it/get_it.dart';

class AIChatScreen extends StatefulWidget {
  const AIChatScreen({Key? key}) : super(key: key);

  @override
  State<AIChatScreen> createState() => _AIChatScreenState();
}

class _ChatMessage {
  final String text;
  final bool isUser;
  final bool isLoading;
  _ChatMessage({required this.text, required this.isUser, this.isLoading = false});
}

class _AIChatScreenState extends State<AIChatScreen> {
  final TextEditingController _controller = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<_ChatMessage> _messages = [];
  bool _isAwaitingAI = false;
  final getIt = GetIt.instance;

  bool _welcomeMessageAdded = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_welcomeMessageAdded) {
      _addWelcomeMessage();
      _welcomeMessageAdded = true;
    }
  }

  void _addWelcomeMessage() {
    final localizations = AppLocalizations.of(context)!;
    setState(() {
      _messages.add(_ChatMessage(
        text: localizations.ai_chat_welcome,
        isUser: false,
      ));
    });
  }

  Future<void> _sendMessage() async {
    final text = _controller.text.trim();
    if (text.isEmpty || _isAwaitingAI) return;

    setState(() {
      _messages.add(_ChatMessage(text: text, isUser: true));
      _messages.add(_ChatMessage(text: '', isUser: false, isLoading: true));
      _isAwaitingAI = true;
    });
    _controller.clear();

    final geminiChatService = getIt<GeminiChatService>();
    if (!geminiChatService.isInitialized) {
      await geminiChatService.initialize();
      if (!mounted) return;
    }

    String aiResponse = '';
    try {
      await for (final chunk in geminiChatService.sendMessage(text)) {
        if (!mounted) return;
        setState(() {
          aiResponse += chunk;
          _messages.removeLast();
          _messages.add(_ChatMessage(text: aiResponse, isUser: false, isLoading: true));
        });
      }
      if (!mounted) return;
      setState(() {
        _messages.removeLast();
        _messages.add(_ChatMessage(text: aiResponse, isUser: false));
        _isAwaitingAI = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _messages.removeLast();
        _messages.add(_ChatMessage(text: 'Error: $e', isUser: false));
        _isAwaitingAI = false;
      });
    }

    Future.delayed(const Duration(milliseconds: 100), () {
      if (!mounted) return;
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });
  }

  Widget _buildChatBubble(_ChatMessage msg, BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    
    return Align(
      alignment: msg.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Row(
        mainAxisAlignment: msg.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (!msg.isUser) ...[
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withAlpha(40),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.smart_toy,
                  size: 20,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
          ],
          Flexible(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 4),
              constraints: BoxConstraints(maxWidth: MediaQuery.of(context).size.width * 0.75),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  decoration: BoxDecoration(
                    color: msg.isUser
                        ? theme.colorScheme.primary.withAlpha(180)
                        : theme.cardColor.withAlpha(150),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: msg.isUser
                          ? theme.colorScheme.primary.withAlpha(100)
                          : theme.dividerColor.withAlpha(40),
                      width: 1,
                    ),
                  ),
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!msg.isUser) ...[
                        Text(
                          'RoamR AI',
                          style: theme.textTheme.labelSmall?.copyWith(
                            color: theme.colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 2),
                      ],
                      msg.isLoading
                          ? Row(
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(theme.colorScheme.primary),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(localizations.ai_chat_loading),
                              ],
                            )
                          : Text(
                              msg.text,
                              style: theme.textTheme.bodyLarge?.copyWith(
                                color: msg.isUser
                                    ? Colors.white
                                    : theme.textTheme.bodyLarge?.color,
                              ),
                            ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    
    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Row(
          children: [
            Icon(Icons.smart_toy, color: theme.colorScheme.primary),
            const SizedBox(width: 8),
            Text(localizations.ai_chat_title),
          ],
        ),
        backgroundColor: theme.appBarTheme.backgroundColor,
        elevation: 0,
      ),
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 8),
                itemCount: _messages.length,
                itemBuilder: (context, index) {
                  return _buildChatBubble(_messages[index], context);
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(8, 0, 8, 8),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _controller,
                      decoration: InputDecoration(
                        hintText: localizations.ai_chat_input_hint,
                        filled: true,
                        fillColor: theme.cardColor.withAlpha(120),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(16),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                      ),
                      onSubmitted: (_) => _sendMessage(),
                      enabled: !_isAwaitingAI,
                    ),
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.send, color: theme.colorScheme.primary),
                    onPressed: _isAwaitingAI ? null : _sendMessage,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
