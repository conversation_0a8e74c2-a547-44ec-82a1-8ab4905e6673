import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/utils/dialog_utils.dart';

import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_state.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/premium_utils.dart';
import 'package:roamr/widgets/trip_timeline_filter_widget.dart';
import 'package:roamr/widgets/trip_timeline_widget.dart';
import 'package:roamr/widgets/trip_timeline_header_widget.dart';
import 'package:roamr/pages/trip_map_view_screen.dart';
import 'package:roamr/pages/add_edit_itinerary_screen.dart';
import '../models/trip.dart';
import '../models/trip_itinerary.dart';

class TripTimelineScreen extends StatefulWidget {
  const TripTimelineScreen({super.key, required this.trip});

  final Trip trip;

  @override
  State<TripTimelineScreen> createState() => _TripTimelineScreenState();
}

class _TripTimelineScreenState extends State<TripTimelineScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabAnimationController;
  bool _isScrollingDown = false;
  double _lastScrollPosition = 0;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimationController.forward(); // Start with visible button

    // Add scroll listener
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // Get current scroll position
    final currentPosition = _scrollController.position.pixels;

    // Determine scroll direction
    if (currentPosition > _lastScrollPosition && !_isScrollingDown) {
      // Scrolling down - hide button
      _isScrollingDown = true;
      _fabAnimationController.reverse();
    } else if (currentPosition < _lastScrollPosition && _isScrollingDown) {
      // Scrolling up - show button
      _isScrollingDown = false;
      _fabAnimationController.forward();
    }

    // Update last position
    _lastScrollPosition = currentPosition;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return BlocProvider(
      create:
          (context) => TripTimelineBloc(
            tripId: widget.trip.id,
            repository: context.read<TripRepository>(),
          )..add(const TripTimelineSubscriptionRequested()),
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        floatingActionButton: BlocBuilder<TripTimelineBloc, TripTimelineState>(
          builder: (context, state) {
            return ScaleTransition(
              scale: _fabAnimationController,
              child: AnimatedBuilder(
                animation: _fabAnimationController,
                builder: (context, child) {
                  return Transform.translate(
                    offset: Offset(
                      0,
                      100 * (1 - _fabAnimationController.value),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      elevation: 4 * _fabAnimationController.value,
                      borderRadius: BorderRadius.circular(24),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: () async {
                          // Proceed with map functionality
                          // Get the itineraries with location from the current state
                          final itinerariesWithLocation =
                              state.filteredItineraries
                                  .whereType<TripItinerary>()
                                  .where(
                                    (itinerary) => itinerary.location != null,
                                  )
                                  .toList();

                          // Only open map if there are itineraries with location data
                          if (itinerariesWithLocation.isNotEmpty) {
                            if (context.mounted) {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder:
                                      (_) => TripMapViewScreen(
                                        trip: widget.trip,
                                        itineraries: itinerariesWithLocation,
                                      ),
                                ),
                              );
                            }
                          } else if (context.mounted) {
                            // Show dialog when there are no itineraries with location data
                            showNoLocationsDialog(context);
                          }
                        },
                        child: Container(
                          height: 36,
                          width: 80,
                          padding: EdgeInsets.zero,
                          decoration: BoxDecoration(
                            color: colorScheme.secondary.withAlpha(204),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Center(
                            child: const Icon(
                              Icons.map,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
        body: NestedScrollView(
          controller: _scrollController,
          headerSliverBuilder: (context, innerBoxIsScrolled) {
            return [
              SliverAppBar(
                title: Text(widget.trip.title),
                pinned: true,
                floating: true,
                snap: true,
                expandedHeight: 260,
                backgroundColor: colorScheme.primary.withAlpha(75),
                actions: [
                  Padding(
                    padding: const EdgeInsets.only(right: 16.0, top: 8.0),
                    child: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: colorScheme.secondary,
                        shape: BoxShape.circle,
                      ),
                      child: IconButton(
                        padding: EdgeInsets.zero,
                        iconSize: 20,
                        icon: const Icon(Icons.add, color: Colors.white),
                        onPressed: () async {
                          final state = context.read<TripTimelineBloc>().state;
                          if (state.filteredItineraries.length >= 10) {
                            final isPremium =
                                await PremiumUtils.checkPremiumStatus(context);
                            if (!isPremium) return;
                          }
                          if (context.mounted) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddEditItineraryScreen(
                                  tripId: widget.trip.id,
                                  startDate: widget.trip.startDate,
                                  endDate: widget.trip.endDate,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ),
                  ),
                ],
                flexibleSpace: FlexibleSpaceBar(
                  collapseMode: CollapseMode.pin,
                  background: Container(
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withAlpha(20),
                    ),
                    child: SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.only(top: kToolbarHeight),
                        child: SingleChildScrollView(
                          physics: const NeverScrollableScrollPhysics(),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              TripTimelineHeaderWidget(trip: widget.trip),
                              TripTimelineFilterWidget(),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ];
          },
          body: MediaQuery.removePadding(
            context: context,
            removeTop: true,
            child: TripTimelineWidget(trip: widget.trip),
          ),
        ),
      ),
    );
  }
}
