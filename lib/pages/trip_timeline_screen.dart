import 'package:flutter/material.dart';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/utils/dialog_utils.dart';

import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_state.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/premium_utils.dart';
import 'package:roamr/widgets/themed_background.dart';
import 'package:roamr/widgets/trip_timeline_filter_widget.dart';
import 'package:roamr/widgets/trip_timeline_widget.dart';
import 'package:roamr/widgets/trip_timeline_header_widget.dart';
import 'package:roamr/pages/trip_map_view_screen.dart';
import 'package:roamr/pages/add_edit_itinerary_screen.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/firebase/analytics_service.dart';
import '../models/trip_itinerary.dart';
import 'package:roamr/utils/usage_limit_utils.dart';

class TripTimelineScreen extends StatefulWidget {
  const TripTimelineScreen({super.key, required this.trip});

  final Trip trip;

  @override
  State<TripTimelineScreen> createState() => _TripTimelineScreenState();
}

class _TripTimelineScreenState extends State<TripTimelineScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabAnimationController;
  bool _isScrollingDown = false;
  double _lastScrollPosition = 0;

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _fabAnimationController.forward(); // Start with visible button

    // Add scroll listener
    _scrollController.addListener(_scrollListener);
    
    // Log timeline viewed
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AnalyticsService.instance.logTimelineViewed(
        trip: widget.trip,
      );
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_scrollListener);
    _fabAnimationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // Get current scroll position
    final currentPosition = _scrollController.position.pixels;

    // Determine scroll direction
    if (currentPosition > _lastScrollPosition && !_isScrollingDown) {
      // Scrolling down - hide button
      _isScrollingDown = true;
      _fabAnimationController.reverse();
    } else if (currentPosition < _lastScrollPosition && _isScrollingDown) {
      // Scrolling up - show button
      _isScrollingDown = false;
      _fabAnimationController.forward();
    }

    // Update last position
    _lastScrollPosition = currentPosition;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return BlocProvider(
      create: (context) => TripTimelineBloc(
            tripId: widget.trip.id,
            repository: context.read<TripRepository>(),
          )..add(const TripTimelineSubscriptionRequested()),
      child: ThemedBackground(
        child: Scaffold(
          backgroundColor: Colors.transparent,
          floatingActionButton: Builder(
            builder: (fabContext) => Container(
              width: 56,
              height: 56,
              decoration: BoxDecoration(
                color: colorScheme.secondary,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(30),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  borderRadius: BorderRadius.circular(28),
                  onTap: () async {
                    if (!await checkItineraryLimitAndShowDialog(fabContext, widget.trip.id)) return;
                    if (fabContext.mounted) {
                      Navigator.push(
                        fabContext,
                        MaterialPageRoute(
                          builder: (context) => AddEditItineraryScreen(
                            tripId: widget.trip.id,
                            startDate: widget.trip.startDate,
                            endDate: widget.trip.endDate,
                          ),
                        ),
                      );
                    }
                  },
                  child: const Icon(Icons.add, color: Colors.white, size: 28),
                ),
              ),
            ),
          ),
          floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
          body: Builder(
            builder: (context) {
              return Stack(
                children: [
                  NestedScrollView(
                    controller: _scrollController,
                    headerSliverBuilder: (context, innerBoxIsScrolled) {
                      return [
                        SliverAppBar(
                          title: Text(widget.trip.title),
                          pinned: true,
                          floating: true,
                          snap: true,
                          expandedHeight: 260,
                          backgroundColor: Colors.transparent,
                          actions: const [],
                          flexibleSpace: FlexibleSpaceBar(
                            collapseMode: CollapseMode.pin,
                            background: Container(
                              decoration: BoxDecoration(
                                color: colorScheme.primary.withAlpha(50),
                              ),
                              child: SafeArea(
                                child: Padding(
                                  padding: const EdgeInsets.only(top: kToolbarHeight),
                                  child: SingleChildScrollView(
                                    physics: const NeverScrollableScrollPhysics(),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TripTimelineHeaderWidget(trip: widget.trip),
                                        const TripTimelineFilterWidget(),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ];
                    },
                    body: MediaQuery.removePadding(
                      context: context,
                      removeTop: true,
                      child: TripTimelineWidget(trip: widget.trip),
                    ),
                  ),
                  // Custom positioned map button
                  Positioned(
                    bottom: 32,
                    left: 0,
                    right: 0,
                    child: BlocBuilder<TripTimelineBloc, TripTimelineState>(
                      builder: (context, state) {
                        return ScaleTransition(
                          scale: _fabAnimationController,
                          child: AnimatedBuilder(
                            animation: _fabAnimationController,
                            builder: (context, child) {
                              return Transform.translate(
                                offset: Offset(
                                  0,
                                  100 * (1 - _fabAnimationController.value),
                                ),
                                child: Center(
                                  child: Material(
                                    color: colorScheme.secondary.withAlpha(204),
                                    borderRadius: BorderRadius.circular(24),
                                    elevation: 4 * _fabAnimationController.value,
                                    child: InkWell(
                                      borderRadius: BorderRadius.circular(24),
                                      onTap: () async {
                                        final itinerariesWithLocation =
                                            state.filteredItineraries
                                                .whereType<TripItinerary>()
                                                .where((itinerary) => itinerary.location != null)
                                                .toList();

                                        if (itinerariesWithLocation.isNotEmpty) {
                                          if (context.mounted) {
                                            Navigator.of(context).push(
                                              MaterialPageRoute(
                                                builder: (_) => TripMapViewScreen(
                                                  trip: widget.trip,
                                                  itineraries: itinerariesWithLocation,
                                                ),
                                              ),
                                            );
                                          }
                                        } else if (context.mounted) {
                                          showNoLocationsDialog(context);
                                        }
                                      },
                                      child: Container(
                                        height: 36,
                                        padding: const EdgeInsets.symmetric(horizontal: 24),
                                        decoration: BoxDecoration(
                                          color: colorScheme.secondary.withAlpha(204),
                                          borderRadius: BorderRadius.circular(24),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withAlpha(30),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: const Icon(Icons.map, color: Colors.white, size: 20),
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
    );
  }
}
