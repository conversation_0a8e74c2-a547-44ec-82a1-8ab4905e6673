import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/utils/rating_utils.dart';

import 'package:roamr/widgets/city_picker_widget.dart';
import 'package:roamr/widgets/table_calendar_date_range_picker.dart';
import 'package:roamr/widgets/themed_background.dart';

class AddTripScreen extends StatefulWidget {
  final Trip? trip;

  const AddTripScreen({super.key, this.trip});

  @override
  State<AddTripScreen> createState() => _AddTripScreenState();
}

class _AddTripScreenState extends State<AddTripScreen> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _tripTitleController = TextEditingController();
  final DateRangeController _tripDateRangeController = DateRangeController();
  String? _city;
  String? _countryCode;
  String? _countryName;

  @override
  void initState() {
    super.initState();
    if (widget.trip != null) {
      _tripTitleController.text = widget.trip!.title;
      _tripDateRangeController.setDateRange(widget.trip!.startDate, widget.trip!.endDate);
      _city = widget.trip!.city;
      _countryCode = widget.trip!.countryCode;
      _countryName = widget.trip!.countryName;
    }
  }

  @override
  void dispose() {
    _tripTitleController.dispose();
    _tripDateRangeController.dispose();
    super.dispose();
  }

  Future<void> _selectDateRange(BuildContext context) async {
    // Use our custom table calendar date range picker
    DateTimeRange? picked = await showTableCalendarDateRangePicker(
      context: context,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      initialStartDate: _tripDateRangeController.startDate,
      initialEndDate: _tripDateRangeController.endDate,
    );

    if (picked != null) {
      _tripDateRangeController.setDateRange(picked.start, picked.end);
      _formKey.currentState?.validate(); // Clear the validation error
    }
  }

  // Convert country code to emoji flag
  String countryCodeToEmoji(String countryCode) {
    if (countryCode.isEmpty) return '';

    // Convert country code to uppercase
    final code = countryCode.toUpperCase();

    // Convert each letter to the corresponding regional indicator symbol
    final int firstLetter = code.codeUnitAt(0) - 0x41 + 0x1F1E6;
    final int secondLetter = code.codeUnitAt(1) - 0x41 + 0x1F1E6;

    // Convert the code points to characters and return the flag
    return String.fromCharCode(firstLetter) + String.fromCharCode(secondLetter);
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ThemedBackground(
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          backgroundColor: ThemedBackground.getAppBarColor(context),
          elevation: 0,
          title: Text(widget.trip == null ? AppLocalizations.of(context)!.add_trip : AppLocalizations.of(context)!.edit_trip),
          actions: [
            TextButton(
              onPressed: () {
                if (_formKey.currentState!.validate()) {
                  if (_tripDateRangeController.startDate != null &&
                      _tripDateRangeController.endDate != null) {
                    if (widget.trip != null) {
                      // Updating an existing trip
                      context.read<AddTripBloc>().add(
                        UpdateTrip(
                          id: widget.trip!.id,
                          title: _tripTitleController.text,
                          startDate: _tripDateRangeController.startDate!,
                          endDate: _tripDateRangeController.endDate!,
                          city: _city,
                          countryCode: _countryCode,
                          countryName: _countryName,
                        ),
                      );
                      Navigator.pop(context);
                    } else {
                      // Adding a new trip
                      context.read<AddTripBloc>().add(
                        AddTrip(
                          title: _tripTitleController.text,
                          startDate: _tripDateRangeController.startDate!,
                          endDate: _tripDateRangeController.endDate!,
                          city: _city,
                          countryCode: _countryCode,
                          countryName: _countryName,
                        ),
                      );

                      // Navigate back first
                      Navigator.pop(context);

                      // Simply show the rating dialog after adding a trip
                      // This will be shown after returning to the previous screen
                      Future.delayed(const Duration(milliseconds: 500), () {
                        // Show the rating dialog without a context
                        // This avoids BuildContext across async gap issues
                        RatingUtils.requestReview();
                      });
                    }
                  }
                }
              },
              child: Text(
                AppLocalizations.of(context)!.save,
                style: TextStyle(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
        ),
        body: BlocBuilder<AddTripBloc, AddTripState>(
          builder: (context, state) {
            if (state.status == AddTripStatus.loading) {
              return const Center(child: CircularProgressIndicator());
            }

            return Container(
              decoration: BoxDecoration(
                color: colorScheme.primary.withAlpha(20),
              ),
              width: double.infinity,
              height: double.infinity,
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  padding: EdgeInsets.fromLTRB(16.0, MediaQuery.of(context).padding.top + 72.0, 16.0, 16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Trip details section
                      ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: theme.cardColor.withAlpha(150),
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [       // Trip name field
                                TextFormField(
                                  style: textTheme.bodyLarge,
                                  controller: _tripTitleController,
                                  decoration: InputDecoration(
                                    labelText: AppLocalizations.of(context)!.trip_name,
                                    hintText: AppLocalizations.of(context)!.trip_name_hint,
                                    border: const OutlineInputBorder(),
                                    floatingLabelBehavior: FloatingLabelBehavior.always,
                                    filled: true,
                                    fillColor: theme.cardColor.withAlpha(100),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return AppLocalizations.of(context)!.enter_title;
                                    }
                                    return null;
                                  },
                                  onChanged: (value) {
                                    _formKey.currentState?.validate(); // Clear the validation error in real time
                                  },
                                ),

                                const SizedBox(height: 16),

                                // Date range field
                                TextFormField(
                                  readOnly: true,
                                  controller: _tripDateRangeController,
                                  onTap: () => _selectDateRange(context),
                                  decoration: InputDecoration(
                                    labelText: AppLocalizations.of(context)!.date_range,
                                    hintText: AppLocalizations.of(context)!.date_range_example,
                                    border: const OutlineInputBorder(),
                                    floatingLabelBehavior: FloatingLabelBehavior.always,
                                    suffixIcon: const Icon(Icons.calendar_today),
                                    filled: true,
                                    fillColor: theme.cardColor.withAlpha(100),
                                  ),
                                  validator: (value) {
                                    if (_tripDateRangeController.startDate == null ||
                                        _tripDateRangeController.endDate == null) {
                                      return AppLocalizations.of(context)!.select_date_range;
                                    }
                                    return null;
                                  },
                                ),

                                const SizedBox(height: 16),

                                // City picker
                                const SizedBox(height: 16),

                                CityPickerWidget(
                                  initialCity: _city,
                                  onCitySelected: (city, countryCode, countryName) {
                                    setState(() {
                                      _city = city;
                                      _countryCode = countryCode;
                                      _countryName = countryName;
                                    });
                                  },
                                ),

                                // Country display
                                if (_countryCode != null && _countryCode!.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 16.0),
                                    child: Container(
                                      padding: const EdgeInsets.all(12.0),
                                      decoration: BoxDecoration(
                                        color: theme.cardColor.withAlpha(100),
                                        borderRadius: BorderRadius.circular(8.0),
                                        border: Border.all(color: theme.dividerColor),
                                      ),
                                      child: Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.all(8.0),
                                            decoration: BoxDecoration(
                                              color: theme.colorScheme.surface,
                                              shape: BoxShape.circle,
                                              boxShadow: [
                                                BoxShadow(
                                                  color: Colors.black.withAlpha(25),
                                                  blurRadius: 4,
                                                  offset: const Offset(0, 2),
                                                ),
                                              ],
                                            ),
                                            child: Text(
                                              countryCodeToEmoji(_countryCode!),
                                              style: const TextStyle(fontSize: 24),
                                            ),
                                          ),
                                          const SizedBox(width: 12),
                                          Expanded(
                                            child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  AppLocalizations.of(context)!.country,
                                                  style: textTheme.bodySmall?.copyWith(
                                                    color: theme.colorScheme.onSurface.withAlpha(153),
                                                  ),
                                                ),
                                                Text(
                                                  _countryName ?? '',
                                                  style: textTheme.titleMedium,
                                                  maxLines: 2,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class DateRangeController extends TextEditingController {
  DateTime? _startDate;
  DateTime? _endDate;

  DateTime? get startDate => _startDate;
  DateTime? get endDate => _endDate;

  void setDateRange(DateTime startDate, DateTime endDate) {
    _startDate = startDate;
    _endDate = endDate;
    text =
        "${DateFormat('MMM d, y').format(startDate)} - ${DateFormat('MMM d, y').format(endDate)}";
  }
}
