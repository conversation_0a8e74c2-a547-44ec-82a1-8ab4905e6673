import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/models/achievement.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/services/achievement_service.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/achievement_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AchievementsDetailScreen extends StatefulWidget {
  const AchievementsDetailScreen({super.key});

  @override
  State<AchievementsDetailScreen> createState() => _AchievementsDetailScreenState();
}

class _AchievementsDetailScreenState extends State<AchievementsDetailScreen> {
  bool _isLoading = true;
  List<Achievement> _achievements = [];
  List<Achievement> _destinationAchievements = [];
  List<Achievement> _expenseAchievements = [];
  List<Achievement> _frequencyAchievements = [];
  List<Achievement> _durationAchievements = [];
  List<Achievement> _flightAchievements = [];

  @override
  void initState() {
    super.initState();
    _loadAchievements();
  }

  Future<void> _loadAchievements() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the repository first
      final repository = context.read<TripRepository>();

      // Get trips directly from the repository instead of the bloc
      final trips = await repository.getAllTrips();

      if (trips.isEmpty) {
        setState(() {
          _isLoading = false;
          _achievements = Achievements.getAllAchievements();
          _filterAchievements();
        });
        return;
      }

      // Get all itineraries for all trips
      final List<TripItinerary> allItineraries = [];

      for (final trip in trips) {
        final itineraries = await repository.getTripItinerariesFuture(trip.id);
        allItineraries.addAll(itineraries);
      }

      // Calculate achievements
      final achievements = AchievementService.calculateAchievements(
        trips,
        allItineraries,
      );

      setState(() {
        _isLoading = false;
        _achievements = achievements;
        _filterAchievements();
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _achievements = Achievements.getAllAchievements();
        _filterAchievements();
      });
    }
  }

  void _filterAchievements() {
    // Show all achievements, both locked and unlocked
    _destinationAchievements = _achievements
        .where((a) => a.type == AchievementType.destination)
        .toList();
    _expenseAchievements = _achievements
        .where((a) => a.type == AchievementType.expense)
        .toList();
    _frequencyAchievements = _achievements
        .where((a) => a.type == AchievementType.frequency)
        .toList();
    _durationAchievements = _achievements
        .where((a) => a.type == AchievementType.duration)
        .toList();
    _flightAchievements = _achievements
        .where((a) => a.type == AchievementType.flight)
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: Text(localizations.achievements),
        backgroundColor: theme.colorScheme.primary.withAlpha(20),
        elevation: 0,
        foregroundColor: CustomTheme.getPrimaryTextColor(context),
      ),
      body: Container(
        decoration: BoxDecoration(
          color: theme.colorScheme.primary.withAlpha(20),
        ),
        width: double.infinity,
        height: double.infinity,
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: EdgeInsets.fromLTRB(16.0, MediaQuery.of(context).padding.top + 56.0, 16.0, 16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Description
                    Padding(
                      padding: const EdgeInsets.only(bottom: 8.0),
                      child: Text(
                        localizations.achievements_description,
                        style: theme.textTheme.bodyMedium,
                      ),
                    ),

                    // Destination achievements
                    _buildAchievementSection(
                      context,
                      localizations.destination_achievements,
                      AchievementUtils.getIconForType(AchievementType.destination),
                      _destinationAchievements,
                      AchievementType.destination,
                    ),

                    // Expense achievements
                    _buildAchievementSection(
                      context,
                      localizations.expense_achievements,
                      AchievementUtils.getIconForType(AchievementType.expense),
                      _expenseAchievements,
                      AchievementType.expense,
                    ),

                    // Frequency achievements
                    _buildAchievementSection(
                      context,
                      localizations.frequency_achievements,
                      AchievementUtils.getIconForType(AchievementType.frequency),
                      _frequencyAchievements,
                      AchievementType.frequency,
                    ),

                    // Duration achievements
                    _buildAchievementSection(
                      context,
                      localizations.duration_achievements,
                      AchievementUtils.getIconForType(AchievementType.duration),
                      _durationAchievements,
                      AchievementType.duration,
                    ),

                    // Flight achievements
                    _buildAchievementSection(
                      context,
                      localizations.flight_achievements,
                      AchievementUtils.getIconForType(AchievementType.flight),
                      _flightAchievements,
                      AchievementType.flight,
                    ),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildAchievementSection(
    BuildContext context,
    String title,
    IconData icon,
    List<Achievement> achievements,
    AchievementType type,
  ) {
    final theme = Theme.of(context);
    final color = AchievementUtils.getColorForType(type);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title
        Padding(
          padding: const EdgeInsets.only(top: 16.0, bottom: 8.0),
          child: Row(
            children: [
              Icon(
                icon,
                color: color,
                size: 24.0,
              ),
              const SizedBox(width: 8.0),
              Text(
                title,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: CustomTheme.getPrimaryTextColor(context),
                ),
              ),
            ],
          ),
        ),

        // Achievements grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: EdgeInsets.zero,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            childAspectRatio: 0.85,
            crossAxisSpacing: 4,
            mainAxisSpacing: 4,
          ),
          itemCount: achievements.length,
          itemBuilder: (context, index) {
            final achievement = achievements[index];
            return _buildAchievementItem(context, achievement);
          },
        ),

        const SizedBox(height: 8.0),
      ],
    );
  }

  Widget _buildAchievementItem(BuildContext context, Achievement achievement) {
    final theme = Theme.of(context);
    final isUnlocked = achievement.isUnlocked;
    final progress = achievement.progress;
    final target = achievement.targetValue;
    final localizedTitle = _getLocalizedTitle(context, achievement);
    final localizedDescription = _getLocalizedDescription(context, achievement);

    return Card(
      elevation: 0,
      margin: const EdgeInsets.all(2.0),
      color: theme.cardColor.withAlpha(150),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: theme.brightness == Brightness.dark
              ? Colors.white.withAlpha(30)
              : Colors.white.withAlpha(100),
          width: 0.5,
        ),
      ),
      child: InkWell(
        onTap: () {
          _showAchievementDetails(context, achievement, localizedTitle, localizedDescription);
        },
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Badge using the buildBadgeWithStar utility method
              SizedBox(
                height: 100,
                child: AchievementUtils.buildBadgeWithStar(
                  achievement.type,
                  achievement.level,
                  isUnlocked: isUnlocked,
                ),
              ),

              const SizedBox(height: 4.0),

              // Title
              Text(
                localizedTitle,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: isUnlocked
                      ? CustomTheme.getPrimaryTextColor(context)
                      : Colors.grey.shade400,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Progress text
              Text(
                '$progress/$target',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 9,
                  color: isUnlocked ? AchievementUtils.getColorForType(achievement.type) : Colors.grey.shade400,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAchievementDetails(
    BuildContext context,
    Achievement achievement,
    String title,
    String description,
  ) {
    final theme = Theme.of(context);
    final isUnlocked = achievement.isUnlocked;
    final progress = achievement.progress;
    final target = achievement.targetValue;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: CustomTheme.getCardBackgroundColor(context),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16.0),
        ),
        title: Row(
          children: [
            SizedBox(
              width: 40,
              height: 40,
              child: AchievementUtils.buildBadgeWithStar(
                achievement.type,
                achievement.level,
                isUnlocked: isUnlocked,
              ),
            ),
            const SizedBox(width: 8.0),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: isUnlocked
                      ? CustomTheme.getPrimaryTextColor(context)
                      : Colors.grey.shade400,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: CustomTheme.getPrimaryTextColor(context),
              ),
            ),
            const SizedBox(height: 16.0),
            LinearProgressIndicator(
              value: target > 0 ? (progress / target).clamp(0.0, 1.0) : 0.0,
              backgroundColor: Colors.grey.withAlpha(51),
              color: isUnlocked ? AchievementUtils.getColorForType(achievement.type) : Colors.grey.shade400,
            ),
            const SizedBox(height: 8.0),
            Text(
              '${AppLocalizations.of(context)!.progress}: $progress/$target',
              style: theme.textTheme.bodySmall?.copyWith(
                color: isUnlocked ? AchievementUtils.getColorForType(achievement.type) : Colors.grey.shade400,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              AppLocalizations.of(context)!.close,
              style: TextStyle(
                color: theme.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to get localized title
  String _getLocalizedTitle(BuildContext context, Achievement achievement) {
    final localizations = AppLocalizations.of(context)!;

    // Try to access the localized string directly
    switch (achievement.id) {
      case 'novice_traveller':
        return localizations.novice_traveller_title;
      case 'world_traveller':
        return localizations.world_traveller_title;
      case 'globetrotter':
        return localizations.globetrotter_title;
      case 'continental_explorer':
        return localizations.continental_explorer_title;
      case 'continental_collector':
        return localizations.continental_collector_title;
      case 'world_conqueror':
        return localizations.world_conqueror_title;
      case 'budget_tracker':
        return localizations.budget_tracker_title;
      case 'expense_manager':
        return localizations.expense_manager_title;
      case 'financial_voyager':
        return localizations.financial_voyager_title;
      case 'luxury_traveller':
        return localizations.luxury_traveller_title;
      case 'travel_beginner':
        return localizations.travel_beginner_title;
      case 'travel_enthusiast':
        return localizations.travel_enthusiast_title;
      case 'travel_addict':
        return localizations.travel_addict_title;
      case 'day_tripper':
        return localizations.day_tripper_title;
      case 'weekend_wanderer':
        return localizations.weekend_wanderer_title;
      case 'vacation_voyager':
        return localizations.vacation_voyager_title;
      case 'extended_explorer':
        return localizations.extended_explorer_title;
      case 'long_term_traveler':
        return localizations.long_term_traveler_title;
      case 'nomadic_adventurer':
        return localizations.nomadic_adventurer_title;
      case 'first_flight':
        return localizations.first_flight_title;
      case 'frequent_flyer':
        return localizations.frequent_flyer_title;
      case 'aviation_enthusiast':
        return localizations.aviation_enthusiast_title;
      default:
        return achievement.title;
    }
  }

  // Helper method to get localized description
  String _getLocalizedDescription(BuildContext context, Achievement achievement) {
    final localizations = AppLocalizations.of(context)!;

    // Try to access the localized string directly
    switch (achievement.id) {
      case 'novice_traveller':
        return localizations.novice_traveller_desc;
      case 'world_traveller':
        return localizations.world_traveller_desc;
      case 'globetrotter':
        return localizations.globetrotter_desc;
      case 'continental_explorer':
        return localizations.continental_explorer_desc;
      case 'continental_collector':
        return localizations.continental_collector_desc;
      case 'world_conqueror':
        return localizations.world_conqueror_desc;
      case 'budget_tracker':
        return localizations.budget_tracker_desc;
      case 'expense_manager':
        return localizations.expense_manager_desc;
      case 'financial_voyager':
        return localizations.financial_voyager_desc;
      case 'luxury_traveller':
        return localizations.luxury_traveller_desc;
      case 'travel_beginner':
        return localizations.travel_beginner_desc;
      case 'travel_enthusiast':
        return localizations.travel_enthusiast_desc;
      case 'travel_addict':
        return localizations.travel_addict_desc;
      case 'day_tripper':
        return localizations.day_tripper_desc;
      case 'weekend_wanderer':
        return localizations.weekend_wanderer_desc;
      case 'vacation_voyager':
        return localizations.vacation_voyager_desc;
      case 'extended_explorer':
        return localizations.extended_explorer_desc;
      case 'long_term_traveler':
        return localizations.long_term_traveler_desc;
      case 'nomadic_adventurer':
        return localizations.nomadic_adventurer_desc;
      case 'first_flight':
        return localizations.first_flight_desc;
      case 'frequent_flyer':
        return localizations.frequent_flyer_desc;
      case 'aviation_enthusiast':
        return localizations.aviation_enthusiast_desc;
      default:
        return achievement.description;
    }
  }
}
