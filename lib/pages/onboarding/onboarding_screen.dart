import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/language_bloc/language_bloc.dart';
import 'package:roamr/data/settings_preference.dart';
import 'package:roamr/pages/auth/login_register_page.dart';
import 'package:roamr/pages/onboarding/theme_screen.dart';
import 'package:roamr/pages/onboarding/welcome_screen.dart';
import 'package:roamr/pages/onboarding/feature_location_search_screen.dart';
import 'package:roamr/pages/onboarding/feature_itinerary_map_screen.dart';
import 'package:roamr/pages/onboarding/feature_expense_tracking_screen.dart';
import 'package:roamr/pages/onboarding/feature_trip_attachments_screen.dart';
import 'package:roamr/pages/onboarding/feature_comprehensive_planning_screen.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/widgets/themed_background.dart';

class OnboardingScreen extends StatefulWidget {
  // Add a callback to notify parent when onboarding is completed
  final VoidCallback? onOnboardingCompleted;
  final bool startAtSignIn;

  const OnboardingScreen({super.key, this.onOnboardingCompleted, this.startAtSignIn = false});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();

  // Helper: index of the sign-in step (last page)
  static int get signInPageIndex => 6; // 0-based, last page
}

class _OnboardingScreenState extends State<OnboardingScreen> {
  late int _currentPage;
  final int _totalPages = 7; // 8 onboarding screens
  bool _isLoading = false;
  bool _signInOnlyMode = false;

  @override
  void initState() {
    super.initState();
    if (widget.startAtSignIn) {
      _signInOnlyMode = true;
      Future.microtask(() async {
        final loginResult = await Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => const LoginRegisterPage(isOnboarding: true),
          ),
        );
        if (loginResult == true) {
          await RevenueCatService().presentPaywall();
          if (widget.onOnboardingCompleted != null) {
            widget.onOnboardingCompleted!();
          }
        }
      });
    } else {
      _currentPage = 0;
    }
  }

  Future<void> _nextPage() async {
    if (_currentPage < _totalPages - 2) {
      setState(() {
        _currentPage++;
      });
    } else if (_currentPage == _totalPages - 2) {
      // Last pre-sign-in step completed, mark onboarding as completed
      await SettingsPreferences.setOnboardingCompleted(true);
      setState(() {
        _currentPage++;
      });
    } else {
      setState(() {
        _isLoading = true;
      });

      // Show sign-in screen
      final loginResult = await Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const LoginRegisterPage(isOnboarding: true),
        ),
      );

      if (mounted && loginResult == true) {
        // Only after successful login, show the paywall
        await RevenueCatService().presentPaywall();
        if (mounted) {
          _completeOnboarding();
        }
      } else {
        // If login not successful, reset loading state
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      setState(() {
        _currentPage--;
      });
    }
  }

  void _completeOnboarding() async {
    // Only notifies parent; does not set any flags.
    if (!mounted) return;
    if (widget.onOnboardingCompleted != null) {
      widget.onOnboardingCompleted!();
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use BlocBuilder to rebuild when language changes
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        if (_signInOnlyMode) {
          // Show a loading spinner while sign-in modal is up
          return ThemedBackground(
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: const Center(child: CircularProgressIndicator()),
            ),
          );
        } else {
          return ThemedBackground(
            child: Scaffold(
              backgroundColor: Colors.transparent,
              body: SafeArea(
                child: Stack(
                  children: [
                    // Main content
                    Column(
                      children: [
                        // Navigation buttons (back and skip)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Back button (only show if not on first page)
                            _currentPage > 0
                                ? IconButton(
                                  icon: const Icon(CupertinoIcons.back),
                                  onPressed: _previousPage,
                                )
                                : const SizedBox(
                                  width: 60,
                                ), // Empty space for alignment
                          ],
                        ),

                        // Page content
                        Expanded(
                          child: IndexedStack(
                            index: _currentPage,
                            children: [
                              WelcomeScreen(onNext: _nextPage),
                              FeatureComprehensivePlanningScreen(onNext: _nextPage),
                              FeatureLocationSearchScreen(onNext: _nextPage),
                              FeatureItineraryMapScreen(onNext: _nextPage),
                              FeatureExpenseTrackingScreen(onNext: _nextPage),
                              FeatureTripAttachmentsScreen(onNext: _nextPage),
                              ThemeScreen(onNext: _nextPage),
                            ],
                          ),
                        ),
                      ],
                    ),
                    // Loading overlay
                    if (_isLoading)
                      Positioned.fill(
                        child: Container(
                          color: Colors.black.withOpacity(0.5),
                          child: const Center(child: CircularProgressIndicator()),
                        ),
                      ),
                    // Dot indicators at the bottom
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 16.0,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: List.generate(_totalPages, (index) {
                          return Container(
                            width: 8,
                            height: 8,
                            margin: const EdgeInsets.symmetric(horizontal: 4),
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: _currentPage == index
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant.withOpacity(0.3),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }
      },
    );
  }
}
