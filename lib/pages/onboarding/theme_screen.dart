import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/blocs/theme_bloc/theme_bloc.dart';
import 'package:roamr/blocs/language_bloc/language_bloc.dart';
import 'package:roamr/widgets/themed_background.dart';

class ThemeScreen extends StatelessWidget {
  final VoidCallback onNext;

  const ThemeScreen({
    super.key,
    required this.onNext,
  });

  @override
  Widget build(BuildContext context) {
    // Use BlocBuilder to rebuild when language changes
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, languageState) {
        final theme = Theme.of(context);
        final localizations = AppLocalizations.of(context)!;

        return ThemedBackground(
          child: Padding(
            padding: const EdgeInsets.only(top: 24.0, right: 24.0, left: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Text(
                  localizations.onboarding_theme_title,
                  style: theme.textTheme.headlineMedium,
                ),

                const SizedBox(height: 8),

                // Subtitle
                Text(
                  localizations.onboarding_theme_subtitle,
                  style: theme.textTheme.bodyLarge,
                ),

                const SizedBox(height: 40),

                // Theme options
                BlocBuilder<ThemeBloc, ThemeState>(
                  builder: (context, state) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        _buildThemeOption(
                          context,
                          localizations.light,
                          Icons.light_mode,
                          ThemeMode.light,
                          state.themeMode,
                        ),
                        const SizedBox(height: 16),
                        _buildThemeOption(
                          context,
                          localizations.dark,
                          Icons.dark_mode,
                          ThemeMode.dark,
                          state.themeMode,
                        ),
                        const SizedBox(height: 16),
                        _buildThemeOption(
                          context,
                          localizations.system,
                          Icons.settings_suggest,
                          ThemeMode.system,
                          state.themeMode,
                        ),
                      ],
                    );
                  },
                ),

                const Spacer(),

                // Consistent bottom button style
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 40.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF007F5F), Color(0xFF00BFA5)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: onNext,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                    ),
                    child: Text(
                      localizations.onboarding_theme_button,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildThemeOption(
    BuildContext context,
    String text,
    IconData icon,
    ThemeMode themeMode,
    ThemeMode currentThemeMode,
  ) {
    final theme = Theme.of(context);
    final isSelected = themeMode == currentThemeMode;

    return InkWell(
      onTap: () {
        context.read<ThemeBloc>().add(ChangeThemeEvent(themeMode));
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primary.withAlpha(25) // ~0.1 opacity
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withAlpha(76), // ~0.3 opacity
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: isSelected
                  ? theme.colorScheme.primary
                  : theme.colorScheme.onSurface,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                text,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: isSelected
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
              ),
          ],
        ),
      ),
    );
  }
} 