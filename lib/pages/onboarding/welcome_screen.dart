import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/blocs/language_bloc/language_bloc.dart';
import 'package:roamr/widgets/app_logo.dart';
import 'package:roamr/widgets/themed_background.dart';

class WelcomeScreen extends StatelessWidget {
  final VoidCallback onNext;

  const WelcomeScreen({super.key, required this.onNext});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<LanguageBloc, LanguageState>(
      builder: (context, state) {
        final theme = Theme.of(context);
        final localizations = AppLocalizations.of(context)!;

        return ThemedBackground(
          child: SafeArea(
            child: Column(
              children: [
                // Fixed header with page indicator
                const SizedBox(height: 16),

                // Scrollable content
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24.0,
                      vertical: 16.0,
                    ),
                    child: <PERSON>umn(
                      children: [
                        // App logo
                        const AppLogo(
                          height: 120,
                          alignment: Alignment.center,
                          padding: EdgeInsets.symmetric(vertical: 16),
                        ),

                        const SizedBox(height: 24),

                        // Welcome title
                        Text(
                          localizations.onboarding_welcome_title,
                          style: theme.textTheme.headlineMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 16),

                        // Welcome subtitle
                        Text(
                          localizations.onboarding_welcome_subtitle,
                          style: theme.textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 32),

                        // Language selection
                        _buildLanguageSelector(context),
                        const SizedBox(height: 16),
                      ],
                    ),
                  ),
                ),

                // Fixed bottom button
                Container(
                  width: double.infinity,
                  margin: const EdgeInsets.only(
                    bottom: 40.0,
                    left: 24.0,
                    right: 24.0,
                    top: 8.0,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF007F5F), Color(0xFF00BFA5)],
                      begin: Alignment.centerLeft,
                      end: Alignment.centerRight,
                    ),
                    borderRadius: BorderRadius.circular(30.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: ElevatedButton(
                    onPressed: onNext,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.transparent,
                      shadowColor: Colors.transparent,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(30.0),
                      ),
                    ),
                    child: Text(
                      localizations.onboarding_welcome_button,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLanguageSelector(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Language selection title
        Text(
          localizations.onboarding_select_language,
          style: theme.textTheme.titleMedium,
        ),

        const SizedBox(height: 12),

        // Language options
        Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withAlpha(76), // ~0.3 opacity
            ),
          ),
          child: BlocBuilder<LanguageBloc, LanguageState>(
            builder: (context, state) {
              return Column(
                children: [
                  _buildLanguageOption(
                    context,
                    'English',
                    'en',
                    state.locale.languageCode,
                  ),
                  _buildDivider(),
                  _buildLanguageOption(
                    context,
                    'Español',
                    'es',
                    state.locale.languageCode,
                  ),
                  _buildDivider(),
                  _buildLanguageOption(
                    context,
                    'हिंदी',
                    'hi',
                    state.locale.languageCode,
                  ),
                  _buildDivider(),
                  _buildLanguageOption(
                    context,
                    'Deutsch',
                    'de',
                    state.locale.languageCode,
                  ),
                  _buildDivider(),
                  _buildLanguageOption(
                    context,
                    'Français',
                    'fr',
                    state.locale.languageCode,
                  ),
                  _buildDivider(),
                  _buildLanguageOption(
                    context,
                    'ไทย',
                    'th',
                    state.locale.languageCode,
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLanguageOption(
    BuildContext context,
    String label,
    String languageCode,
    String currentLanguageCode,
  ) {
    final theme = Theme.of(context);
    final isSelected = languageCode == currentLanguageCode;

    return InkWell(
      onTap: () {
        context.read<LanguageBloc>().add(
          ChangeLanguageEvent(Locale(languageCode)),
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        child: Row(
          children: [
            Text(
              label,
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? theme.colorScheme.primary : null,
              ),
            ),
            const Spacer(),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: theme.colorScheme.primary,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return const Divider(height: 1, thickness: 0.5, indent: 16, endIndent: 16);
  }
}
