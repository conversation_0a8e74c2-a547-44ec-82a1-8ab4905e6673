import 'dart:developer' as developer;
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/utils/map_utils.dart';

class AnalyticsService {
  // Global static instance
  static final AnalyticsService instance = AnalyticsService._internal();

  // Firebase Analytics instance
  final FirebaseAnalytics _analytics = FirebaseAnalytics.instance;

  // Private constructor
  AnalyticsService._internal() {
    // Enable debug logging for Firebase Analytics
    _analytics.setAnalyticsCollectionEnabled(true);

    if (kDebugMode) {
      // Enable debug view for Firebase Analytics
      _analytics.setAnalyticsCollectionEnabled(true);
      _analytics.setSessionTimeoutDuration(const Duration(minutes: 30));

      // Log the app instance ID for debugging
      _getAppInstanceId();
    }
  }

  Future<void> _getAppInstanceId() async {
    try {
      final appInstanceId = await FirebaseAnalytics.instance.appInstanceId;
      developer.log(
        'Firebase Analytics App Instance ID: $appInstanceId',
        name: 'AnalyticsService',
      );
    } catch (e) {
      developer.log(
        'Failed to get app instance ID: $e',
        name: 'AnalyticsService',
      );
    }
  }

  Future<void> logEvent({
    required String name,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      // Debug logging
      if (kDebugMode) {
        debugPrint('\n===== Analytics Event =====');
        debugPrint('Event: $name');
        if (parameters != null) {
          debugPrint('Parameters:');
          parameters.forEach((key, value) {
            debugPrint('  $key: $value (${value.runtimeType})');
          });
        }
        debugPrint('=========================\n');
      }

      // Filter and cast parameters
      final filteredParams = parameters?.filterAndCast();

      // Log the event
      await _analytics.logEvent(name: name, parameters: filteredParams);

      // Log success
      developer.log(
        '✅ Successfully logged event: $name',
        name: 'AnalyticsService',
      );
      if (kDebugMode && filteredParams != null) {
        developer.log(
          '📊 Event parameters: $filteredParams',
          name: 'AnalyticsService',
        );
      }
    } catch (e, stackTrace) {
      // Log detailed error
      developer.log(
        '❌ Failed to log event $name: $e',
        name: 'AnalyticsService',
        error: e,
        stackTrace: stackTrace,
      );

      // Log parameters that caused the error
      if (kDebugMode) {
        developer.log(
          '⚠️ Event parameters that caused error:',
          name: 'AnalyticsService',
        );
        parameters?.forEach((key, value) {
          developer.log(
            '  $key: $value (${value.runtimeType})',
            name: 'AnalyticsService',
          );
        });
      }

      // Re-throw in debug mode to fail fast
      if (kDebugMode) {
        rethrow;
      }
    }
  }

  // Event names
  // User Engagement
  static const String _eventAppOpen = 'app_open';
  static const String _eventAppBackground = 'custom_app_background';
  static const String _eventSessionStart = 'user_session_start';
  static const String _eventSessionEnd = 'user_session_end';

  // Trip Management
  static const String _eventTripCreated = 'trip_created';
  static const String _eventTripUpdated = 'trip_updated';
  static const String _eventTripDeleted = 'trip_deleted';
  static const String _eventTripViewed = 'trip_viewed';

  // Content Interaction
  static const String _eventAttachmentAdded = 'attachment_added';
  static const String _eventMapViewed = 'map_viewed';
  static const String _eventTimelineViewed = 'timeline_viewed';

  // Itinerary Events
  static const String _eventItineraryItemAdded = 'itinerary_item_added';
  static const String _eventItineraryItemUpdated = 'itinerary_item_updated';
  static const String _eventItineraryItemDeleted = 'itinerary_item_deleted';

  // Map Search Events
  static const String _eventMapSearch = 'map_search';
  static const String _eventMapSearchOpened = 'map_search_opened';

  // Getter for FirebaseAnalytics instance
  FirebaseAnalytics get analytics => _analytics;

  // Track screen views
  Future<void> logScreenView({
    required String screenName,
    String? screenClass,
    Map<String, Object>? parameters,
  }) async {
    try {
      developer.log(
        'Logging screen view: $screenName',
        name: 'AnalyticsService',
      );
      await _analytics.logScreenView(
        screenName: screenName,
        screenClass: screenClass,
        parameters: parameters,
      );
    } catch (e) {
      developer.log('Error logging screen view: $e', name: 'AnalyticsService');
      rethrow;
    }
  }

  // Set user properties
  Future<void> setUserProperty({
    required String name,
    required String? value,
  }) async {
    await _analytics.setUserProperty(name: name, value: value);
  }

  // Set user ID
  Future<void> setUserId(String? userId) async {
    await _analytics.setUserId(id: userId);
  }

  // Reset analytics data (for testing or when user logs out)
  Future<void> resetAnalyticsData() async {
    await _analytics.resetAnalyticsData();
  }

  // User Engagement Events
  Future<void> logAppOpen() async {
    final params =
        {'timestamp': DateTime.now().toIso8601String()}.filterAndCast();

    await logEvent(name: _eventAppOpen, parameters: params);

    // Also log session start when app opens
    await logSessionStart();
  }

  Future<void> logAppBackground() async {
    final params =
        {'timestamp': DateTime.now().toIso8601String()}.filterAndCast();

    await logEvent(name: _eventAppBackground, parameters: params);

    // Also log session end when app goes to background
    await logSessionEnd();
  }

  // Trip Management Events
  Future<void> logTripCreated({required Trip trip}) async {
    final params =
        {
          'trip_id': trip.id,
          'trip_name': trip.title,
          'trip_type': '${trip.startDate.year}-${trip.endDate.year}',
          'start_date': trip.startDate.toIso8601String(),
          'end_date': trip.endDate.toIso8601String(),
          'duration_days': trip.endDate.difference(trip.startDate).inDays + 1,
          'timestamp': DateTime.now().toIso8601String(),
          'city': trip.city,
          'country': trip.countryName,
        }.filterAndCast();

    await logEvent(name: _eventTripCreated, parameters: params);
  }

  Future<void> logTripUpdated({required Trip trip}) async {
    final params =
        {
          'trip_id': trip.id,
          'trip_name': trip.title,
          'trip_type': '${trip.startDate.year}-${trip.endDate.year}',
          'start_date': trip.startDate.toIso8601String(),
          'end_date': trip.endDate.toIso8601String(),
          'duration_days': trip.endDate.difference(trip.startDate).inDays + 1,
          'timestamp': DateTime.now().toIso8601String(),
          'city': trip.city,
          'country': trip.countryName,
        }.filterAndCast();

    await logEvent(name: _eventTripUpdated, parameters: params);
  }

  Future<void> logTripDeleted({required String tripId}) async {
    final params = {
      'trip_id': tripId,
      'timestamp': DateTime.now().toIso8601String(),
    }.filterAndCast();

    await logEvent(name: _eventTripDeleted, parameters: params);
  }

  Future<void> logTripViewed({required Trip trip}) async {
    final params =
        {
          'trip_id': trip.id,
          'trip_name': trip.title,
          'trip_type': '${trip.startDate.year}-${trip.endDate.year}',
          'days_until_trip': DateTime.now().difference(trip.startDate).inDays,
          'timestamp': DateTime.now().toIso8601String(),
          'city': trip.city,
          'country': trip.countryName,
        }.filterAndCast();

    await logEvent(name: _eventTripViewed, parameters: params);
  }

  // Session Events
  Future<void> logSessionStart() async {
    await logEvent(
      name: _eventSessionStart,
      parameters: {'timestamp': DateTime.now().toIso8601String()},
    );
  }

  Future<void> logSessionEnd() async {
    await logEvent(
      name: _eventSessionEnd,
      parameters: {
        'timestamp': DateTime.now().toIso8601String(),
        'session_duration_seconds':
            _sessionStartTime != null
                ? DateTime.now().difference(_sessionStartTime!).inSeconds
                : null,
      },
    );
    _sessionStartTime = null;
  }

  DateTime? _sessionStartTime;

  // Content Interaction Events
  Future<void> logAttachmentAdded({
    required TripItinerary itinerary,
    required String attachmentType,
    int? fileSizeKB,
  }) async {
    final params = <String, dynamic>{
      'attachment_type': attachmentType,
      'file_size_kb': fileSizeKB,
      'timestamp': DateTime.now().toIso8601String(),
      'itinerary_id': itinerary.id,
      'itinerary_title': itinerary.title,
      'itinerary_date': itinerary.date?.toIso8601String(),
      'itinerary_category': itinerary.category?.name,
      'itinerary_has_location': itinerary.location != null,
    };

    await logEvent(
      name: _eventAttachmentAdded,
      parameters: params,
    );
  }

  Future<void> logMapViewed({required Trip trip}) async {
    final params = <String, dynamic>{
      'trip_id': trip.id,
      'trip_name': trip.title,
      'trip_city': trip.city,
      'trip_country': trip.countryName,
      'trip_start_date': trip.startDate.toIso8601String(),
      'trip_end_date': trip.endDate.toIso8601String(),
      'trip_duration_days': trip.endDate.difference(trip.startDate).inDays + 1,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await logEvent(
      name: _eventMapViewed,
      parameters: params,
    );
  }

  Future<void> logTimelineViewed({required Trip trip}) async {
    final params = <String, dynamic>{
      'trip_id': trip.id,
      'trip_name': trip.title,
      'trip_city': trip.city,
      'trip_country': trip.countryName,
      'trip_start_date': trip.startDate.toIso8601String(),
      'trip_end_date': trip.endDate.toIso8601String(),
      'trip_duration_days': trip.endDate.difference(trip.startDate).inDays + 1,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await logEvent(
      name: _eventTimelineViewed,
      parameters: params,
    );
  }

  /// Logs when a user performs a map search for an itinerary location
  /// [itinerary] - The itinerary being updated with the new location
  /// [searchQuery] - The search term used by the user
  /// [selectedPlaceName] - The name of the place selected from search results
  /// Logs when a user opens the map search interface
  /// [itinerary] - The itinerary being edited when the search was opened
  Future<void> logMapSearchOpened({required TripItinerary itinerary}) async {
    final params = <String, dynamic>{
      'itinerary_id': itinerary.id,
      'itinerary_title': itinerary.title,
      'has_location': itinerary.location != null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await logEvent(
      name: _eventMapSearchOpened,
      parameters: params,
    );
  }

  /// Logs when a user performs a map search and selects a location
  /// [itinerary] - The itinerary being updated with the selected location
  /// [searchQuery] - The search term used by the user
  /// [selectedPlaceName] - The name of the place selected from search results
  Future<void> logMapSearch({
    required TripItinerary itinerary,
    required String searchQuery,
    required String selectedPlaceName,
  }) async {
    final params = <String, dynamic>{
      'itinerary_id': itinerary.id,
      'itinerary_title': itinerary.title,
      'search_query': searchQuery,
      'selected_place_name': selectedPlaceName,
      'has_location': itinerary.location != null,
      'timestamp': DateTime.now().toIso8601String(),
    };

    await logEvent(
      name: _eventMapSearch,
      parameters: params,
    );
  }

  // Itinerary Events
  Future<void> logItineraryItemAdded({
    required String tripId,
    required TripItinerary itinerary,
  }) async {
    final params =
        {
          'trip_id': tripId,
          'item_id': itinerary.id,
          'item_type': itinerary.category.name,
          'title': itinerary.title,
          'date': itinerary.date.toIso8601String(),
          'timestamp': DateTime.now().toIso8601String(),
          'amount': itinerary.amount,
          'location_lat': itinerary.location?.latitude,
          'location_lng': itinerary.location?.longitude,
          'location_text': itinerary.locationText,
          'airline': itinerary.airlineName,
          'flight_number': itinerary.flightNumber,
          'checkout_date': itinerary.checkoutDate?.toIso8601String(),
          'description': itinerary.description,
          'rank': itinerary.rank,
        }.filterAndCast();

    await logEvent(
      name: _eventItineraryItemAdded,
      parameters: params,
    );
  }

  Future<void> logItineraryItemUpdated({
    required String tripId,
    required TripItinerary itinerary,
  }) async {
    final params =
        {
          'trip_id': tripId,
          'item_id': itinerary.id,
          'item_type': itinerary.category.name,
          'title': itinerary.title,
          'date': itinerary.date.toIso8601String(),
          'timestamp': DateTime.now().toIso8601String(),
          'amount': itinerary.amount,
          'location_lat': itinerary.location?.latitude,
          'location_lng': itinerary.location?.longitude,
          'location_text': itinerary.locationText,
          'airline': itinerary.airlineName,
          'flight_number': itinerary.flightNumber,
          'checkout_date': itinerary.checkoutDate?.toIso8601String(),
          'description': itinerary.description,
          'rank': itinerary.rank,
        }.filterAndCast();

    await logEvent(
      name: _eventItineraryItemUpdated,
      parameters: params,
    );
  }

  Future<void> logItineraryItemDeleted({
    required String tripId,
    required TripItinerary itinerary,
  }) async {
    final params =
        {
          'trip_id': tripId,
          'item_id': itinerary.id,
          'item_type': itinerary.category.name,
          'title': itinerary.title,
          'date': itinerary.date.toIso8601String(),
          'timestamp': DateTime.now().toIso8601String(),
          'amount': itinerary.amount,
          'location_lat': itinerary.location?.latitude,
          'location_lng': itinerary.location?.longitude,
          'location_text': itinerary.locationText,
          'airline': itinerary.airlineName,
          'flight_number': itinerary.flightNumber,
          'checkout_date': itinerary.checkoutDate?.toIso8601String(),
          'description': itinerary.description,
          'rank': itinerary.rank,
        }.filterAndCast();

    await logEvent(
      name: _eventItineraryItemDeleted,
      parameters: params,
    );
  }
}
