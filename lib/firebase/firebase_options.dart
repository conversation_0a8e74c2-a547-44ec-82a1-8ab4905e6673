// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBD5MTaUtNZHieitwkXiXT3YwksDdAkG30',
    appId: '1:230444048720:web:ca6932bf34ea9918150c55',
    messagingSenderId: '230444048720',
    projectId: 'roamr-144b6',
    authDomain: 'roamr-144b6.firebaseapp.com',
    storageBucket: 'roamr-144b6.firebasestorage.app',
    measurementId: 'G-8CGD5D368T',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDKoEao8BiT_H8KuO-EpVpdZfcJstJDQF8',
    appId: '1:230444048720:android:307af1533e2b15e0150c55',
    messagingSenderId: '230444048720',
    projectId: 'roamr-144b6',
    storageBucket: 'roamr-144b6.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCZhfPrjzDnCzCT4Ap4se_n--yGvThwLrk',
    appId: '1:230444048720:ios:432060374ea2c66b150c55',
    messagingSenderId: '230444048720',
    projectId: 'roamr-144b6',
    storageBucket: 'roamr-144b6.firebasestorage.app',
    iosBundleId: 'com.pankaj6apr.roamr',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyCZhfPrjzDnCzCT4Ap4se_n--yGvThwLrk',
    appId: '1:230444048720:ios:432060374ea2c66b150c55',
    messagingSenderId: '230444048720',
    projectId: 'roamr-144b6',
    storageBucket: 'roamr-144b6.firebasestorage.app',
    iosBundleId: 'com.pankaj6apr.roamr',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyBD5MTaUtNZHieitwkXiXT3YwksDdAkG30',
    appId: '1:230444048720:web:251dd930ccfa5b91150c55',
    messagingSenderId: '230444048720',
    projectId: 'roamr-144b6',
    authDomain: 'roamr-144b6.firebaseapp.com',
    storageBucket: 'roamr-144b6.firebasestorage.app',
    measurementId: 'G-00HKWQ5J6N',
  );
}
