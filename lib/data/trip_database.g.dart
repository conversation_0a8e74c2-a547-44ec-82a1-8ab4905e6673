// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'trip_database.dart';

// ignore_for_file: type=lint
class $TripsTable extends Trips with TableInfo<$TripsTable, TripItem> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $TripsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 36),
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      clientDefault: () => Uuid().v4());
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 100),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _startDateMeta =
      const VerificationMeta('startDate');
  @override
  late final GeneratedColumn<DateTime> startDate = GeneratedColumn<DateTime>(
      'start_date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _endDateMeta =
      const VerificationMeta('endDate');
  @override
  late final GeneratedColumn<DateTime> endDate = GeneratedColumn<DateTime>(
      'end_date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _cityMeta = const VerificationMeta('city');
  @override
  late final GeneratedColumn<String> city = GeneratedColumn<String>(
      'city', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _countryCodeMeta =
      const VerificationMeta('countryCode');
  @override
  late final GeneratedColumn<String> countryCode = GeneratedColumn<String>(
      'country_code', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _countryNameMeta =
      const VerificationMeta('countryName');
  @override
  late final GeneratedColumn<String> countryName = GeneratedColumn<String>(
      'country_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _autocompleteDataMeta =
      const VerificationMeta('autocompleteData');
  @override
  late final GeneratedColumn<String> autocompleteData = GeneratedColumn<String>(
      'autocomplete_data', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        title,
        startDate,
        endDate,
        city,
        countryCode,
        countryName,
        autocompleteData
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'trips';
  @override
  VerificationContext validateIntegrity(Insertable<TripItem> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('start_date')) {
      context.handle(_startDateMeta,
          startDate.isAcceptableOrUnknown(data['start_date']!, _startDateMeta));
    } else if (isInserting) {
      context.missing(_startDateMeta);
    }
    if (data.containsKey('end_date')) {
      context.handle(_endDateMeta,
          endDate.isAcceptableOrUnknown(data['end_date']!, _endDateMeta));
    } else if (isInserting) {
      context.missing(_endDateMeta);
    }
    if (data.containsKey('city')) {
      context.handle(
          _cityMeta, city.isAcceptableOrUnknown(data['city']!, _cityMeta));
    }
    if (data.containsKey('country_code')) {
      context.handle(
          _countryCodeMeta,
          countryCode.isAcceptableOrUnknown(
              data['country_code']!, _countryCodeMeta));
    }
    if (data.containsKey('country_name')) {
      context.handle(
          _countryNameMeta,
          countryName.isAcceptableOrUnknown(
              data['country_name']!, _countryNameMeta));
    }
    if (data.containsKey('autocomplete_data')) {
      context.handle(
          _autocompleteDataMeta,
          autocompleteData.isAcceptableOrUnknown(
              data['autocomplete_data']!, _autocompleteDataMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => const {};
  @override
  TripItem map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return TripItem(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      startDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}start_date'])!,
      endDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}end_date'])!,
      city: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}city']),
      countryCode: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}country_code']),
      countryName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}country_name']),
      autocompleteData: attachedDatabase.typeMapping.read(
          DriftSqlType.string, data['${effectivePrefix}autocomplete_data']),
    );
  }

  @override
  $TripsTable createAlias(String alias) {
    return $TripsTable(attachedDatabase, alias);
  }
}

class TripItem extends DataClass implements Insertable<TripItem> {
  final String id;
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String? city;
  final String? countryCode;
  final String? countryName;
  final String? autocompleteData;
  const TripItem(
      {required this.id,
      required this.title,
      required this.startDate,
      required this.endDate,
      this.city,
      this.countryCode,
      this.countryName,
      this.autocompleteData});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['title'] = Variable<String>(title);
    map['start_date'] = Variable<DateTime>(startDate);
    map['end_date'] = Variable<DateTime>(endDate);
    if (!nullToAbsent || city != null) {
      map['city'] = Variable<String>(city);
    }
    if (!nullToAbsent || countryCode != null) {
      map['country_code'] = Variable<String>(countryCode);
    }
    if (!nullToAbsent || countryName != null) {
      map['country_name'] = Variable<String>(countryName);
    }
    if (!nullToAbsent || autocompleteData != null) {
      map['autocomplete_data'] = Variable<String>(autocompleteData);
    }
    return map;
  }

  TripsCompanion toCompanion(bool nullToAbsent) {
    return TripsCompanion(
      id: Value(id),
      title: Value(title),
      startDate: Value(startDate),
      endDate: Value(endDate),
      city: city == null && nullToAbsent ? const Value.absent() : Value(city),
      countryCode: countryCode == null && nullToAbsent
          ? const Value.absent()
          : Value(countryCode),
      countryName: countryName == null && nullToAbsent
          ? const Value.absent()
          : Value(countryName),
      autocompleteData: autocompleteData == null && nullToAbsent
          ? const Value.absent()
          : Value(autocompleteData),
    );
  }

  factory TripItem.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return TripItem(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      startDate: serializer.fromJson<DateTime>(json['startDate']),
      endDate: serializer.fromJson<DateTime>(json['endDate']),
      city: serializer.fromJson<String?>(json['city']),
      countryCode: serializer.fromJson<String?>(json['countryCode']),
      countryName: serializer.fromJson<String?>(json['countryName']),
      autocompleteData: serializer.fromJson<String?>(json['autocompleteData']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'startDate': serializer.toJson<DateTime>(startDate),
      'endDate': serializer.toJson<DateTime>(endDate),
      'city': serializer.toJson<String?>(city),
      'countryCode': serializer.toJson<String?>(countryCode),
      'countryName': serializer.toJson<String?>(countryName),
      'autocompleteData': serializer.toJson<String?>(autocompleteData),
    };
  }

  TripItem copyWith(
          {String? id,
          String? title,
          DateTime? startDate,
          DateTime? endDate,
          Value<String?> city = const Value.absent(),
          Value<String?> countryCode = const Value.absent(),
          Value<String?> countryName = const Value.absent(),
          Value<String?> autocompleteData = const Value.absent()}) =>
      TripItem(
        id: id ?? this.id,
        title: title ?? this.title,
        startDate: startDate ?? this.startDate,
        endDate: endDate ?? this.endDate,
        city: city.present ? city.value : this.city,
        countryCode: countryCode.present ? countryCode.value : this.countryCode,
        countryName: countryName.present ? countryName.value : this.countryName,
        autocompleteData: autocompleteData.present
            ? autocompleteData.value
            : this.autocompleteData,
      );
  TripItem copyWithCompanion(TripsCompanion data) {
    return TripItem(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      startDate: data.startDate.present ? data.startDate.value : this.startDate,
      endDate: data.endDate.present ? data.endDate.value : this.endDate,
      city: data.city.present ? data.city.value : this.city,
      countryCode:
          data.countryCode.present ? data.countryCode.value : this.countryCode,
      countryName:
          data.countryName.present ? data.countryName.value : this.countryName,
      autocompleteData: data.autocompleteData.present
          ? data.autocompleteData.value
          : this.autocompleteData,
    );
  }

  @override
  String toString() {
    return (StringBuffer('TripItem(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('city: $city, ')
          ..write('countryCode: $countryCode, ')
          ..write('countryName: $countryName, ')
          ..write('autocompleteData: $autocompleteData')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(id, title, startDate, endDate, city,
      countryCode, countryName, autocompleteData);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is TripItem &&
          other.id == this.id &&
          other.title == this.title &&
          other.startDate == this.startDate &&
          other.endDate == this.endDate &&
          other.city == this.city &&
          other.countryCode == this.countryCode &&
          other.countryName == this.countryName &&
          other.autocompleteData == this.autocompleteData);
}

class TripsCompanion extends UpdateCompanion<TripItem> {
  final Value<String> id;
  final Value<String> title;
  final Value<DateTime> startDate;
  final Value<DateTime> endDate;
  final Value<String?> city;
  final Value<String?> countryCode;
  final Value<String?> countryName;
  final Value<String?> autocompleteData;
  final Value<int> rowid;
  const TripsCompanion({
    this.id = const Value.absent(),
    this.title = const Value.absent(),
    this.startDate = const Value.absent(),
    this.endDate = const Value.absent(),
    this.city = const Value.absent(),
    this.countryCode = const Value.absent(),
    this.countryName = const Value.absent(),
    this.autocompleteData = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  TripsCompanion.insert({
    this.id = const Value.absent(),
    required String title,
    required DateTime startDate,
    required DateTime endDate,
    this.city = const Value.absent(),
    this.countryCode = const Value.absent(),
    this.countryName = const Value.absent(),
    this.autocompleteData = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : title = Value(title),
        startDate = Value(startDate),
        endDate = Value(endDate);
  static Insertable<TripItem> custom({
    Expression<String>? id,
    Expression<String>? title,
    Expression<DateTime>? startDate,
    Expression<DateTime>? endDate,
    Expression<String>? city,
    Expression<String>? countryCode,
    Expression<String>? countryName,
    Expression<String>? autocompleteData,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (startDate != null) 'start_date': startDate,
      if (endDate != null) 'end_date': endDate,
      if (city != null) 'city': city,
      if (countryCode != null) 'country_code': countryCode,
      if (countryName != null) 'country_name': countryName,
      if (autocompleteData != null) 'autocomplete_data': autocompleteData,
      if (rowid != null) 'rowid': rowid,
    });
  }

  TripsCompanion copyWith(
      {Value<String>? id,
      Value<String>? title,
      Value<DateTime>? startDate,
      Value<DateTime>? endDate,
      Value<String?>? city,
      Value<String?>? countryCode,
      Value<String?>? countryName,
      Value<String?>? autocompleteData,
      Value<int>? rowid}) {
    return TripsCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      city: city ?? this.city,
      countryCode: countryCode ?? this.countryCode,
      countryName: countryName ?? this.countryName,
      autocompleteData: autocompleteData ?? this.autocompleteData,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (startDate.present) {
      map['start_date'] = Variable<DateTime>(startDate.value);
    }
    if (endDate.present) {
      map['end_date'] = Variable<DateTime>(endDate.value);
    }
    if (city.present) {
      map['city'] = Variable<String>(city.value);
    }
    if (countryCode.present) {
      map['country_code'] = Variable<String>(countryCode.value);
    }
    if (countryName.present) {
      map['country_name'] = Variable<String>(countryName.value);
    }
    if (autocompleteData.present) {
      map['autocomplete_data'] = Variable<String>(autocompleteData.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('TripsCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('startDate: $startDate, ')
          ..write('endDate: $endDate, ')
          ..write('city: $city, ')
          ..write('countryCode: $countryCode, ')
          ..write('countryName: $countryName, ')
          ..write('autocompleteData: $autocompleteData, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $ItinerariesTable extends Itineraries
    with TableInfo<$ItinerariesTable, ItineraryItem> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $ItinerariesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 40),
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      clientDefault: () => Uuid().v4());
  static const VerificationMeta _tripIdMeta = const VerificationMeta('tripId');
  @override
  late final GeneratedColumn<String> tripId = GeneratedColumn<String>(
      'trip_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'REFERENCES trips(id) NOT NULL');
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 255),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _amountMeta = const VerificationMeta('amount');
  @override
  late final GeneratedColumn<double> amount = GeneratedColumn<double>(
      'amount', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
      'date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _categoryMeta =
      const VerificationMeta('category');
  @override
  late final GeneratedColumn<String> category = GeneratedColumn<String>(
      'category', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _locationMeta =
      const VerificationMeta('location');
  @override
  late final GeneratedColumn<String> location = GeneratedColumn<String>(
      'location', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _locationTextMeta =
      const VerificationMeta('locationText');
  @override
  late final GeneratedColumn<String> locationText = GeneratedColumn<String>(
      'location_text', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _airlineNameMeta =
      const VerificationMeta('airlineName');
  @override
  late final GeneratedColumn<String> airlineName = GeneratedColumn<String>(
      'airline_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _flightNumberMeta =
      const VerificationMeta('flightNumber');
  @override
  late final GeneratedColumn<String> flightNumber = GeneratedColumn<String>(
      'flight_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _checkoutDateMeta =
      const VerificationMeta('checkoutDate');
  @override
  late final GeneratedColumn<DateTime> checkoutDate = GeneratedColumn<DateTime>(
      'checkout_date', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _rankMeta = const VerificationMeta('rank');
  @override
  late final GeneratedColumn<int> rank = GeneratedColumn<int>(
      'rank', aliasedName, true,
      type: DriftSqlType.int, requiredDuringInsert: false);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        tripId,
        title,
        amount,
        date,
        category,
        location,
        locationText,
        airlineName,
        flightNumber,
        checkoutDate,
        description,
        rank
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'itineraries';
  @override
  VerificationContext validateIntegrity(Insertable<ItineraryItem> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('trip_id')) {
      context.handle(_tripIdMeta,
          tripId.isAcceptableOrUnknown(data['trip_id']!, _tripIdMeta));
    } else if (isInserting) {
      context.missing(_tripIdMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('amount')) {
      context.handle(_amountMeta,
          amount.isAcceptableOrUnknown(data['amount']!, _amountMeta));
    }
    if (data.containsKey('date')) {
      context.handle(
          _dateMeta, date.isAcceptableOrUnknown(data['date']!, _dateMeta));
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('category')) {
      context.handle(_categoryMeta,
          category.isAcceptableOrUnknown(data['category']!, _categoryMeta));
    } else if (isInserting) {
      context.missing(_categoryMeta);
    }
    if (data.containsKey('location')) {
      context.handle(_locationMeta,
          location.isAcceptableOrUnknown(data['location']!, _locationMeta));
    }
    if (data.containsKey('location_text')) {
      context.handle(
          _locationTextMeta,
          locationText.isAcceptableOrUnknown(
              data['location_text']!, _locationTextMeta));
    }
    if (data.containsKey('airline_name')) {
      context.handle(
          _airlineNameMeta,
          airlineName.isAcceptableOrUnknown(
              data['airline_name']!, _airlineNameMeta));
    }
    if (data.containsKey('flight_number')) {
      context.handle(
          _flightNumberMeta,
          flightNumber.isAcceptableOrUnknown(
              data['flight_number']!, _flightNumberMeta));
    }
    if (data.containsKey('checkout_date')) {
      context.handle(
          _checkoutDateMeta,
          checkoutDate.isAcceptableOrUnknown(
              data['checkout_date']!, _checkoutDateMeta));
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('rank')) {
      context.handle(
          _rankMeta, rank.isAcceptableOrUnknown(data['rank']!, _rankMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  ItineraryItem map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return ItineraryItem(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      tripId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}trip_id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      amount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}amount']),
      date: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}date'])!,
      category: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category'])!,
      location: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}location']),
      locationText: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}location_text']),
      airlineName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}airline_name']),
      flightNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}flight_number']),
      checkoutDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}checkout_date']),
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      rank: attachedDatabase.typeMapping
          .read(DriftSqlType.int, data['${effectivePrefix}rank']),
    );
  }

  @override
  $ItinerariesTable createAlias(String alias) {
    return $ItinerariesTable(attachedDatabase, alias);
  }
}

class ItineraryItem extends DataClass implements Insertable<ItineraryItem> {
  final String id;
  final String tripId;
  final String title;
  final double? amount;
  final DateTime date;
  final String category;
  final String? location;
  final String? locationText;
  final String? airlineName;
  final String? flightNumber;
  final DateTime? checkoutDate;
  final String? description;
  final int? rank;
  const ItineraryItem(
      {required this.id,
      required this.tripId,
      required this.title,
      this.amount,
      required this.date,
      required this.category,
      this.location,
      this.locationText,
      this.airlineName,
      this.flightNumber,
      this.checkoutDate,
      this.description,
      this.rank});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['trip_id'] = Variable<String>(tripId);
    map['title'] = Variable<String>(title);
    if (!nullToAbsent || amount != null) {
      map['amount'] = Variable<double>(amount);
    }
    map['date'] = Variable<DateTime>(date);
    map['category'] = Variable<String>(category);
    if (!nullToAbsent || location != null) {
      map['location'] = Variable<String>(location);
    }
    if (!nullToAbsent || locationText != null) {
      map['location_text'] = Variable<String>(locationText);
    }
    if (!nullToAbsent || airlineName != null) {
      map['airline_name'] = Variable<String>(airlineName);
    }
    if (!nullToAbsent || flightNumber != null) {
      map['flight_number'] = Variable<String>(flightNumber);
    }
    if (!nullToAbsent || checkoutDate != null) {
      map['checkout_date'] = Variable<DateTime>(checkoutDate);
    }
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    if (!nullToAbsent || rank != null) {
      map['rank'] = Variable<int>(rank);
    }
    return map;
  }

  ItinerariesCompanion toCompanion(bool nullToAbsent) {
    return ItinerariesCompanion(
      id: Value(id),
      tripId: Value(tripId),
      title: Value(title),
      amount:
          amount == null && nullToAbsent ? const Value.absent() : Value(amount),
      date: Value(date),
      category: Value(category),
      location: location == null && nullToAbsent
          ? const Value.absent()
          : Value(location),
      locationText: locationText == null && nullToAbsent
          ? const Value.absent()
          : Value(locationText),
      airlineName: airlineName == null && nullToAbsent
          ? const Value.absent()
          : Value(airlineName),
      flightNumber: flightNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(flightNumber),
      checkoutDate: checkoutDate == null && nullToAbsent
          ? const Value.absent()
          : Value(checkoutDate),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      rank: rank == null && nullToAbsent ? const Value.absent() : Value(rank),
    );
  }

  factory ItineraryItem.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return ItineraryItem(
      id: serializer.fromJson<String>(json['id']),
      tripId: serializer.fromJson<String>(json['tripId']),
      title: serializer.fromJson<String>(json['title']),
      amount: serializer.fromJson<double?>(json['amount']),
      date: serializer.fromJson<DateTime>(json['date']),
      category: serializer.fromJson<String>(json['category']),
      location: serializer.fromJson<String?>(json['location']),
      locationText: serializer.fromJson<String?>(json['locationText']),
      airlineName: serializer.fromJson<String?>(json['airlineName']),
      flightNumber: serializer.fromJson<String?>(json['flightNumber']),
      checkoutDate: serializer.fromJson<DateTime?>(json['checkoutDate']),
      description: serializer.fromJson<String?>(json['description']),
      rank: serializer.fromJson<int?>(json['rank']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'tripId': serializer.toJson<String>(tripId),
      'title': serializer.toJson<String>(title),
      'amount': serializer.toJson<double?>(amount),
      'date': serializer.toJson<DateTime>(date),
      'category': serializer.toJson<String>(category),
      'location': serializer.toJson<String?>(location),
      'locationText': serializer.toJson<String?>(locationText),
      'airlineName': serializer.toJson<String?>(airlineName),
      'flightNumber': serializer.toJson<String?>(flightNumber),
      'checkoutDate': serializer.toJson<DateTime?>(checkoutDate),
      'description': serializer.toJson<String?>(description),
      'rank': serializer.toJson<int?>(rank),
    };
  }

  ItineraryItem copyWith(
          {String? id,
          String? tripId,
          String? title,
          Value<double?> amount = const Value.absent(),
          DateTime? date,
          String? category,
          Value<String?> location = const Value.absent(),
          Value<String?> locationText = const Value.absent(),
          Value<String?> airlineName = const Value.absent(),
          Value<String?> flightNumber = const Value.absent(),
          Value<DateTime?> checkoutDate = const Value.absent(),
          Value<String?> description = const Value.absent(),
          Value<int?> rank = const Value.absent()}) =>
      ItineraryItem(
        id: id ?? this.id,
        tripId: tripId ?? this.tripId,
        title: title ?? this.title,
        amount: amount.present ? amount.value : this.amount,
        date: date ?? this.date,
        category: category ?? this.category,
        location: location.present ? location.value : this.location,
        locationText:
            locationText.present ? locationText.value : this.locationText,
        airlineName: airlineName.present ? airlineName.value : this.airlineName,
        flightNumber:
            flightNumber.present ? flightNumber.value : this.flightNumber,
        checkoutDate:
            checkoutDate.present ? checkoutDate.value : this.checkoutDate,
        description: description.present ? description.value : this.description,
        rank: rank.present ? rank.value : this.rank,
      );
  ItineraryItem copyWithCompanion(ItinerariesCompanion data) {
    return ItineraryItem(
      id: data.id.present ? data.id.value : this.id,
      tripId: data.tripId.present ? data.tripId.value : this.tripId,
      title: data.title.present ? data.title.value : this.title,
      amount: data.amount.present ? data.amount.value : this.amount,
      date: data.date.present ? data.date.value : this.date,
      category: data.category.present ? data.category.value : this.category,
      location: data.location.present ? data.location.value : this.location,
      locationText: data.locationText.present
          ? data.locationText.value
          : this.locationText,
      airlineName:
          data.airlineName.present ? data.airlineName.value : this.airlineName,
      flightNumber: data.flightNumber.present
          ? data.flightNumber.value
          : this.flightNumber,
      checkoutDate: data.checkoutDate.present
          ? data.checkoutDate.value
          : this.checkoutDate,
      description:
          data.description.present ? data.description.value : this.description,
      rank: data.rank.present ? data.rank.value : this.rank,
    );
  }

  @override
  String toString() {
    return (StringBuffer('ItineraryItem(')
          ..write('id: $id, ')
          ..write('tripId: $tripId, ')
          ..write('title: $title, ')
          ..write('amount: $amount, ')
          ..write('date: $date, ')
          ..write('category: $category, ')
          ..write('location: $location, ')
          ..write('locationText: $locationText, ')
          ..write('airlineName: $airlineName, ')
          ..write('flightNumber: $flightNumber, ')
          ..write('checkoutDate: $checkoutDate, ')
          ..write('description: $description, ')
          ..write('rank: $rank')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      tripId,
      title,
      amount,
      date,
      category,
      location,
      locationText,
      airlineName,
      flightNumber,
      checkoutDate,
      description,
      rank);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is ItineraryItem &&
          other.id == this.id &&
          other.tripId == this.tripId &&
          other.title == this.title &&
          other.amount == this.amount &&
          other.date == this.date &&
          other.category == this.category &&
          other.location == this.location &&
          other.locationText == this.locationText &&
          other.airlineName == this.airlineName &&
          other.flightNumber == this.flightNumber &&
          other.checkoutDate == this.checkoutDate &&
          other.description == this.description &&
          other.rank == this.rank);
}

class ItinerariesCompanion extends UpdateCompanion<ItineraryItem> {
  final Value<String> id;
  final Value<String> tripId;
  final Value<String> title;
  final Value<double?> amount;
  final Value<DateTime> date;
  final Value<String> category;
  final Value<String?> location;
  final Value<String?> locationText;
  final Value<String?> airlineName;
  final Value<String?> flightNumber;
  final Value<DateTime?> checkoutDate;
  final Value<String?> description;
  final Value<int?> rank;
  final Value<int> rowid;
  const ItinerariesCompanion({
    this.id = const Value.absent(),
    this.tripId = const Value.absent(),
    this.title = const Value.absent(),
    this.amount = const Value.absent(),
    this.date = const Value.absent(),
    this.category = const Value.absent(),
    this.location = const Value.absent(),
    this.locationText = const Value.absent(),
    this.airlineName = const Value.absent(),
    this.flightNumber = const Value.absent(),
    this.checkoutDate = const Value.absent(),
    this.description = const Value.absent(),
    this.rank = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  ItinerariesCompanion.insert({
    this.id = const Value.absent(),
    required String tripId,
    required String title,
    this.amount = const Value.absent(),
    required DateTime date,
    required String category,
    this.location = const Value.absent(),
    this.locationText = const Value.absent(),
    this.airlineName = const Value.absent(),
    this.flightNumber = const Value.absent(),
    this.checkoutDate = const Value.absent(),
    this.description = const Value.absent(),
    this.rank = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : tripId = Value(tripId),
        title = Value(title),
        date = Value(date),
        category = Value(category);
  static Insertable<ItineraryItem> custom({
    Expression<String>? id,
    Expression<String>? tripId,
    Expression<String>? title,
    Expression<double>? amount,
    Expression<DateTime>? date,
    Expression<String>? category,
    Expression<String>? location,
    Expression<String>? locationText,
    Expression<String>? airlineName,
    Expression<String>? flightNumber,
    Expression<DateTime>? checkoutDate,
    Expression<String>? description,
    Expression<int>? rank,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (tripId != null) 'trip_id': tripId,
      if (title != null) 'title': title,
      if (amount != null) 'amount': amount,
      if (date != null) 'date': date,
      if (category != null) 'category': category,
      if (location != null) 'location': location,
      if (locationText != null) 'location_text': locationText,
      if (airlineName != null) 'airline_name': airlineName,
      if (flightNumber != null) 'flight_number': flightNumber,
      if (checkoutDate != null) 'checkout_date': checkoutDate,
      if (description != null) 'description': description,
      if (rank != null) 'rank': rank,
      if (rowid != null) 'rowid': rowid,
    });
  }

  ItinerariesCompanion copyWith(
      {Value<String>? id,
      Value<String>? tripId,
      Value<String>? title,
      Value<double?>? amount,
      Value<DateTime>? date,
      Value<String>? category,
      Value<String?>? location,
      Value<String?>? locationText,
      Value<String?>? airlineName,
      Value<String?>? flightNumber,
      Value<DateTime?>? checkoutDate,
      Value<String?>? description,
      Value<int?>? rank,
      Value<int>? rowid}) {
    return ItinerariesCompanion(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      location: location ?? this.location,
      locationText: locationText ?? this.locationText,
      airlineName: airlineName ?? this.airlineName,
      flightNumber: flightNumber ?? this.flightNumber,
      checkoutDate: checkoutDate ?? this.checkoutDate,
      description: description ?? this.description,
      rank: rank ?? this.rank,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (tripId.present) {
      map['trip_id'] = Variable<String>(tripId.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (amount.present) {
      map['amount'] = Variable<double>(amount.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (category.present) {
      map['category'] = Variable<String>(category.value);
    }
    if (location.present) {
      map['location'] = Variable<String>(location.value);
    }
    if (locationText.present) {
      map['location_text'] = Variable<String>(locationText.value);
    }
    if (airlineName.present) {
      map['airline_name'] = Variable<String>(airlineName.value);
    }
    if (flightNumber.present) {
      map['flight_number'] = Variable<String>(flightNumber.value);
    }
    if (checkoutDate.present) {
      map['checkout_date'] = Variable<DateTime>(checkoutDate.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (rank.present) {
      map['rank'] = Variable<int>(rank.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('ItinerariesCompanion(')
          ..write('id: $id, ')
          ..write('tripId: $tripId, ')
          ..write('title: $title, ')
          ..write('amount: $amount, ')
          ..write('date: $date, ')
          ..write('category: $category, ')
          ..write('location: $location, ')
          ..write('locationText: $locationText, ')
          ..write('airlineName: $airlineName, ')
          ..write('flightNumber: $flightNumber, ')
          ..write('checkoutDate: $checkoutDate, ')
          ..write('description: $description, ')
          ..write('rank: $rank, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $FavoriteItinerariesTable extends FavoriteItineraries
    with TableInfo<$FavoriteItinerariesTable, FavoriteItinerary> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $FavoriteItinerariesTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 40),
      type: DriftSqlType.string,
      requiredDuringInsert: true);
  static const VerificationMeta _titleMeta = const VerificationMeta('title');
  @override
  late final GeneratedColumn<String> title = GeneratedColumn<String>(
      'title', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _amountMeta = const VerificationMeta('amount');
  @override
  late final GeneratedColumn<double> amount = GeneratedColumn<double>(
      'amount', aliasedName, true,
      type: DriftSqlType.double, requiredDuringInsert: false);
  static const VerificationMeta _dateMeta = const VerificationMeta('date');
  @override
  late final GeneratedColumn<DateTime> date = GeneratedColumn<DateTime>(
      'date', aliasedName, false,
      type: DriftSqlType.dateTime, requiredDuringInsert: true);
  static const VerificationMeta _categoryMeta =
      const VerificationMeta('category');
  @override
  late final GeneratedColumn<String> category = GeneratedColumn<String>(
      'category', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _locationMeta =
      const VerificationMeta('location');
  @override
  late final GeneratedColumn<String> location = GeneratedColumn<String>(
      'location', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _locationTextMeta =
      const VerificationMeta('locationText');
  @override
  late final GeneratedColumn<String> locationText = GeneratedColumn<String>(
      'location_text', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _airlineNameMeta =
      const VerificationMeta('airlineName');
  @override
  late final GeneratedColumn<String> airlineName = GeneratedColumn<String>(
      'airline_name', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _flightNumberMeta =
      const VerificationMeta('flightNumber');
  @override
  late final GeneratedColumn<String> flightNumber = GeneratedColumn<String>(
      'flight_number', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _checkoutDateMeta =
      const VerificationMeta('checkoutDate');
  @override
  late final GeneratedColumn<DateTime> checkoutDate = GeneratedColumn<DateTime>(
      'checkout_date', aliasedName, true,
      type: DriftSqlType.dateTime, requiredDuringInsert: false);
  static const VerificationMeta _descriptionMeta =
      const VerificationMeta('description');
  @override
  late final GeneratedColumn<String> description = GeneratedColumn<String>(
      'description', aliasedName, true,
      type: DriftSqlType.string, requiredDuringInsert: false);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns => [
        id,
        title,
        amount,
        date,
        category,
        location,
        locationText,
        airlineName,
        flightNumber,
        checkoutDate,
        description,
        createdAt
      ];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'favorite_itineraries';
  @override
  VerificationContext validateIntegrity(Insertable<FavoriteItinerary> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    } else if (isInserting) {
      context.missing(_idMeta);
    }
    if (data.containsKey('title')) {
      context.handle(
          _titleMeta, title.isAcceptableOrUnknown(data['title']!, _titleMeta));
    } else if (isInserting) {
      context.missing(_titleMeta);
    }
    if (data.containsKey('amount')) {
      context.handle(_amountMeta,
          amount.isAcceptableOrUnknown(data['amount']!, _amountMeta));
    }
    if (data.containsKey('date')) {
      context.handle(
          _dateMeta, date.isAcceptableOrUnknown(data['date']!, _dateMeta));
    } else if (isInserting) {
      context.missing(_dateMeta);
    }
    if (data.containsKey('category')) {
      context.handle(_categoryMeta,
          category.isAcceptableOrUnknown(data['category']!, _categoryMeta));
    } else if (isInserting) {
      context.missing(_categoryMeta);
    }
    if (data.containsKey('location')) {
      context.handle(_locationMeta,
          location.isAcceptableOrUnknown(data['location']!, _locationMeta));
    }
    if (data.containsKey('location_text')) {
      context.handle(
          _locationTextMeta,
          locationText.isAcceptableOrUnknown(
              data['location_text']!, _locationTextMeta));
    }
    if (data.containsKey('airline_name')) {
      context.handle(
          _airlineNameMeta,
          airlineName.isAcceptableOrUnknown(
              data['airline_name']!, _airlineNameMeta));
    }
    if (data.containsKey('flight_number')) {
      context.handle(
          _flightNumberMeta,
          flightNumber.isAcceptableOrUnknown(
              data['flight_number']!, _flightNumberMeta));
    }
    if (data.containsKey('checkout_date')) {
      context.handle(
          _checkoutDateMeta,
          checkoutDate.isAcceptableOrUnknown(
              data['checkout_date']!, _checkoutDateMeta));
    }
    if (data.containsKey('description')) {
      context.handle(
          _descriptionMeta,
          description.isAcceptableOrUnknown(
              data['description']!, _descriptionMeta));
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  FavoriteItinerary map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return FavoriteItinerary(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      title: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}title'])!,
      amount: attachedDatabase.typeMapping
          .read(DriftSqlType.double, data['${effectivePrefix}amount']),
      date: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}date'])!,
      category: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}category'])!,
      location: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}location']),
      locationText: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}location_text']),
      airlineName: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}airline_name']),
      flightNumber: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}flight_number']),
      checkoutDate: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}checkout_date']),
      description: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}description']),
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
    );
  }

  @override
  $FavoriteItinerariesTable createAlias(String alias) {
    return $FavoriteItinerariesTable(attachedDatabase, alias);
  }
}

class FavoriteItinerary extends DataClass
    implements Insertable<FavoriteItinerary> {
  final String id;
  final String title;
  final double? amount;
  final DateTime date;
  final String category;
  final String? location;
  final String? locationText;
  final String? airlineName;
  final String? flightNumber;
  final DateTime? checkoutDate;
  final String? description;
  final DateTime createdAt;
  const FavoriteItinerary(
      {required this.id,
      required this.title,
      this.amount,
      required this.date,
      required this.category,
      this.location,
      this.locationText,
      this.airlineName,
      this.flightNumber,
      this.checkoutDate,
      this.description,
      required this.createdAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['title'] = Variable<String>(title);
    if (!nullToAbsent || amount != null) {
      map['amount'] = Variable<double>(amount);
    }
    map['date'] = Variable<DateTime>(date);
    map['category'] = Variable<String>(category);
    if (!nullToAbsent || location != null) {
      map['location'] = Variable<String>(location);
    }
    if (!nullToAbsent || locationText != null) {
      map['location_text'] = Variable<String>(locationText);
    }
    if (!nullToAbsent || airlineName != null) {
      map['airline_name'] = Variable<String>(airlineName);
    }
    if (!nullToAbsent || flightNumber != null) {
      map['flight_number'] = Variable<String>(flightNumber);
    }
    if (!nullToAbsent || checkoutDate != null) {
      map['checkout_date'] = Variable<DateTime>(checkoutDate);
    }
    if (!nullToAbsent || description != null) {
      map['description'] = Variable<String>(description);
    }
    map['created_at'] = Variable<DateTime>(createdAt);
    return map;
  }

  FavoriteItinerariesCompanion toCompanion(bool nullToAbsent) {
    return FavoriteItinerariesCompanion(
      id: Value(id),
      title: Value(title),
      amount:
          amount == null && nullToAbsent ? const Value.absent() : Value(amount),
      date: Value(date),
      category: Value(category),
      location: location == null && nullToAbsent
          ? const Value.absent()
          : Value(location),
      locationText: locationText == null && nullToAbsent
          ? const Value.absent()
          : Value(locationText),
      airlineName: airlineName == null && nullToAbsent
          ? const Value.absent()
          : Value(airlineName),
      flightNumber: flightNumber == null && nullToAbsent
          ? const Value.absent()
          : Value(flightNumber),
      checkoutDate: checkoutDate == null && nullToAbsent
          ? const Value.absent()
          : Value(checkoutDate),
      description: description == null && nullToAbsent
          ? const Value.absent()
          : Value(description),
      createdAt: Value(createdAt),
    );
  }

  factory FavoriteItinerary.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return FavoriteItinerary(
      id: serializer.fromJson<String>(json['id']),
      title: serializer.fromJson<String>(json['title']),
      amount: serializer.fromJson<double?>(json['amount']),
      date: serializer.fromJson<DateTime>(json['date']),
      category: serializer.fromJson<String>(json['category']),
      location: serializer.fromJson<String?>(json['location']),
      locationText: serializer.fromJson<String?>(json['locationText']),
      airlineName: serializer.fromJson<String?>(json['airlineName']),
      flightNumber: serializer.fromJson<String?>(json['flightNumber']),
      checkoutDate: serializer.fromJson<DateTime?>(json['checkoutDate']),
      description: serializer.fromJson<String?>(json['description']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'title': serializer.toJson<String>(title),
      'amount': serializer.toJson<double?>(amount),
      'date': serializer.toJson<DateTime>(date),
      'category': serializer.toJson<String>(category),
      'location': serializer.toJson<String?>(location),
      'locationText': serializer.toJson<String?>(locationText),
      'airlineName': serializer.toJson<String?>(airlineName),
      'flightNumber': serializer.toJson<String?>(flightNumber),
      'checkoutDate': serializer.toJson<DateTime?>(checkoutDate),
      'description': serializer.toJson<String?>(description),
      'createdAt': serializer.toJson<DateTime>(createdAt),
    };
  }

  FavoriteItinerary copyWith(
          {String? id,
          String? title,
          Value<double?> amount = const Value.absent(),
          DateTime? date,
          String? category,
          Value<String?> location = const Value.absent(),
          Value<String?> locationText = const Value.absent(),
          Value<String?> airlineName = const Value.absent(),
          Value<String?> flightNumber = const Value.absent(),
          Value<DateTime?> checkoutDate = const Value.absent(),
          Value<String?> description = const Value.absent(),
          DateTime? createdAt}) =>
      FavoriteItinerary(
        id: id ?? this.id,
        title: title ?? this.title,
        amount: amount.present ? amount.value : this.amount,
        date: date ?? this.date,
        category: category ?? this.category,
        location: location.present ? location.value : this.location,
        locationText:
            locationText.present ? locationText.value : this.locationText,
        airlineName: airlineName.present ? airlineName.value : this.airlineName,
        flightNumber:
            flightNumber.present ? flightNumber.value : this.flightNumber,
        checkoutDate:
            checkoutDate.present ? checkoutDate.value : this.checkoutDate,
        description: description.present ? description.value : this.description,
        createdAt: createdAt ?? this.createdAt,
      );
  FavoriteItinerary copyWithCompanion(FavoriteItinerariesCompanion data) {
    return FavoriteItinerary(
      id: data.id.present ? data.id.value : this.id,
      title: data.title.present ? data.title.value : this.title,
      amount: data.amount.present ? data.amount.value : this.amount,
      date: data.date.present ? data.date.value : this.date,
      category: data.category.present ? data.category.value : this.category,
      location: data.location.present ? data.location.value : this.location,
      locationText: data.locationText.present
          ? data.locationText.value
          : this.locationText,
      airlineName:
          data.airlineName.present ? data.airlineName.value : this.airlineName,
      flightNumber: data.flightNumber.present
          ? data.flightNumber.value
          : this.flightNumber,
      checkoutDate: data.checkoutDate.present
          ? data.checkoutDate.value
          : this.checkoutDate,
      description:
          data.description.present ? data.description.value : this.description,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('FavoriteItinerary(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('amount: $amount, ')
          ..write('date: $date, ')
          ..write('category: $category, ')
          ..write('location: $location, ')
          ..write('locationText: $locationText, ')
          ..write('airlineName: $airlineName, ')
          ..write('flightNumber: $flightNumber, ')
          ..write('checkoutDate: $checkoutDate, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode => Object.hash(
      id,
      title,
      amount,
      date,
      category,
      location,
      locationText,
      airlineName,
      flightNumber,
      checkoutDate,
      description,
      createdAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is FavoriteItinerary &&
          other.id == this.id &&
          other.title == this.title &&
          other.amount == this.amount &&
          other.date == this.date &&
          other.category == this.category &&
          other.location == this.location &&
          other.locationText == this.locationText &&
          other.airlineName == this.airlineName &&
          other.flightNumber == this.flightNumber &&
          other.checkoutDate == this.checkoutDate &&
          other.description == this.description &&
          other.createdAt == this.createdAt);
}

class FavoriteItinerariesCompanion extends UpdateCompanion<FavoriteItinerary> {
  final Value<String> id;
  final Value<String> title;
  final Value<double?> amount;
  final Value<DateTime> date;
  final Value<String> category;
  final Value<String?> location;
  final Value<String?> locationText;
  final Value<String?> airlineName;
  final Value<String?> flightNumber;
  final Value<DateTime?> checkoutDate;
  final Value<String?> description;
  final Value<DateTime> createdAt;
  final Value<int> rowid;
  const FavoriteItinerariesCompanion({
    this.id = const Value.absent(),
    this.title = const Value.absent(),
    this.amount = const Value.absent(),
    this.date = const Value.absent(),
    this.category = const Value.absent(),
    this.location = const Value.absent(),
    this.locationText = const Value.absent(),
    this.airlineName = const Value.absent(),
    this.flightNumber = const Value.absent(),
    this.checkoutDate = const Value.absent(),
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  FavoriteItinerariesCompanion.insert({
    required String id,
    required String title,
    this.amount = const Value.absent(),
    required DateTime date,
    required String category,
    this.location = const Value.absent(),
    this.locationText = const Value.absent(),
    this.airlineName = const Value.absent(),
    this.flightNumber = const Value.absent(),
    this.checkoutDate = const Value.absent(),
    this.description = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : id = Value(id),
        title = Value(title),
        date = Value(date),
        category = Value(category);
  static Insertable<FavoriteItinerary> custom({
    Expression<String>? id,
    Expression<String>? title,
    Expression<double>? amount,
    Expression<DateTime>? date,
    Expression<String>? category,
    Expression<String>? location,
    Expression<String>? locationText,
    Expression<String>? airlineName,
    Expression<String>? flightNumber,
    Expression<DateTime>? checkoutDate,
    Expression<String>? description,
    Expression<DateTime>? createdAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (title != null) 'title': title,
      if (amount != null) 'amount': amount,
      if (date != null) 'date': date,
      if (category != null) 'category': category,
      if (location != null) 'location': location,
      if (locationText != null) 'location_text': locationText,
      if (airlineName != null) 'airline_name': airlineName,
      if (flightNumber != null) 'flight_number': flightNumber,
      if (checkoutDate != null) 'checkout_date': checkoutDate,
      if (description != null) 'description': description,
      if (createdAt != null) 'created_at': createdAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  FavoriteItinerariesCompanion copyWith(
      {Value<String>? id,
      Value<String>? title,
      Value<double?>? amount,
      Value<DateTime>? date,
      Value<String>? category,
      Value<String?>? location,
      Value<String?>? locationText,
      Value<String?>? airlineName,
      Value<String?>? flightNumber,
      Value<DateTime?>? checkoutDate,
      Value<String?>? description,
      Value<DateTime>? createdAt,
      Value<int>? rowid}) {
    return FavoriteItinerariesCompanion(
      id: id ?? this.id,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      location: location ?? this.location,
      locationText: locationText ?? this.locationText,
      airlineName: airlineName ?? this.airlineName,
      flightNumber: flightNumber ?? this.flightNumber,
      checkoutDate: checkoutDate ?? this.checkoutDate,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (title.present) {
      map['title'] = Variable<String>(title.value);
    }
    if (amount.present) {
      map['amount'] = Variable<double>(amount.value);
    }
    if (date.present) {
      map['date'] = Variable<DateTime>(date.value);
    }
    if (category.present) {
      map['category'] = Variable<String>(category.value);
    }
    if (location.present) {
      map['location'] = Variable<String>(location.value);
    }
    if (locationText.present) {
      map['location_text'] = Variable<String>(locationText.value);
    }
    if (airlineName.present) {
      map['airline_name'] = Variable<String>(airlineName.value);
    }
    if (flightNumber.present) {
      map['flight_number'] = Variable<String>(flightNumber.value);
    }
    if (checkoutDate.present) {
      map['checkout_date'] = Variable<DateTime>(checkoutDate.value);
    }
    if (description.present) {
      map['description'] = Variable<String>(description.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('FavoriteItinerariesCompanion(')
          ..write('id: $id, ')
          ..write('title: $title, ')
          ..write('amount: $amount, ')
          ..write('date: $date, ')
          ..write('category: $category, ')
          ..write('location: $location, ')
          ..write('locationText: $locationText, ')
          ..write('airlineName: $airlineName, ')
          ..write('flightNumber: $flightNumber, ')
          ..write('checkoutDate: $checkoutDate, ')
          ..write('description: $description, ')
          ..write('createdAt: $createdAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

class $AttachmentsTable extends Attachments
    with TableInfo<$AttachmentsTable, AttachmentItem> {
  @override
  final GeneratedDatabase attachedDatabase;
  final String? _alias;
  $AttachmentsTable(this.attachedDatabase, [this._alias]);
  static const VerificationMeta _idMeta = const VerificationMeta('id');
  @override
  late final GeneratedColumn<String> id = GeneratedColumn<String>(
      'id', aliasedName, false,
      additionalChecks:
          GeneratedColumn.checkTextLength(minTextLength: 1, maxTextLength: 40),
      type: DriftSqlType.string,
      requiredDuringInsert: false,
      clientDefault: () => Uuid().v4());
  static const VerificationMeta _itineraryIdMeta =
      const VerificationMeta('itineraryId');
  @override
  late final GeneratedColumn<String> itineraryId = GeneratedColumn<String>(
      'itinerary_id', aliasedName, false,
      type: DriftSqlType.string,
      requiredDuringInsert: true,
      $customConstraints: 'REFERENCES itineraries(id) NOT NULL');
  static const VerificationMeta _typeMeta = const VerificationMeta('type');
  @override
  late final GeneratedColumn<String> type = GeneratedColumn<String>(
      'type', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _filePathMeta =
      const VerificationMeta('filePath');
  @override
  late final GeneratedColumn<String> filePath = GeneratedColumn<String>(
      'file_path', aliasedName, false,
      type: DriftSqlType.string, requiredDuringInsert: true);
  static const VerificationMeta _createdAtMeta =
      const VerificationMeta('createdAt');
  @override
  late final GeneratedColumn<DateTime> createdAt = GeneratedColumn<DateTime>(
      'created_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  static const VerificationMeta _updatedAtMeta =
      const VerificationMeta('updatedAt');
  @override
  late final GeneratedColumn<DateTime> updatedAt = GeneratedColumn<DateTime>(
      'updated_at', aliasedName, false,
      type: DriftSqlType.dateTime,
      requiredDuringInsert: false,
      defaultValue: currentDateAndTime);
  @override
  List<GeneratedColumn> get $columns =>
      [id, itineraryId, type, filePath, createdAt, updatedAt];
  @override
  String get aliasedName => _alias ?? actualTableName;
  @override
  String get actualTableName => $name;
  static const String $name = 'attachments';
  @override
  VerificationContext validateIntegrity(Insertable<AttachmentItem> instance,
      {bool isInserting = false}) {
    final context = VerificationContext();
    final data = instance.toColumns(true);
    if (data.containsKey('id')) {
      context.handle(_idMeta, id.isAcceptableOrUnknown(data['id']!, _idMeta));
    }
    if (data.containsKey('itinerary_id')) {
      context.handle(
          _itineraryIdMeta,
          itineraryId.isAcceptableOrUnknown(
              data['itinerary_id']!, _itineraryIdMeta));
    } else if (isInserting) {
      context.missing(_itineraryIdMeta);
    }
    if (data.containsKey('type')) {
      context.handle(
          _typeMeta, type.isAcceptableOrUnknown(data['type']!, _typeMeta));
    } else if (isInserting) {
      context.missing(_typeMeta);
    }
    if (data.containsKey('file_path')) {
      context.handle(_filePathMeta,
          filePath.isAcceptableOrUnknown(data['file_path']!, _filePathMeta));
    } else if (isInserting) {
      context.missing(_filePathMeta);
    }
    if (data.containsKey('created_at')) {
      context.handle(_createdAtMeta,
          createdAt.isAcceptableOrUnknown(data['created_at']!, _createdAtMeta));
    }
    if (data.containsKey('updated_at')) {
      context.handle(_updatedAtMeta,
          updatedAt.isAcceptableOrUnknown(data['updated_at']!, _updatedAtMeta));
    }
    return context;
  }

  @override
  Set<GeneratedColumn> get $primaryKey => {id};
  @override
  AttachmentItem map(Map<String, dynamic> data, {String? tablePrefix}) {
    final effectivePrefix = tablePrefix != null ? '$tablePrefix.' : '';
    return AttachmentItem(
      id: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}id'])!,
      itineraryId: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}itinerary_id'])!,
      type: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}type'])!,
      filePath: attachedDatabase.typeMapping
          .read(DriftSqlType.string, data['${effectivePrefix}file_path'])!,
      createdAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}created_at'])!,
      updatedAt: attachedDatabase.typeMapping
          .read(DriftSqlType.dateTime, data['${effectivePrefix}updated_at'])!,
    );
  }

  @override
  $AttachmentsTable createAlias(String alias) {
    return $AttachmentsTable(attachedDatabase, alias);
  }
}

class AttachmentItem extends DataClass implements Insertable<AttachmentItem> {
  final String id;
  final String itineraryId;
  final String type;
  final String filePath;
  final DateTime createdAt;
  final DateTime updatedAt;
  const AttachmentItem(
      {required this.id,
      required this.itineraryId,
      required this.type,
      required this.filePath,
      required this.createdAt,
      required this.updatedAt});
  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    map['id'] = Variable<String>(id);
    map['itinerary_id'] = Variable<String>(itineraryId);
    map['type'] = Variable<String>(type);
    map['file_path'] = Variable<String>(filePath);
    map['created_at'] = Variable<DateTime>(createdAt);
    map['updated_at'] = Variable<DateTime>(updatedAt);
    return map;
  }

  AttachmentsCompanion toCompanion(bool nullToAbsent) {
    return AttachmentsCompanion(
      id: Value(id),
      itineraryId: Value(itineraryId),
      type: Value(type),
      filePath: Value(filePath),
      createdAt: Value(createdAt),
      updatedAt: Value(updatedAt),
    );
  }

  factory AttachmentItem.fromJson(Map<String, dynamic> json,
      {ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return AttachmentItem(
      id: serializer.fromJson<String>(json['id']),
      itineraryId: serializer.fromJson<String>(json['itineraryId']),
      type: serializer.fromJson<String>(json['type']),
      filePath: serializer.fromJson<String>(json['filePath']),
      createdAt: serializer.fromJson<DateTime>(json['createdAt']),
      updatedAt: serializer.fromJson<DateTime>(json['updatedAt']),
    );
  }
  @override
  Map<String, dynamic> toJson({ValueSerializer? serializer}) {
    serializer ??= driftRuntimeOptions.defaultSerializer;
    return <String, dynamic>{
      'id': serializer.toJson<String>(id),
      'itineraryId': serializer.toJson<String>(itineraryId),
      'type': serializer.toJson<String>(type),
      'filePath': serializer.toJson<String>(filePath),
      'createdAt': serializer.toJson<DateTime>(createdAt),
      'updatedAt': serializer.toJson<DateTime>(updatedAt),
    };
  }

  AttachmentItem copyWith(
          {String? id,
          String? itineraryId,
          String? type,
          String? filePath,
          DateTime? createdAt,
          DateTime? updatedAt}) =>
      AttachmentItem(
        id: id ?? this.id,
        itineraryId: itineraryId ?? this.itineraryId,
        type: type ?? this.type,
        filePath: filePath ?? this.filePath,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
  AttachmentItem copyWithCompanion(AttachmentsCompanion data) {
    return AttachmentItem(
      id: data.id.present ? data.id.value : this.id,
      itineraryId:
          data.itineraryId.present ? data.itineraryId.value : this.itineraryId,
      type: data.type.present ? data.type.value : this.type,
      filePath: data.filePath.present ? data.filePath.value : this.filePath,
      createdAt: data.createdAt.present ? data.createdAt.value : this.createdAt,
      updatedAt: data.updatedAt.present ? data.updatedAt.value : this.updatedAt,
    );
  }

  @override
  String toString() {
    return (StringBuffer('AttachmentItem(')
          ..write('id: $id, ')
          ..write('itineraryId: $itineraryId, ')
          ..write('type: $type, ')
          ..write('filePath: $filePath, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt')
          ..write(')'))
        .toString();
  }

  @override
  int get hashCode =>
      Object.hash(id, itineraryId, type, filePath, createdAt, updatedAt);
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      (other is AttachmentItem &&
          other.id == this.id &&
          other.itineraryId == this.itineraryId &&
          other.type == this.type &&
          other.filePath == this.filePath &&
          other.createdAt == this.createdAt &&
          other.updatedAt == this.updatedAt);
}

class AttachmentsCompanion extends UpdateCompanion<AttachmentItem> {
  final Value<String> id;
  final Value<String> itineraryId;
  final Value<String> type;
  final Value<String> filePath;
  final Value<DateTime> createdAt;
  final Value<DateTime> updatedAt;
  final Value<int> rowid;
  const AttachmentsCompanion({
    this.id = const Value.absent(),
    this.itineraryId = const Value.absent(),
    this.type = const Value.absent(),
    this.filePath = const Value.absent(),
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  });
  AttachmentsCompanion.insert({
    this.id = const Value.absent(),
    required String itineraryId,
    required String type,
    required String filePath,
    this.createdAt = const Value.absent(),
    this.updatedAt = const Value.absent(),
    this.rowid = const Value.absent(),
  })  : itineraryId = Value(itineraryId),
        type = Value(type),
        filePath = Value(filePath);
  static Insertable<AttachmentItem> custom({
    Expression<String>? id,
    Expression<String>? itineraryId,
    Expression<String>? type,
    Expression<String>? filePath,
    Expression<DateTime>? createdAt,
    Expression<DateTime>? updatedAt,
    Expression<int>? rowid,
  }) {
    return RawValuesInsertable({
      if (id != null) 'id': id,
      if (itineraryId != null) 'itinerary_id': itineraryId,
      if (type != null) 'type': type,
      if (filePath != null) 'file_path': filePath,
      if (createdAt != null) 'created_at': createdAt,
      if (updatedAt != null) 'updated_at': updatedAt,
      if (rowid != null) 'rowid': rowid,
    });
  }

  AttachmentsCompanion copyWith(
      {Value<String>? id,
      Value<String>? itineraryId,
      Value<String>? type,
      Value<String>? filePath,
      Value<DateTime>? createdAt,
      Value<DateTime>? updatedAt,
      Value<int>? rowid}) {
    return AttachmentsCompanion(
      id: id ?? this.id,
      itineraryId: itineraryId ?? this.itineraryId,
      type: type ?? this.type,
      filePath: filePath ?? this.filePath,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      rowid: rowid ?? this.rowid,
    );
  }

  @override
  Map<String, Expression> toColumns(bool nullToAbsent) {
    final map = <String, Expression>{};
    if (id.present) {
      map['id'] = Variable<String>(id.value);
    }
    if (itineraryId.present) {
      map['itinerary_id'] = Variable<String>(itineraryId.value);
    }
    if (type.present) {
      map['type'] = Variable<String>(type.value);
    }
    if (filePath.present) {
      map['file_path'] = Variable<String>(filePath.value);
    }
    if (createdAt.present) {
      map['created_at'] = Variable<DateTime>(createdAt.value);
    }
    if (updatedAt.present) {
      map['updated_at'] = Variable<DateTime>(updatedAt.value);
    }
    if (rowid.present) {
      map['rowid'] = Variable<int>(rowid.value);
    }
    return map;
  }

  @override
  String toString() {
    return (StringBuffer('AttachmentsCompanion(')
          ..write('id: $id, ')
          ..write('itineraryId: $itineraryId, ')
          ..write('type: $type, ')
          ..write('filePath: $filePath, ')
          ..write('createdAt: $createdAt, ')
          ..write('updatedAt: $updatedAt, ')
          ..write('rowid: $rowid')
          ..write(')'))
        .toString();
  }
}

abstract class _$TripDatabase extends GeneratedDatabase {
  _$TripDatabase(QueryExecutor e) : super(e);
  $TripDatabaseManager get managers => $TripDatabaseManager(this);
  late final $TripsTable trips = $TripsTable(this);
  late final $ItinerariesTable itineraries = $ItinerariesTable(this);
  late final $FavoriteItinerariesTable favoriteItineraries =
      $FavoriteItinerariesTable(this);
  late final $AttachmentsTable attachments = $AttachmentsTable(this);
  @override
  Iterable<TableInfo<Table, Object?>> get allTables =>
      allSchemaEntities.whereType<TableInfo<Table, Object?>>();
  @override
  List<DatabaseSchemaEntity> get allSchemaEntities =>
      [trips, itineraries, favoriteItineraries, attachments];
}

typedef $$TripsTableCreateCompanionBuilder = TripsCompanion Function({
  Value<String> id,
  required String title,
  required DateTime startDate,
  required DateTime endDate,
  Value<String?> city,
  Value<String?> countryCode,
  Value<String?> countryName,
  Value<String?> autocompleteData,
  Value<int> rowid,
});
typedef $$TripsTableUpdateCompanionBuilder = TripsCompanion Function({
  Value<String> id,
  Value<String> title,
  Value<DateTime> startDate,
  Value<DateTime> endDate,
  Value<String?> city,
  Value<String?> countryCode,
  Value<String?> countryName,
  Value<String?> autocompleteData,
  Value<int> rowid,
});

final class $$TripsTableReferences
    extends BaseReferences<_$TripDatabase, $TripsTable, TripItem> {
  $$TripsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static MultiTypedResultKey<$ItinerariesTable, List<ItineraryItem>>
      _itinerariesRefsTable(_$TripDatabase db) => MultiTypedResultKey.fromTable(
          db.itineraries,
          aliasName: $_aliasNameGenerator(db.trips.id, db.itineraries.tripId));

  $$ItinerariesTableProcessedTableManager get itinerariesRefs {
    final manager = $$ItinerariesTableTableManager($_db, $_db.itineraries)
        .filter((f) => f.tripId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_itinerariesRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$TripsTableFilterComposer extends Composer<_$TripDatabase, $TripsTable> {
  $$TripsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get city => $composableBuilder(
      column: $table.city, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get countryCode => $composableBuilder(
      column: $table.countryCode, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get countryName => $composableBuilder(
      column: $table.countryName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get autocompleteData => $composableBuilder(
      column: $table.autocompleteData,
      builder: (column) => ColumnFilters(column));

  Expression<bool> itinerariesRefs(
      Expression<bool> Function($$ItinerariesTableFilterComposer f) f) {
    final $$ItinerariesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.itineraries,
        getReferencedColumn: (t) => t.tripId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ItinerariesTableFilterComposer(
              $db: $db,
              $table: $db.itineraries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$TripsTableOrderingComposer
    extends Composer<_$TripDatabase, $TripsTable> {
  $$TripsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get startDate => $composableBuilder(
      column: $table.startDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get endDate => $composableBuilder(
      column: $table.endDate, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get city => $composableBuilder(
      column: $table.city, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get countryCode => $composableBuilder(
      column: $table.countryCode, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get countryName => $composableBuilder(
      column: $table.countryName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get autocompleteData => $composableBuilder(
      column: $table.autocompleteData,
      builder: (column) => ColumnOrderings(column));
}

class $$TripsTableAnnotationComposer
    extends Composer<_$TripDatabase, $TripsTable> {
  $$TripsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<DateTime> get startDate =>
      $composableBuilder(column: $table.startDate, builder: (column) => column);

  GeneratedColumn<DateTime> get endDate =>
      $composableBuilder(column: $table.endDate, builder: (column) => column);

  GeneratedColumn<String> get city =>
      $composableBuilder(column: $table.city, builder: (column) => column);

  GeneratedColumn<String> get countryCode => $composableBuilder(
      column: $table.countryCode, builder: (column) => column);

  GeneratedColumn<String> get countryName => $composableBuilder(
      column: $table.countryName, builder: (column) => column);

  GeneratedColumn<String> get autocompleteData => $composableBuilder(
      column: $table.autocompleteData, builder: (column) => column);

  Expression<T> itinerariesRefs<T extends Object>(
      Expression<T> Function($$ItinerariesTableAnnotationComposer a) f) {
    final $$ItinerariesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.itineraries,
        getReferencedColumn: (t) => t.tripId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ItinerariesTableAnnotationComposer(
              $db: $db,
              $table: $db.itineraries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$TripsTableTableManager extends RootTableManager<
    _$TripDatabase,
    $TripsTable,
    TripItem,
    $$TripsTableFilterComposer,
    $$TripsTableOrderingComposer,
    $$TripsTableAnnotationComposer,
    $$TripsTableCreateCompanionBuilder,
    $$TripsTableUpdateCompanionBuilder,
    (TripItem, $$TripsTableReferences),
    TripItem,
    PrefetchHooks Function({bool itinerariesRefs})> {
  $$TripsTableTableManager(_$TripDatabase db, $TripsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$TripsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$TripsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$TripsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<DateTime> startDate = const Value.absent(),
            Value<DateTime> endDate = const Value.absent(),
            Value<String?> city = const Value.absent(),
            Value<String?> countryCode = const Value.absent(),
            Value<String?> countryName = const Value.absent(),
            Value<String?> autocompleteData = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TripsCompanion(
            id: id,
            title: title,
            startDate: startDate,
            endDate: endDate,
            city: city,
            countryCode: countryCode,
            countryName: countryName,
            autocompleteData: autocompleteData,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            Value<String> id = const Value.absent(),
            required String title,
            required DateTime startDate,
            required DateTime endDate,
            Value<String?> city = const Value.absent(),
            Value<String?> countryCode = const Value.absent(),
            Value<String?> countryName = const Value.absent(),
            Value<String?> autocompleteData = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              TripsCompanion.insert(
            id: id,
            title: title,
            startDate: startDate,
            endDate: endDate,
            city: city,
            countryCode: countryCode,
            countryName: countryName,
            autocompleteData: autocompleteData,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) =>
                  (e.readTable(table), $$TripsTableReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: ({itinerariesRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (itinerariesRefs) db.itineraries],
              addJoins: null,
              getPrefetchedDataCallback: (items) async {
                return [
                  if (itinerariesRefs)
                    await $_getPrefetchedData<TripItem, $TripsTable,
                            ItineraryItem>(
                        currentTable: table,
                        referencedTable:
                            $$TripsTableReferences._itinerariesRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$TripsTableReferences(db, table, p0)
                                .itinerariesRefs,
                        referencedItemsForCurrentItem: (item,
                                referencedItems) =>
                            referencedItems.where((e) => e.tripId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$TripsTableProcessedTableManager = ProcessedTableManager<
    _$TripDatabase,
    $TripsTable,
    TripItem,
    $$TripsTableFilterComposer,
    $$TripsTableOrderingComposer,
    $$TripsTableAnnotationComposer,
    $$TripsTableCreateCompanionBuilder,
    $$TripsTableUpdateCompanionBuilder,
    (TripItem, $$TripsTableReferences),
    TripItem,
    PrefetchHooks Function({bool itinerariesRefs})>;
typedef $$ItinerariesTableCreateCompanionBuilder = ItinerariesCompanion
    Function({
  Value<String> id,
  required String tripId,
  required String title,
  Value<double?> amount,
  required DateTime date,
  required String category,
  Value<String?> location,
  Value<String?> locationText,
  Value<String?> airlineName,
  Value<String?> flightNumber,
  Value<DateTime?> checkoutDate,
  Value<String?> description,
  Value<int?> rank,
  Value<int> rowid,
});
typedef $$ItinerariesTableUpdateCompanionBuilder = ItinerariesCompanion
    Function({
  Value<String> id,
  Value<String> tripId,
  Value<String> title,
  Value<double?> amount,
  Value<DateTime> date,
  Value<String> category,
  Value<String?> location,
  Value<String?> locationText,
  Value<String?> airlineName,
  Value<String?> flightNumber,
  Value<DateTime?> checkoutDate,
  Value<String?> description,
  Value<int?> rank,
  Value<int> rowid,
});

final class $$ItinerariesTableReferences
    extends BaseReferences<_$TripDatabase, $ItinerariesTable, ItineraryItem> {
  $$ItinerariesTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $TripsTable _tripIdTable(_$TripDatabase db) => db.trips
      .createAlias($_aliasNameGenerator(db.itineraries.tripId, db.trips.id));

  $$TripsTableProcessedTableManager get tripId {
    final $_column = $_itemColumn<String>('trip_id')!;

    final manager = $$TripsTableTableManager($_db, $_db.trips)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_tripIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }

  static MultiTypedResultKey<$AttachmentsTable, List<AttachmentItem>>
      _attachmentsRefsTable(_$TripDatabase db) =>
          MultiTypedResultKey.fromTable(db.attachments,
              aliasName: $_aliasNameGenerator(
                  db.itineraries.id, db.attachments.itineraryId));

  $$AttachmentsTableProcessedTableManager get attachmentsRefs {
    final manager = $$AttachmentsTableTableManager($_db, $_db.attachments)
        .filter((f) => f.itineraryId.id.sqlEquals($_itemColumn<String>('id')!));

    final cache = $_typedResult.readTableOrNull(_attachmentsRefsTable($_db));
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: cache));
  }
}

class $$ItinerariesTableFilterComposer
    extends Composer<_$TripDatabase, $ItinerariesTable> {
  $$ItinerariesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get location => $composableBuilder(
      column: $table.location, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get locationText => $composableBuilder(
      column: $table.locationText, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<int> get rank => $composableBuilder(
      column: $table.rank, builder: (column) => ColumnFilters(column));

  $$TripsTableFilterComposer get tripId {
    final $$TripsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.tripId,
        referencedTable: $db.trips,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TripsTableFilterComposer(
              $db: $db,
              $table: $db.trips,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<bool> attachmentsRefs(
      Expression<bool> Function($$AttachmentsTableFilterComposer f) f) {
    final $$AttachmentsTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.attachments,
        getReferencedColumn: (t) => t.itineraryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$AttachmentsTableFilterComposer(
              $db: $db,
              $table: $db.attachments,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$ItinerariesTableOrderingComposer
    extends Composer<_$TripDatabase, $ItinerariesTable> {
  $$ItinerariesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get location => $composableBuilder(
      column: $table.location, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get locationText => $composableBuilder(
      column: $table.locationText,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<int> get rank => $composableBuilder(
      column: $table.rank, builder: (column) => ColumnOrderings(column));

  $$TripsTableOrderingComposer get tripId {
    final $$TripsTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.tripId,
        referencedTable: $db.trips,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TripsTableOrderingComposer(
              $db: $db,
              $table: $db.trips,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$ItinerariesTableAnnotationComposer
    extends Composer<_$TripDatabase, $ItinerariesTable> {
  $$ItinerariesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<double> get amount =>
      $composableBuilder(column: $table.amount, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<String> get category =>
      $composableBuilder(column: $table.category, builder: (column) => column);

  GeneratedColumn<String> get location =>
      $composableBuilder(column: $table.location, builder: (column) => column);

  GeneratedColumn<String> get locationText => $composableBuilder(
      column: $table.locationText, builder: (column) => column);

  GeneratedColumn<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => column);

  GeneratedColumn<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber, builder: (column) => column);

  GeneratedColumn<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<int> get rank =>
      $composableBuilder(column: $table.rank, builder: (column) => column);

  $$TripsTableAnnotationComposer get tripId {
    final $$TripsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.tripId,
        referencedTable: $db.trips,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$TripsTableAnnotationComposer(
              $db: $db,
              $table: $db.trips,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }

  Expression<T> attachmentsRefs<T extends Object>(
      Expression<T> Function($$AttachmentsTableAnnotationComposer a) f) {
    final $$AttachmentsTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.id,
        referencedTable: $db.attachments,
        getReferencedColumn: (t) => t.itineraryId,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$AttachmentsTableAnnotationComposer(
              $db: $db,
              $table: $db.attachments,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return f(composer);
  }
}

class $$ItinerariesTableTableManager extends RootTableManager<
    _$TripDatabase,
    $ItinerariesTable,
    ItineraryItem,
    $$ItinerariesTableFilterComposer,
    $$ItinerariesTableOrderingComposer,
    $$ItinerariesTableAnnotationComposer,
    $$ItinerariesTableCreateCompanionBuilder,
    $$ItinerariesTableUpdateCompanionBuilder,
    (ItineraryItem, $$ItinerariesTableReferences),
    ItineraryItem,
    PrefetchHooks Function({bool tripId, bool attachmentsRefs})> {
  $$ItinerariesTableTableManager(_$TripDatabase db, $ItinerariesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$ItinerariesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$ItinerariesTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$ItinerariesTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> tripId = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<double?> amount = const Value.absent(),
            Value<DateTime> date = const Value.absent(),
            Value<String> category = const Value.absent(),
            Value<String?> location = const Value.absent(),
            Value<String?> locationText = const Value.absent(),
            Value<String?> airlineName = const Value.absent(),
            Value<String?> flightNumber = const Value.absent(),
            Value<DateTime?> checkoutDate = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<int?> rank = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ItinerariesCompanion(
            id: id,
            tripId: tripId,
            title: title,
            amount: amount,
            date: date,
            category: category,
            location: location,
            locationText: locationText,
            airlineName: airlineName,
            flightNumber: flightNumber,
            checkoutDate: checkoutDate,
            description: description,
            rank: rank,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            Value<String> id = const Value.absent(),
            required String tripId,
            required String title,
            Value<double?> amount = const Value.absent(),
            required DateTime date,
            required String category,
            Value<String?> location = const Value.absent(),
            Value<String?> locationText = const Value.absent(),
            Value<String?> airlineName = const Value.absent(),
            Value<String?> flightNumber = const Value.absent(),
            Value<DateTime?> checkoutDate = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<int?> rank = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              ItinerariesCompanion.insert(
            id: id,
            tripId: tripId,
            title: title,
            amount: amount,
            date: date,
            category: category,
            location: location,
            locationText: locationText,
            airlineName: airlineName,
            flightNumber: flightNumber,
            checkoutDate: checkoutDate,
            description: description,
            rank: rank,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$ItinerariesTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({tripId = false, attachmentsRefs = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [if (attachmentsRefs) db.attachments],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (tripId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.tripId,
                    referencedTable:
                        $$ItinerariesTableReferences._tripIdTable(db),
                    referencedColumn:
                        $$ItinerariesTableReferences._tripIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [
                  if (attachmentsRefs)
                    await $_getPrefetchedData<ItineraryItem, $ItinerariesTable, AttachmentItem>(
                        currentTable: table,
                        referencedTable: $$ItinerariesTableReferences
                            ._attachmentsRefsTable(db),
                        managerFromTypedResult: (p0) =>
                            $$ItinerariesTableReferences(db, table, p0)
                                .attachmentsRefs,
                        referencedItemsForCurrentItem:
                            (item, referencedItems) => referencedItems
                                .where((e) => e.itineraryId == item.id),
                        typedResults: items)
                ];
              },
            );
          },
        ));
}

typedef $$ItinerariesTableProcessedTableManager = ProcessedTableManager<
    _$TripDatabase,
    $ItinerariesTable,
    ItineraryItem,
    $$ItinerariesTableFilterComposer,
    $$ItinerariesTableOrderingComposer,
    $$ItinerariesTableAnnotationComposer,
    $$ItinerariesTableCreateCompanionBuilder,
    $$ItinerariesTableUpdateCompanionBuilder,
    (ItineraryItem, $$ItinerariesTableReferences),
    ItineraryItem,
    PrefetchHooks Function({bool tripId, bool attachmentsRefs})>;
typedef $$FavoriteItinerariesTableCreateCompanionBuilder
    = FavoriteItinerariesCompanion Function({
  required String id,
  required String title,
  Value<double?> amount,
  required DateTime date,
  required String category,
  Value<String?> location,
  Value<String?> locationText,
  Value<String?> airlineName,
  Value<String?> flightNumber,
  Value<DateTime?> checkoutDate,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<int> rowid,
});
typedef $$FavoriteItinerariesTableUpdateCompanionBuilder
    = FavoriteItinerariesCompanion Function({
  Value<String> id,
  Value<String> title,
  Value<double?> amount,
  Value<DateTime> date,
  Value<String> category,
  Value<String?> location,
  Value<String?> locationText,
  Value<String?> airlineName,
  Value<String?> flightNumber,
  Value<DateTime?> checkoutDate,
  Value<String?> description,
  Value<DateTime> createdAt,
  Value<int> rowid,
});

class $$FavoriteItinerariesTableFilterComposer
    extends Composer<_$TripDatabase, $FavoriteItinerariesTable> {
  $$FavoriteItinerariesTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnFilters(column));

  ColumnFilters<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get location => $composableBuilder(
      column: $table.location, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get locationText => $composableBuilder(
      column: $table.locationText, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));
}

class $$FavoriteItinerariesTableOrderingComposer
    extends Composer<_$TripDatabase, $FavoriteItinerariesTable> {
  $$FavoriteItinerariesTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get title => $composableBuilder(
      column: $table.title, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<double> get amount => $composableBuilder(
      column: $table.amount, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get date => $composableBuilder(
      column: $table.date, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get category => $composableBuilder(
      column: $table.category, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get location => $composableBuilder(
      column: $table.location, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get locationText => $composableBuilder(
      column: $table.locationText,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate,
      builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));
}

class $$FavoriteItinerariesTableAnnotationComposer
    extends Composer<_$TripDatabase, $FavoriteItinerariesTable> {
  $$FavoriteItinerariesTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get title =>
      $composableBuilder(column: $table.title, builder: (column) => column);

  GeneratedColumn<double> get amount =>
      $composableBuilder(column: $table.amount, builder: (column) => column);

  GeneratedColumn<DateTime> get date =>
      $composableBuilder(column: $table.date, builder: (column) => column);

  GeneratedColumn<String> get category =>
      $composableBuilder(column: $table.category, builder: (column) => column);

  GeneratedColumn<String> get location =>
      $composableBuilder(column: $table.location, builder: (column) => column);

  GeneratedColumn<String> get locationText => $composableBuilder(
      column: $table.locationText, builder: (column) => column);

  GeneratedColumn<String> get airlineName => $composableBuilder(
      column: $table.airlineName, builder: (column) => column);

  GeneratedColumn<String> get flightNumber => $composableBuilder(
      column: $table.flightNumber, builder: (column) => column);

  GeneratedColumn<DateTime> get checkoutDate => $composableBuilder(
      column: $table.checkoutDate, builder: (column) => column);

  GeneratedColumn<String> get description => $composableBuilder(
      column: $table.description, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);
}

class $$FavoriteItinerariesTableTableManager extends RootTableManager<
    _$TripDatabase,
    $FavoriteItinerariesTable,
    FavoriteItinerary,
    $$FavoriteItinerariesTableFilterComposer,
    $$FavoriteItinerariesTableOrderingComposer,
    $$FavoriteItinerariesTableAnnotationComposer,
    $$FavoriteItinerariesTableCreateCompanionBuilder,
    $$FavoriteItinerariesTableUpdateCompanionBuilder,
    (
      FavoriteItinerary,
      BaseReferences<_$TripDatabase, $FavoriteItinerariesTable,
          FavoriteItinerary>
    ),
    FavoriteItinerary,
    PrefetchHooks Function()> {
  $$FavoriteItinerariesTableTableManager(
      _$TripDatabase db, $FavoriteItinerariesTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$FavoriteItinerariesTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$FavoriteItinerariesTableOrderingComposer(
                  $db: db, $table: table),
          createComputedFieldComposer: () =>
              $$FavoriteItinerariesTableAnnotationComposer(
                  $db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> title = const Value.absent(),
            Value<double?> amount = const Value.absent(),
            Value<DateTime> date = const Value.absent(),
            Value<String> category = const Value.absent(),
            Value<String?> location = const Value.absent(),
            Value<String?> locationText = const Value.absent(),
            Value<String?> airlineName = const Value.absent(),
            Value<String?> flightNumber = const Value.absent(),
            Value<DateTime?> checkoutDate = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FavoriteItinerariesCompanion(
            id: id,
            title: title,
            amount: amount,
            date: date,
            category: category,
            location: location,
            locationText: locationText,
            airlineName: airlineName,
            flightNumber: flightNumber,
            checkoutDate: checkoutDate,
            description: description,
            createdAt: createdAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            required String id,
            required String title,
            Value<double?> amount = const Value.absent(),
            required DateTime date,
            required String category,
            Value<String?> location = const Value.absent(),
            Value<String?> locationText = const Value.absent(),
            Value<String?> airlineName = const Value.absent(),
            Value<String?> flightNumber = const Value.absent(),
            Value<DateTime?> checkoutDate = const Value.absent(),
            Value<String?> description = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              FavoriteItinerariesCompanion.insert(
            id: id,
            title: title,
            amount: amount,
            date: date,
            category: category,
            location: location,
            locationText: locationText,
            airlineName: airlineName,
            flightNumber: flightNumber,
            checkoutDate: checkoutDate,
            description: description,
            createdAt: createdAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (e.readTable(table), BaseReferences(db, table, e)))
              .toList(),
          prefetchHooksCallback: null,
        ));
}

typedef $$FavoriteItinerariesTableProcessedTableManager = ProcessedTableManager<
    _$TripDatabase,
    $FavoriteItinerariesTable,
    FavoriteItinerary,
    $$FavoriteItinerariesTableFilterComposer,
    $$FavoriteItinerariesTableOrderingComposer,
    $$FavoriteItinerariesTableAnnotationComposer,
    $$FavoriteItinerariesTableCreateCompanionBuilder,
    $$FavoriteItinerariesTableUpdateCompanionBuilder,
    (
      FavoriteItinerary,
      BaseReferences<_$TripDatabase, $FavoriteItinerariesTable,
          FavoriteItinerary>
    ),
    FavoriteItinerary,
    PrefetchHooks Function()>;
typedef $$AttachmentsTableCreateCompanionBuilder = AttachmentsCompanion
    Function({
  Value<String> id,
  required String itineraryId,
  required String type,
  required String filePath,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});
typedef $$AttachmentsTableUpdateCompanionBuilder = AttachmentsCompanion
    Function({
  Value<String> id,
  Value<String> itineraryId,
  Value<String> type,
  Value<String> filePath,
  Value<DateTime> createdAt,
  Value<DateTime> updatedAt,
  Value<int> rowid,
});

final class $$AttachmentsTableReferences
    extends BaseReferences<_$TripDatabase, $AttachmentsTable, AttachmentItem> {
  $$AttachmentsTableReferences(super.$_db, super.$_table, super.$_typedResult);

  static $ItinerariesTable _itineraryIdTable(_$TripDatabase db) =>
      db.itineraries.createAlias(
          $_aliasNameGenerator(db.attachments.itineraryId, db.itineraries.id));

  $$ItinerariesTableProcessedTableManager get itineraryId {
    final $_column = $_itemColumn<String>('itinerary_id')!;

    final manager = $$ItinerariesTableTableManager($_db, $_db.itineraries)
        .filter((f) => f.id.sqlEquals($_column));
    final item = $_typedResult.readTableOrNull(_itineraryIdTable($_db));
    if (item == null) return manager;
    return ProcessedTableManager(
        manager.$state.copyWith(prefetchedData: [item]));
  }
}

class $$AttachmentsTableFilterComposer
    extends Composer<_$TripDatabase, $AttachmentsTable> {
  $$AttachmentsTableFilterComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnFilters<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnFilters(column));

  ColumnFilters<String> get filePath => $composableBuilder(
      column: $table.filePath, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnFilters(column));

  ColumnFilters<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnFilters(column));

  $$ItinerariesTableFilterComposer get itineraryId {
    final $$ItinerariesTableFilterComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.itineraryId,
        referencedTable: $db.itineraries,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ItinerariesTableFilterComposer(
              $db: $db,
              $table: $db.itineraries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AttachmentsTableOrderingComposer
    extends Composer<_$TripDatabase, $AttachmentsTable> {
  $$AttachmentsTableOrderingComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  ColumnOrderings<String> get id => $composableBuilder(
      column: $table.id, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get type => $composableBuilder(
      column: $table.type, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<String> get filePath => $composableBuilder(
      column: $table.filePath, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get createdAt => $composableBuilder(
      column: $table.createdAt, builder: (column) => ColumnOrderings(column));

  ColumnOrderings<DateTime> get updatedAt => $composableBuilder(
      column: $table.updatedAt, builder: (column) => ColumnOrderings(column));

  $$ItinerariesTableOrderingComposer get itineraryId {
    final $$ItinerariesTableOrderingComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.itineraryId,
        referencedTable: $db.itineraries,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ItinerariesTableOrderingComposer(
              $db: $db,
              $table: $db.itineraries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AttachmentsTableAnnotationComposer
    extends Composer<_$TripDatabase, $AttachmentsTable> {
  $$AttachmentsTableAnnotationComposer({
    required super.$db,
    required super.$table,
    super.joinBuilder,
    super.$addJoinBuilderToRootComposer,
    super.$removeJoinBuilderFromRootComposer,
  });
  GeneratedColumn<String> get id =>
      $composableBuilder(column: $table.id, builder: (column) => column);

  GeneratedColumn<String> get type =>
      $composableBuilder(column: $table.type, builder: (column) => column);

  GeneratedColumn<String> get filePath =>
      $composableBuilder(column: $table.filePath, builder: (column) => column);

  GeneratedColumn<DateTime> get createdAt =>
      $composableBuilder(column: $table.createdAt, builder: (column) => column);

  GeneratedColumn<DateTime> get updatedAt =>
      $composableBuilder(column: $table.updatedAt, builder: (column) => column);

  $$ItinerariesTableAnnotationComposer get itineraryId {
    final $$ItinerariesTableAnnotationComposer composer = $composerBuilder(
        composer: this,
        getCurrentColumn: (t) => t.itineraryId,
        referencedTable: $db.itineraries,
        getReferencedColumn: (t) => t.id,
        builder: (joinBuilder,
                {$addJoinBuilderToRootComposer,
                $removeJoinBuilderFromRootComposer}) =>
            $$ItinerariesTableAnnotationComposer(
              $db: $db,
              $table: $db.itineraries,
              $addJoinBuilderToRootComposer: $addJoinBuilderToRootComposer,
              joinBuilder: joinBuilder,
              $removeJoinBuilderFromRootComposer:
                  $removeJoinBuilderFromRootComposer,
            ));
    return composer;
  }
}

class $$AttachmentsTableTableManager extends RootTableManager<
    _$TripDatabase,
    $AttachmentsTable,
    AttachmentItem,
    $$AttachmentsTableFilterComposer,
    $$AttachmentsTableOrderingComposer,
    $$AttachmentsTableAnnotationComposer,
    $$AttachmentsTableCreateCompanionBuilder,
    $$AttachmentsTableUpdateCompanionBuilder,
    (AttachmentItem, $$AttachmentsTableReferences),
    AttachmentItem,
    PrefetchHooks Function({bool itineraryId})> {
  $$AttachmentsTableTableManager(_$TripDatabase db, $AttachmentsTable table)
      : super(TableManagerState(
          db: db,
          table: table,
          createFilteringComposer: () =>
              $$AttachmentsTableFilterComposer($db: db, $table: table),
          createOrderingComposer: () =>
              $$AttachmentsTableOrderingComposer($db: db, $table: table),
          createComputedFieldComposer: () =>
              $$AttachmentsTableAnnotationComposer($db: db, $table: table),
          updateCompanionCallback: ({
            Value<String> id = const Value.absent(),
            Value<String> itineraryId = const Value.absent(),
            Value<String> type = const Value.absent(),
            Value<String> filePath = const Value.absent(),
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AttachmentsCompanion(
            id: id,
            itineraryId: itineraryId,
            type: type,
            filePath: filePath,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          createCompanionCallback: ({
            Value<String> id = const Value.absent(),
            required String itineraryId,
            required String type,
            required String filePath,
            Value<DateTime> createdAt = const Value.absent(),
            Value<DateTime> updatedAt = const Value.absent(),
            Value<int> rowid = const Value.absent(),
          }) =>
              AttachmentsCompanion.insert(
            id: id,
            itineraryId: itineraryId,
            type: type,
            filePath: filePath,
            createdAt: createdAt,
            updatedAt: updatedAt,
            rowid: rowid,
          ),
          withReferenceMapper: (p0) => p0
              .map((e) => (
                    e.readTable(table),
                    $$AttachmentsTableReferences(db, table, e)
                  ))
              .toList(),
          prefetchHooksCallback: ({itineraryId = false}) {
            return PrefetchHooks(
              db: db,
              explicitlyWatchedTables: [],
              addJoins: <
                  T extends TableManagerState<
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic,
                      dynamic>>(state) {
                if (itineraryId) {
                  state = state.withJoin(
                    currentTable: table,
                    currentColumn: table.itineraryId,
                    referencedTable:
                        $$AttachmentsTableReferences._itineraryIdTable(db),
                    referencedColumn:
                        $$AttachmentsTableReferences._itineraryIdTable(db).id,
                  ) as T;
                }

                return state;
              },
              getPrefetchedDataCallback: (items) async {
                return [];
              },
            );
          },
        ));
}

typedef $$AttachmentsTableProcessedTableManager = ProcessedTableManager<
    _$TripDatabase,
    $AttachmentsTable,
    AttachmentItem,
    $$AttachmentsTableFilterComposer,
    $$AttachmentsTableOrderingComposer,
    $$AttachmentsTableAnnotationComposer,
    $$AttachmentsTableCreateCompanionBuilder,
    $$AttachmentsTableUpdateCompanionBuilder,
    (AttachmentItem, $$AttachmentsTableReferences),
    AttachmentItem,
    PrefetchHooks Function({bool itineraryId})>;

class $TripDatabaseManager {
  final _$TripDatabase _db;
  $TripDatabaseManager(this._db);
  $$TripsTableTableManager get trips =>
      $$TripsTableTableManager(_db, _db.trips);
  $$ItinerariesTableTableManager get itineraries =>
      $$ItinerariesTableTableManager(_db, _db.itineraries);
  $$FavoriteItinerariesTableTableManager get favoriteItineraries =>
      $$FavoriteItinerariesTableTableManager(_db, _db.favoriteItineraries);
  $$AttachmentsTableTableManager get attachments =>
      $$AttachmentsTableTableManager(_db, _db.attachments);
}
