import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;

part 'app_database.g.dart';

class JournalEntries extends Table {
  TextColumn get id => text().withLength(min: 1, max: 50)();
  TextColumn get title => text().withLength(min: 1, max: 255)();
  TextColumn get content => text()(); // JSON string of rich text content
  DateTimeColumn get timestamp => dateTime()();
  IntColumn get mood => integer()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();
  TextColumn get location => text().nullable()(); // Location name (e.g., "San Francisco, CA")
  RealColumn get latitude => real().nullable()(); // Latitude coordinate
  RealColumn get longitude => real().nullable()(); // Longitude coordinate
  TextColumn get backgroundColorId => text().nullable()(); // Background color identifier (e.g., 'blue', 'lavender')
  IntColumn get wordCount => integer().nullable()(); // Word count of the entry
  IntColumn get charCount => integer().nullable()(); // Character count of the entry

  @override
  Set<Column> get primaryKey => {id};
}

class Attachments extends Table {
  TextColumn get id => text().withLength(min: 1, max: 50)();
  TextColumn get entryId => text().withLength(min: 1, max: 50)();
  TextColumn get type => text().withLength(min: 1, max: 20)(); // 'photo', 'audio', 'video'
  TextColumn get filePath => text()();
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

class Quotes extends Table {
  IntColumn get id => integer()();
  TextColumn get quoteText => text()();
  TextColumn get author => text()();
  DateTimeColumn get createdAt => dateTime()();

  @override
  Set<Column<Object>> get primaryKey => {id};
}

class Prompts extends Table {
  IntColumn get id => integer()();
  TextColumn get name => text()();
  TextColumn get prompt => text()();
  TextColumn get author => text().nullable()();
  DateTimeColumn get createdAt => dateTime()();

  @override
  Set<Column<Object>> get primaryKey => {id};
}

class SavedChats extends Table {
  TextColumn get id => text().withLength(min: 1, max: 50)();
  TextColumn get title => text().withLength(min: 1, max: 255)();
  TextColumn get content => text()(); // JSON or plain text of the chat
  DateTimeColumn get createdAt => dateTime()();
  DateTimeColumn get updatedAt => dateTime()();

  @override
  Set<Column> get primaryKey => {id};
}

@DriftDatabase(tables: [JournalEntries, Attachments, Quotes, Prompts, SavedChats])
class AppDatabase extends _$AppDatabase {
  AppDatabase() : super(_openConnection());

  @override
  int get schemaVersion => 3;

  @override
  MigrationStrategy get migration {
    return MigrationStrategy(
      onCreate: (Migrator m) {
        return m.createAll();
      },
      onUpgrade: (Migrator m, int from, int to) async {
        if (from < 2) {
          await m.createTable(quotes);
        }
        if (from < 3) {
          await m.createTable(savedChats);
        }
      },
    );
  }

  // Journal entry operations
  Future<List<JournalEntry>> getAllJournalEntries() => select(journalEntries).get();

  Stream<List<JournalEntry>> watchAllJournalEntries() => select(journalEntries).watch();

  Stream<List<JournalEntry>> watchJournalEntriesByDate(DateTime date) {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

    return (select(journalEntries)
      ..where((entry) => entry.timestamp.isBetweenValues(startOfDay, endOfDay))
      ..orderBy([(e) => OrderingTerm(expression: e.timestamp, mode: OrderingMode.desc)]))
      .watch();
  }

  Future<JournalEntry?> getJournalEntryById(String id) {
    return (select(journalEntries)..where((entry) => entry.id.equals(id)))
        .getSingleOrNull();
  }

  Future<int> insertJournalEntry(JournalEntriesCompanion entry) {
    return into(journalEntries).insert(entry);
  }

  Future<bool> updateJournalEntry(JournalEntriesCompanion entry) {
    return update(journalEntries).replace(entry);
  }

  Future<int> deleteJournalEntry(String id) {
    return (delete(journalEntries)..where((e) => e.id.equals(id))).go();
  }

  // Attachment operations
  Future<List<Attachment>> getAttachmentsByEntryId(String entryId) {
    return (select(attachments)..where((a) => a.entryId.equals(entryId))).get();
  }

  Stream<List<Attachment>> watchAttachmentsByEntryId(String entryId) {
    return (select(attachments)..where((a) => a.entryId.equals(entryId))).watch();
  }

  Future<Attachment?> getAttachmentById(String id) {
    return (select(attachments)..where((a) => a.id.equals(id))).getSingleOrNull();
  }

  // Get all attachments
  Future<List<Attachment>> getAllAttachments() {
    return select(attachments).get();
  }

  // Get journal entries within a date range
  Future<List<JournalEntry>> getJournalEntriesByDateRange(DateTime startDate, DateTime endDate) {
    return (select(journalEntries)
      ..where((entry) => entry.timestamp.isBetweenValues(startDate, endDate))
      ..orderBy([(e) => OrderingTerm(expression: e.timestamp, mode: OrderingMode.desc)]))
      .get();
  }

  // Get attachments for journal entries within a date range
  Future<List<Attachment>> getAttachmentsForEntriesInDateRange(DateTime startDate, DateTime endDate) async {
    // First, get all journal entries in the date range
    final entries = await getJournalEntriesByDateRange(startDate, endDate);

    if (entries.isEmpty) {
      return [];
    }

    // Extract the entry IDs
    final entryIds = entries.map((e) => e.id).toList();

    // Get all attachments for these entries
    return (select(attachments)
      ..where((a) => a.entryId.isIn(entryIds))
      ..orderBy([(a) => OrderingTerm(expression: a.createdAt, mode: OrderingMode.desc)]))
      .get();
  }

  // Watch attachments for journal entries within a date range
  Stream<List<Attachment>> watchAttachmentsForEntriesInDateRange(DateTime startDate, DateTime endDate) async* {
    // First, get all journal entries in the date range
    final entries = await getJournalEntriesByDateRange(startDate, endDate);

    if (entries.isEmpty) {
      yield [];
      return;
    }

    // Extract the entry IDs
    final entryIds = entries.map((e) => e.id).toList();

    // Watch all attachments for these entries
    yield* (select(attachments)
      ..where((a) => a.entryId.isIn(entryIds))
      ..orderBy([(a) => OrderingTerm(expression: a.createdAt, mode: OrderingMode.desc)]))
      .watch();
  }

  Future<int> insertAttachment(AttachmentsCompanion attachment) {
    return into(attachments).insert(attachment);
  }

  Future<bool> updateAttachment(AttachmentsCompanion attachment) {
    return update(attachments).replace(attachment);
  }

  Future<int> deleteAttachment(String id) {
    return (delete(attachments)..where((a) => a.id.equals(id))).go();
  }

  Future<int> deleteAttachmentsByEntryId(String entryId) {
    return (delete(attachments)..where((a) => a.entryId.equals(entryId))).go();
  }

  // Quotes operations
  Future<List<Quote>> getAllQuotes() => select(quotes).get();

  Stream<List<Quote>> watchAllQuotes() => select(quotes).watch();

  Future<Quote?> getQuoteById(int id) {
    return (select(quotes)..where((q) => q.id.equals(id))).getSingleOrNull();
  }

  Future<int> insertQuote(QuotesCompanion quote) {
    return into(quotes).insert(quote);
  }

  Future<bool> updateQuote(QuotesCompanion quote) {
    return update(quotes).replace(quote);
  }

  Future<int> deleteQuote(int id) {
    return (delete(quotes)..where((q) => q.id.equals(id))).go();
  }

  Future<void> insertQuotes(List<QuotesCompanion> quotes) async {
    await batch((batch) {
      batch.insertAll(this.quotes, quotes);
    });
  }

  // Prompts operations
  Future<List<Prompt>> getAllPrompts() => select(prompts).get();

  Stream<List<Prompt>> watchAllPrompts() => select(prompts).watch();

  Future<Prompt?> getPromptById(int id) {
    return (select(prompts)..where((p) => p.id.equals(id))).getSingleOrNull();
  }

  Future<int> insertPrompt(PromptsCompanion prompt) {
    return into(prompts).insert(prompt);
  }

  Future<void> insertPrompts(List<PromptsCompanion> promptsList) async {
    await batch((batch) {
      batch.insertAll(this.prompts, promptsList);
    });
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'journal.sqlite'));
    return NativeDatabase(file);
  });
}
