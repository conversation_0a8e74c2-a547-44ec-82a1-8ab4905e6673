import 'package:shared_preferences/shared_preferences.dart';

class SettingsPreferences {
  static const String currencyKey = 'selected_currency';
  static const String themeKey = 'selected_theme';
  static const String languageKey = 'selected_language';
  static const String voucherCodeKey = 'voucher_code';

  static Future<void> saveCurrency(String currency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(currencyKey, currency);
  }

  static Future<String?> getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(currencyKey);
  }

  static Future<void> saveTheme(String theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(themeKey, theme);
  }

  static Future<String?> getTheme() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(themeKey);
  }

  static Future<void> saveLanguage(String language) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(languageKey, language);
  }

  static Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(languageKey);
  }

  static Future<void> saveVoucherCode(String voucherCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(voucherCodeKey, voucherCode);
  }

  static Future<String?> getVoucherCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(voucherCodeKey);
  }
}