import 'package:shared_preferences/shared_preferences.dart';

class SettingsPreferences {
  static const String currencyKey = 'selected_currency';
  static const String themeKey = 'selected_theme';
  static const String languageKey = 'selected_language';
  static const String voucherCodeKey = 'voucher_code';
  static const String _onboardingCompletedKey = 'onboarding_completed';
  static const String _morningReminderEnabledKey = 'morning_reminder_enabled';
  static const String _morningReminderTimeKey = 'morning_reminder_time';
  static const String _eveningReminderEnabledKey = 'evening_reminder_enabled';
  static const String _eveningReminderTimeKey = 'evening_reminder_time';

  static Future<void> saveCurrency(String currency) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(currencyKey, currency);
  }

  static Future<String?> getCurrency() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(currencyKey);
  }

  static Future<void> saveTheme(String theme) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(themeKey, theme);
  }

  static Future<String?> getTheme() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(themeKey);
  }

  static Future<void> saveLanguage(String language) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(languageKey, language);
  }

  static Future<String?> getLanguage() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(languageKey);
  }

  static Future<void> saveVoucherCode(String voucherCode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(voucherCodeKey, voucherCode);
  }

  static Future<String?> getVoucherCode() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(voucherCodeKey);
  }

  static Future<bool> isOnboardingCompleted() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_onboardingCompletedKey) ?? false;
  }

  static Future<void> setOnboardingCompleted(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_onboardingCompletedKey, value);
  }

  static Future<bool> isMorningReminderEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_morningReminderEnabledKey) ?? false;
  }

  static Future<void> setMorningReminderEnabled(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_morningReminderEnabledKey, value);
  }

  static Future<String> getMorningReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_morningReminderTimeKey) ?? '08:00';
  }

  static Future<void> setMorningReminderTime(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_morningReminderTimeKey, value);
  }

  static Future<bool> isEveningReminderEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_eveningReminderEnabledKey) ?? false;
  }

  static Future<void> setEveningReminderEnabled(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_eveningReminderEnabledKey, value);
  }

  static Future<String> getEveningReminderTime() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_eveningReminderTimeKey) ?? '20:00';
  }

  static Future<void> setEveningReminderTime(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_eveningReminderTimeKey, value);
  }
}