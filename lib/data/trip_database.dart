import 'dart:io';
import 'package:drift/drift.dart';
import 'package:drift/native.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

part 'trip_database.g.dart';

@DataClassName('TripItem')
class Trips extends Table {
  TextColumn get id =>
      text().withLength(min: 1, max: 36).clientDefault(() => Uuid().v4())();
  TextColumn get title => text().withLength(min: 1, max: 100)();
  DateTimeColumn get startDate => dateTime()();
  DateTimeColumn get endDate => dateTime()();
  TextColumn get city => text().nullable()();
  TextColumn get countryCode => text().nullable()();
  TextColumn get countryName => text().nullable()();
  TextColumn get autocompleteData => text().nullable()();
}

@DataClassName('ItineraryItem')
class Itineraries extends Table {
  TextColumn get id =>
      text().withLength(min: 1, max: 40).clientDefault(() => Uuid().v4())();
  TextColumn get tripId =>
      text().customConstraint('REFERENCES trips(id) NOT NULL')();
  TextColumn get title => text().withLength(min: 1, max: 255)();
  RealColumn get amount => real().nullable()();
  DateTimeColumn get date => dateTime()();
  TextColumn get category => text()();
  TextColumn get location => text().nullable()();
  TextColumn get locationText => text().nullable()();
  TextColumn get airlineName => text().nullable()();
  TextColumn get flightNumber => text().nullable()();
  DateTimeColumn get checkoutDate => dateTime().nullable()();
  TextColumn get description => text().nullable()();
  IntColumn get rank => integer().nullable()();

  @override
  Set<Column> get primaryKey => {id};
}

@DataClassName('AttachmentItem')
class Attachments extends Table {
  TextColumn get id =>
      text().withLength(min: 1, max: 40).clientDefault(() => Uuid().v4())();
  TextColumn get itineraryId =>
      text().customConstraint('REFERENCES itineraries(id) NOT NULL')();
  TextColumn get type => text()(); // 'photo', 'audio', etc.
  TextColumn get filePath => text()(); // Relative path to the file
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();
  DateTimeColumn get updatedAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};
}

class FavoriteItineraries extends Table {
  TextColumn get id => text().withLength(min: 1, max: 40)();
  TextColumn get title => text()();
  RealColumn get amount => real().nullable()();
  DateTimeColumn get date => dateTime()();
  TextColumn get category => text()();
  TextColumn get location => text().nullable()();
  TextColumn get locationText => text().nullable()();
  TextColumn get airlineName => text().nullable()();
  TextColumn get flightNumber => text().nullable()();
  DateTimeColumn get checkoutDate => dateTime().nullable()();
  TextColumn get description => text().nullable()();
  DateTimeColumn get createdAt => dateTime().withDefault(currentDateAndTime)();

  @override
  Set<Column> get primaryKey => {id};
}

@DriftDatabase(tables: [Trips, Itineraries, FavoriteItineraries, Attachments])
class TripDatabase extends _$TripDatabase {
  TripDatabase._internal() : super(_openConnection());

  static final TripDatabase _instance = TripDatabase._internal();

  factory TripDatabase() {
    return _instance;
  }

  @override
  int get schemaVersion => 4;
  @override
  MigrationStrategy get migration => MigrationStrategy(
    onUpgrade: (migrator, from, to) async {
      if (from == 1) {
        await migrator.createTable(favoriteItineraries);
      }
      if (from <= 2) {
        // Add location columns (city and country) in a single migration
        await migrator.addColumn(trips, trips.city);
        await migrator.addColumn(trips, trips.countryCode);
        await migrator.addColumn(trips, trips.countryName);
      }
      if (from <= 3) {
        // Create the Attachments table
        await migrator.createTable(attachments);
      }
    },
  );

  // Trips
  Future<List<TripItem>> getAllTrips() => select(trips).get();

  Future<int> insertTrip(TripsCompanion trip) => into(trips).insert(trip);

  Future<int> updateTrip(TripsCompanion trip) {
    return (update(trips)
      ..where((tbl) => tbl.id.equals(trip.id.value))).write(trip);
  }

  Future<int> deleteTrip(String id) async {
    await (delete(itineraries)..where((tbl) => tbl.tripId.equals(id))).go();
    return (delete(trips)..where((tbl) => tbl.id.equals(id))).go();
  }

  Stream<List<ItineraryItem>> watchItinerariesForTrip(String tripId) {
    return (select(itineraries)
          ..where((tbl) => tbl.tripId.equals(tripId))
          ..orderBy([
            (tbl) =>
                OrderingTerm(expression: tbl.date, mode: OrderingMode.desc),
            (tbl) => OrderingTerm(expression: tbl.rank),
          ]))
        .watch();
  }

  Future<List<ItineraryItem>> getItinerariesForTrip(String tripId) {
    return (select(itineraries)
          ..where((tbl) => tbl.tripId.equals(tripId))
          ..orderBy([
            (tbl) =>
                OrderingTerm(expression: tbl.date, mode: OrderingMode.desc),
            (tbl) => OrderingTerm(expression: tbl.rank),
          ]))
        .get();
  }

  int getHighestItineraryId(List<ItineraryItem> existingItineraries) {
    int highestItineraryId = 0;
    for (int i = 0; i < existingItineraries.length; i++) {
      // calculate the highest id, needed for itinerary id to be inserted
      final parts = existingItineraries[i].id.split('_');
      if (parts.length == 2) {
        final suffix = int.tryParse(parts[1]);
        if (suffix != null && suffix > highestItineraryId) {
          highestItineraryId = suffix;
        }
      }
    }
    return highestItineraryId;
  }

  // inserts an itinerary at a particular date
  Future<String> insertItinerary(ItinerariesCompanion itinerary) async {
    try {
      final tripId = itinerary.tripId.value;
      final existingItineraries =
          await (select(itineraries)
                ..where((tbl) => tbl.tripId.equals(tripId))
                ..orderBy([
                  (tbl) => OrderingTerm(
                    expression: tbl.date,
                    mode: OrderingMode.desc,
                  ),
                  (tbl) => OrderingTerm(expression: tbl.rank),
                ]))
              .get();

      var i = 0;
      for (i = 0; i < existingItineraries.length; i++) {
        DateTime date1 = DateTime(
          itinerary.date.value.year,
          itinerary.date.value.month,
          itinerary.date.value.day,
        );
        DateTime date2 = DateTime(
          existingItineraries[i].date.year,
          existingItineraries[i].date.month,
          existingItineraries[i].date.day,
        );
        if (date1.isAfter(date2) || date1.isAtSameMomentAs(date2)) break;
      }

      int insertPosition = i;
      int highestItineraryId = getHighestItineraryId(existingItineraries);
      final customId = '${tripId}_${highestItineraryId + 1}';

      // Update ranks of existing itineraries to make space for the new one
      for (var i = insertPosition; i < existingItineraries.length; i++) {
        // if (i >= insertPosition) {
        final item = existingItineraries[i];
        await (update(itineraries)..where(
          (tbl) => tbl.id.equals(item.id),
        )).write(ItinerariesCompanion(rank: Value(item.rank! + 1)));
        // }
      }

      final newItinerary = itinerary.copyWith(
        id: Value(customId),
        rank: Value(insertPosition),
      );
      await into(itineraries).insert(newItinerary);
      return customId;
    } catch (e) {
      print('Error inserting itinerary: $e');
      rethrow; // Re-throw to handle in the repository
    }
  }

  // inserts an itinerary below a particular existing itinerary
  Future<String> insertItineraryBelow(
    ItinerariesCompanion movedItinerary,
    ItinerariesCompanion targetItinerary,
  ) async {
    try {
      final tripId = movedItinerary.tripId.value;
      final existingItineraries =
          await (select(itineraries)
                ..where((tbl) => tbl.tripId.equals(tripId))
                ..orderBy([
                  (tbl) => OrderingTerm(
                    expression: tbl.date,
                    mode: OrderingMode.desc,
                  ),
                  (tbl) => OrderingTerm(expression: tbl.rank),
                ]))
              .get();

      int newRank = 0;
      int targetIndex = -1;
      for (var i = 0; i < existingItineraries.length; i++) {
        if (existingItineraries[i].id == targetItinerary.id.value) {
          targetIndex = i;
          break;
        }
      }
      newRank = existingItineraries[targetIndex].rank! + 1;

      if (targetIndex == -1) {
        throw Exception('Target itinerary not found');
      }

      // Update ranks of all itineraries after the insertion point
      for (var i = targetIndex + 1; i < existingItineraries.length; i++) {
        final item = existingItineraries[i];
        if (item.rank! >= newRank && item.id != movedItinerary.id.value) {
          await (update(itineraries)..where(
            (tbl) => tbl.id.equals(item.id),
          )).write(ItinerariesCompanion(rank: Value(item.rank! + 1)));
        }
      }

      int highestItineraryId = getHighestItineraryId(existingItineraries);
      final customId = '${tripId}_${highestItineraryId + 1}';

      final newItinerary = movedItinerary.copyWith(
        id: Value(customId),
        rank: Value(newRank),
      );
      await into(itineraries).insert(newItinerary);
      return customId;
    } catch (e) {
      print('Error inserting itinerary with rank: $e');
      rethrow;
    }
  }

  Future<int> updateItinerary(ItinerariesCompanion itinerary) {
    return (update(itineraries)
      ..where((tbl) => tbl.id.equals(itinerary.id.value))).write(itinerary);
  }

  Future<int> deleteItinerary(ItinerariesCompanion itinerary) async {
    try {
      // First get the full itinerary data to ensure we have the tripId
      final existingItinerary =
          await (select(itineraries)
            ..where((tbl) => tbl.id.equals(itinerary.id.value))).getSingle();
      final tripId = existingItinerary.tripId;

      final existingItineraries =
          await (select(itineraries)
                ..where((tbl) => tbl.tripId.equals(tripId))
                ..orderBy([
                  (tbl) => OrderingTerm(
                    expression: tbl.date,
                    mode: OrderingMode.desc,
                  ),
                  (tbl) => OrderingTerm(expression: tbl.rank),
                ]))
              .get();

      var i = 0;
      for (i = 0; i < existingItineraries.length; i++) {
        if (itinerary.id.value == existingItineraries[i].id) break;
      }
      while (i < existingItineraries.length) {
        final item = existingItineraries[i];
        await (update(itineraries)..where(
          (tbl) => tbl.id.equals(item.id),
        )).write(ItinerariesCompanion(rank: Value(item.rank! - 1)));
        i++;
      }

      // Favorites are independent, so we don't delete them when an itinerary is deleted

      return (delete(itineraries)
        ..where((tbl) => tbl.id.equals(itinerary.id.value))).go();
    } catch (e) {
      print('Error deleting itinerary: $e');
      return 0;
    }
  }

  // Favorite Itineraries
  Future<void> addToFavorites(ItineraryItem itinerary) async {
    try {
      // Check if already in favorites
      final existing = await (select(favoriteItineraries)
        ..where((tbl) => tbl.id.equals(itinerary.id))).get();

      if (existing.isEmpty) {
        // Create a new favorite entry with all the itinerary data
        await into(favoriteItineraries).insert(
          FavoriteItinerariesCompanion(
            id: Value(itinerary.id),
            title: Value(itinerary.title),
            amount: Value(itinerary.amount),
            date: Value(itinerary.date),
            category: Value(itinerary.category),
            location: Value(itinerary.location),
            locationText: Value(itinerary.locationText),
            airlineName: Value(itinerary.airlineName),
            flightNumber: Value(itinerary.flightNumber),
            checkoutDate: Value(itinerary.checkoutDate),
            description: Value(itinerary.description),
          )
        );
      }
    } catch (e) {
      print('Error adding to favorites: $e');
    }
  }

  Future<void> removeFromFavorites(String itineraryId) async {
    try {
      await (delete(favoriteItineraries)
        ..where((tbl) => tbl.id.equals(itineraryId))).go();
    } catch (e) {
      print('Error removing from favorites: $e');
    }
  }

  Future<bool> isItineraryFavorite(String itineraryId) async {
    final result = await (select(favoriteItineraries)
      ..where((tbl) => tbl.id.equals(itineraryId))).get();
    return result.isNotEmpty;
  }

  Stream<List<FavoriteItinerary>> watchFavoriteItineraries() {
    return (select(favoriteItineraries)
      ..orderBy([(t) => OrderingTerm(expression: t.createdAt, mode: OrderingMode.desc)]))
      .watch();
  }

  // Attachments
  Future<List<AttachmentItem>> getAttachmentsByItineraryId(String itineraryId) {
    return (select(attachments)
      ..where((tbl) => tbl.itineraryId.equals(itineraryId))
      ..orderBy([(t) => OrderingTerm(expression: t.createdAt, mode: OrderingMode.desc)]))
      .get();
  }

  Stream<List<AttachmentItem>> watchAttachmentsByItineraryId(String itineraryId) {
    return (select(attachments)
      ..where((tbl) => tbl.itineraryId.equals(itineraryId))
      ..orderBy([(t) => OrderingTerm(expression: t.createdAt, mode: OrderingMode.desc)]))
      .watch();
  }

  Future<AttachmentItem?> getAttachmentById(String id) {
    return (select(attachments)..where((tbl) => tbl.id.equals(id))).getSingleOrNull();
  }

  Future<String> insertAttachment(AttachmentsCompanion attachment) {
    return into(attachments).insert(attachment).then((_) => attachment.id.value);
  }

  Future<int> deleteAttachment(String id) {
    return (delete(attachments)..where((tbl) => tbl.id.equals(id))).go();
  }

  Future<int> deleteAttachmentsByItineraryId(String itineraryId) {
    return (delete(attachments)..where((tbl) => tbl.itineraryId.equals(itineraryId))).go();
  }

  Future<int> updateAttachmentItineraryId(String attachmentId, String newItineraryId) {
    return (update(attachments)..where((tbl) => tbl.id.equals(attachmentId)))
        .write(AttachmentsCompanion(itineraryId: Value(newItineraryId)));
  }
}

LazyDatabase _openConnection() {
  return LazyDatabase(() async {
    final dbFolder = await getApplicationDocumentsDirectory();
    final file = File(p.join(dbFolder.path, 'trips.sqlite'));
    return NativeDatabase(file);
  });
}
