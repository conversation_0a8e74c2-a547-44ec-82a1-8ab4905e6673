import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

class CommonPrompt {
  final String id;
  final String name;
  final String prompt;
  final String model;
  final String analysisStructure;
  final String responseStructure;
  final String systemInstructions;

  CommonPrompt({
    required this.id,
    required this.name,
    required this.prompt,
    required this.model,
    required this.analysisStructure,
    required this.responseStructure,
    required this.systemInstructions,
  });

  factory CommonPrompt.fromJson(Map<String, dynamic> json) {
    return CommonPrompt(
      id: json['id'].toString(),
      name: json['name'] as String,
      prompt: json['prompt'] as String,
      model: json['model'] as String,
      analysisStructure: json['analysis_structure'] as String,
      responseStructure: json['response_structure'] as String,
      systemInstructions: json['system_instructions'] as String,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'prompt': prompt,
    'model': model,
    'analysis_structure': analysisStructure,
    'response_structure': responseStructure,
    'system_instructions': systemInstructions,
  };
}

class CommonPromptService {
  final SupabaseClient _supabase;
  CommonPromptService(this._supabase);

  Future<void> loadAllPrompts() async {
    try {
      final response = await _supabase.from('common_prompts_journai').select();
      for (final promptData in response) {
        await savePromptToPrefs(Map<String, dynamic>.from(promptData as Map));
      }
    } catch (e) {
      rethrow;
    }
  }

  Future<CommonPrompt?> getPrompt(String name) async {
    return await getPromptFromPrefs(name);
  }

  static Future<void> savePromptToPrefs(Map<String, dynamic> promptData) async {
    final prefs = await SharedPreferences.getInstance();
    final key = promptData['name'].toString().toLowerCase();
    await prefs.setString(key, jsonEncode(promptData));
  }

  static Future<CommonPrompt?> getPromptFromPrefs(String name) async {
    final prefs = await SharedPreferences.getInstance();
    final key = name.toLowerCase();
    final jsonString = prefs.getString(key);
    if (jsonString == null) return null;
    final json = Map<String, dynamic>.from(jsonDecode(jsonString) as Map);
    return CommonPrompt.fromJson(json);
  }
} 