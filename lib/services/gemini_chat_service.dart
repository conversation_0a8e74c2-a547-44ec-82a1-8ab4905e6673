import 'package:firebase_ai/firebase_ai.dart';
import 'package:roamr/services/common_prompt_service.dart';

class GeminiChatService {
  static final GeminiChatService instance = GeminiChatService._();
  GenerativeModel? _model;
  ChatSession? _chatSession;
  bool _initialized = false;
  CommonPrompt? _chatPrompt;

  GeminiChatService._();

  bool get isInitialized => _initialized && _model != null && _chatSession != null && _chatPrompt != null;

  Future<void> initialize({String location = 'global'}) async {
    if (_initialized) return;
    _chatPrompt = await CommonPromptService.getPromptFromPrefs('chat_service_roamr');
    if (_chatPrompt == null) {
      throw Exception('Failed to load chat prompt from local cache');
    }
    _model = FirebaseAI.vertexAI(location: location).generativeModel(
      model: _chatPrompt!.model,
      systemInstruction: Content.text(_chatPrompt!.systemInstructions),
    );
    _chatSession = _model!.startChat();
    _initialized = true;
  }



  /// Resets the chat session (clears history)
  void resetChat() {
    if (_model != null) {
      _chatSession = _model!.startChat();
    }
  }

  /// Sends a message and streams the AI's response as text chunks.
  Stream<String> sendMessage(String message) {
    if (_chatSession == null) {
      throw Exception('Chat session not initialized');
    }
    final responseStream = _chatSession!.sendMessageStream(Content.text(message));
    return responseStream.map((chunk) => chunk.text ?? '');
  }

  GenerativeModel? get model => _model;
}
