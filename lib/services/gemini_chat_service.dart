import 'package:firebase_ai/firebase_ai.dart';
import 'package:flutter/foundation.dart';

class GeminiChatService {
  static final GeminiChatService instance = GeminiChatService._();
  GenerativeModel? _model;
  ChatSession? _chatSession;
  bool _initialized = false;

  GeminiChatService._();

  bool get isInitialized => _initialized && _model != null && _chatSession != null;

  Future<void> initialize({String location = 'us-central1'}) async {
    if (_initialized) return;

    try {
      _model = FirebaseAI.vertexAI(location: location).generativeModel(
        model: 'gemini-1.5-flash',
        systemInstruction: Content.text(_getSystemInstructions()),
      );

      _chatSession = _model!.startChat();
      _initialized = true;
      debugPrint('GeminiChatService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing GeminiChatService: $e');
      rethrow;
    }
  }

  String _getSystemInstructions() {
    return '''
You are RoamR AI, a helpful travel assistant integrated into the RoamR travel planning app. Your role is to help users with:

1. Travel planning and itinerary suggestions
2. Destination recommendations based on preferences
3. Travel tips and advice
4. Local culture and customs information
5. Budget planning for trips
6. Packing suggestions
7. Transportation options
8. Accommodation recommendations
9. Food and dining suggestions
10. Activity and attraction recommendations

Guidelines:
- Be friendly, enthusiastic, and helpful
- Provide practical, actionable advice
- Ask clarifying questions when needed
- Consider budget, time constraints, and user preferences
- Suggest specific places, activities, and experiences
- Be concise but informative
- Use emojis sparingly and appropriately
- If you don't know something specific, be honest and suggest reliable sources

Remember: You're helping users plan amazing travel experiences through the RoamR app!
''';
  }

  /// Resets the chat session (clears history)
  void resetChat() {
    if (_model != null) {
      _chatSession = _model!.startChat();
    }
  }

  /// Sends a message and streams the AI's response as text chunks.
  Stream<String> sendMessage(String message) {
    if (_chatSession == null) {
      throw Exception('Chat session not initialized');
    }
    final responseStream = _chatSession!.sendMessageStream(Content.text(message));
    return responseStream.map((chunk) => chunk.text ?? '');
  }

  GenerativeModel? get model => _model;
} 