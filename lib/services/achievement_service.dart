import 'package:roamr/models/achievement.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';

/// Service for calculating and tracking achievements
class AchievementService {
  /// Map of country codes to continents
  static const Map<String, String> countryToContinentMap = {
    // Europe
    'AT': 'Europe', 'BE': 'Europe', 'BG': 'Europe', 'HR': 'Europe', 'CY': 'Europe',
    'CZ': 'Europe', 'DK': 'Europe', 'EE': 'Europe', 'FI': 'Europe', 'FR': 'Europe',
    'DE': 'Europe', 'GR': 'Europe', 'HU': 'Europe', 'IE': 'Europe', 'IT': 'Europe',
    'LV': 'Europe', 'LT': 'Europe', 'LU': 'Europe', 'MT': 'Europe', 'NL': 'Europe',
    'PL': 'Europe', 'PT': 'Europe', 'RO': 'Europe', 'SK': 'Europe', 'SI': 'Europe',
    'ES': 'Europe', 'SE': 'Europe', 'GB': 'Europe', 'UK': 'Europe', 'CH': 'Europe',
    'NO': 'Europe', 'IS': 'Europe',

    // North America
    'US': 'North America', 'CA': 'North America', 'MX': 'North America', 'CR': 'North America',
    'PA': 'North America', 'BS': 'North America', 'JM': 'North America', 'CU': 'North America',

    // South America
    'BR': 'South America', 'AR': 'South America', 'CL': 'South America', 'CO': 'South America',
    'PE': 'South America', 'VE': 'South America', 'EC': 'South America', 'BO': 'South America',

    // Asia
    'CN': 'Asia', 'JP': 'Asia', 'KR': 'Asia', 'IN': 'Asia', 'TH': 'Asia', 'VN': 'Asia',
    'ID': 'Asia', 'MY': 'Asia', 'SG': 'Asia', 'PH': 'Asia', 'AE': 'Asia', 'SA': 'Asia',
    'TR': 'Asia', 'IL': 'Asia', 'LB': 'Asia', 'JO': 'Asia', 'HK': 'Asia', 'TW': 'Asia',

    // Africa
    'ZA': 'Africa', 'EG': 'Africa', 'MA': 'Africa', 'TN': 'Africa', 'KE': 'Africa',
    'NG': 'Africa', 'GH': 'Africa', 'ET': 'Africa', 'TZ': 'Africa', 'UG': 'Africa',

    // Oceania
    'AU': 'Oceania', 'NZ': 'Oceania', 'FJ': 'Oceania', 'PG': 'Oceania',

    // Antarctica
    'AQ': 'Antarctica',
  };

  /// Get continent for a country code
  static String? getContinentForCountry(String? countryCode) {
    if (countryCode == null) return null;
    return countryToContinentMap[countryCode.toUpperCase()];
  }

  /// Calculate achievements based on trips and itineraries
  static List<Achievement> calculateAchievements(
    List<Trip> trips,
    List<TripItinerary> allItineraries,
  ) {
    // Get all predefined achievements
    final achievements = Achievements.getAllAchievements();

    // Calculate destination-based achievements
    _calculateDestinationAchievements(achievements, trips);

    // Calculate expense-based achievements
    _calculateExpenseAchievements(achievements, allItineraries);

    // Calculate frequency-based achievements
    _calculateFrequencyAchievements(achievements, trips);

    // Calculate duration-based achievements
    _calculateDurationAchievements(achievements, trips);

    // Calculate flight-based achievements
    _calculateFlightAchievements(achievements, allItineraries);

    return achievements;
  }

  /// Calculate destination-based achievements
  static void _calculateDestinationAchievements(
    List<Achievement> achievements,
    List<Trip> trips,
  ) {
    // Count unique countries
    final Set<String> uniqueCountries = {};

    // Count unique continents
    final Set<String> uniqueContinents = {};

    // Process all trips
    for (final trip in trips) {
      if (trip.countryCode != null && trip.countryCode!.isNotEmpty) {
        final countryCode = trip.countryCode!.toUpperCase();
        uniqueCountries.add(countryCode);

        // Get continent for country
        final continent = getContinentForCountry(trip.countryCode);
        if (continent != null) {
          uniqueContinents.add(continent);
        }
      }
    }

    // Update country-based achievements
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];

      if (achievement.type == AchievementType.destination) {
        int progress = 0;
        bool isUnlocked = false;

        switch (achievement.id) {
          case 'novice_traveller':
          case 'world_traveller':
          case 'globetrotter':
            progress = uniqueCountries.length;
            isUnlocked = progress >= achievement.targetValue;
            break;

          case 'continental_explorer':
          case 'continental_collector':
          case 'world_conqueror':
            // For continental achievements, we still show continent count as progress
            progress = uniqueContinents.length;

            // But for unlocking, we check BOTH continent count AND country count
            // This ensures that higher-level achievements are truly progressive
            final continentCount = uniqueContinents.length;
            final countryCount = uniqueCountries.length;

            // Each continental achievement requires its continent count AND a minimum country count
            switch (achievement.id) {
              case 'continental_explorer': // Level 4
                // Requires 2+ continents AND at least 20 countries (same as globetrotter)
                isUnlocked = continentCount >= achievement.targetValue && countryCount >= 20;
                break;

              case 'continental_collector': // Level 5
                // Requires 4+ continents AND at least 25 countries
                isUnlocked = continentCount >= achievement.targetValue && countryCount >= 25;
                break;

              case 'world_conqueror': // Level 6
                // Requires all 7 continents AND at least 30 countries
                isUnlocked = continentCount >= achievement.targetValue && countryCount >= 30;
                break;
            }
            break;
        }

        // Update achievement
        achievements[i] = achievement.copyWith(
          progress: progress,
          isUnlocked: isUnlocked,
        );
      }
    }
  }

  /// Calculate expense-based achievements
  static void _calculateExpenseAchievements(
    List<Achievement> achievements,
    List<TripItinerary> allItineraries,
  ) {
    // Calculate total expenses
    double totalExpenses = 0;

    for (final itinerary in allItineraries) {
      if (itinerary.amount != null) {
        totalExpenses += itinerary.amount!;
      }
    }

    // Update expense-based achievements
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];

      if (achievement.type == AchievementType.expense) {
        final progress = totalExpenses.toInt();
        final isUnlocked = progress >= achievement.targetValue;

        // Update achievement
        achievements[i] = achievement.copyWith(
          progress: progress,
          isUnlocked: isUnlocked,
        );
      }
    }
  }

  /// Calculate frequency-based achievements
  static void _calculateFrequencyAchievements(
    List<Achievement> achievements,
    List<Trip> trips,
  ) {
    // Count total trips
    final tripCount = trips.length;

    // Update frequency-based achievements
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];

      if (achievement.type == AchievementType.frequency) {
        final progress = tripCount;
        final isUnlocked = progress >= achievement.targetValue;

        // Update achievement
        achievements[i] = achievement.copyWith(
          progress: progress,
          isUnlocked: isUnlocked,
        );
      }
    }
  }

  /// Calculate duration-based achievements
  static void _calculateDurationAchievements(
    List<Achievement> achievements,
    List<Trip> trips,
  ) {
    // Track longest trip duration
    int longestTripDuration = 0;

    for (final trip in trips) {
      // Calculate trip duration in days (inclusive of start and end dates)
      final duration = trip.endDate.difference(trip.startDate).inDays + 1;

      // We're only tracking longest trip duration now

      // Track longest trip duration
      if (duration > longestTripDuration) {
        longestTripDuration = duration;
      }
    }

    // Update duration-based achievements
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];

      if (achievement.type == AchievementType.duration) {
        int progress = 0;
        bool isUnlocked = false;

        // For all duration achievements, progress is the longest trip duration
        progress = longestTripDuration;

        // Check if the achievement is unlocked based on its ID
        switch (achievement.id) {
          case 'day_tripper':
          case 'weekend_wanderer':
          case 'vacation_voyager':
          case 'extended_explorer':
          case 'long_term_traveler':
          case 'nomadic_adventurer':
            isUnlocked = progress >= achievement.targetValue;
            break;
        }

        // Update achievement
        achievements[i] = achievement.copyWith(
          progress: progress,
          isUnlocked: isUnlocked,
        );
      }
    }
  }

  /// Calculate flight-based achievements
  static void _calculateFlightAchievements(
    List<Achievement> achievements,
    List<TripItinerary> allItineraries,
  ) {
    // Count flights
    int totalFlights = 0;

    for (final itinerary in allItineraries) {
      if (itinerary.category.name == 'flight' &&
          itinerary.airlineName != null &&
          itinerary.airlineName!.isNotEmpty) {
        totalFlights++;
      }
    }

    // Update flight-based achievements
    for (int i = 0; i < achievements.length; i++) {
      final achievement = achievements[i];

      if (achievement.type == AchievementType.flight) {
        // For all flight achievements, progress is simply the total number of flights
        final progress = totalFlights;
        final isUnlocked = progress >= achievement.targetValue;

        // Update achievement
        achievements[i] = achievement.copyWith(
          progress: progress,
          isUnlocked: isUnlocked,
        );
      }
    }
  }
}
