import 'package:firebase_ai/firebase_ai.dart';
import 'package:eunoia/services/common_prompt_service.dart';
import 'package:flutter/material.dart';

class GeminiChatSummaryService {
  static final GeminiChatSummaryService instance = GeminiChatSummaryService._();
  GenerativeModel? _model;
  CommonPrompt? _chatPrompt;

  GeminiChatSummaryService._();

  bool get isInitialized => _model != null && _chatPrompt != null;

  Future<void> initialize() async {
    _chatPrompt = await CommonPromptService.getPromptFromPrefs('analyze_chat');
    if (_chatPrompt == null) {
      throw Exception('Failed to load chat prompt from local cache');
    }
    _model = FirebaseAI.vertexAI().generativeModel(
      model: _chatPrompt!.model,
      systemInstruction: Content.text(_chatPrompt!.systemInstructions),
    );
  }

  Future<String> summarizeChatSession(BuildContext context, List<String> chatMessages) async {
    if (_model == null || _chatPrompt == null) {
      throw Exception('GeminiChatSummaryService not initialized. Call initialize() first.');
    }
    try {
      final buffer = StringBuffer();
      buffer.writeln(_chatPrompt!.prompt);
      buffer.writeln('\nChat Session:');
      for (final msg in chatMessages) {
        buffer.writeln(msg);
      }
      if (_chatPrompt!.analysisStructure.isNotEmpty) {
        buffer.writeln('\nAnalysis Structure:');
        buffer.writeln(_chatPrompt!.analysisStructure);
      }
      if (_chatPrompt!.responseStructure.isNotEmpty) {
        buffer.writeln('\nResponse Structure:');
        buffer.writeln(_chatPrompt!.responseStructure);
      }
      final prompt = buffer.toString();
      debugPrint('DEBUG: Gemini Chat Summary Prompt:');
      debugPrint('System Instructions: ${_chatPrompt!.systemInstructions}');
      debugPrint('Full Prompt: $prompt');
      final content = Content.text(prompt);
      final response = await _model!.generateContent([content]);
      if (response.text == null || response.text!.isEmpty) {
        throw Exception('No response received from Gemini');
      }
      return response.text!.trim();
    } catch (e) {
      throw Exception('Failed to summarize chat session: $e');
    }
  }
} 