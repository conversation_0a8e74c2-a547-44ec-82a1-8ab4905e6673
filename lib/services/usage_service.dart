import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:roamr/models/user_usage.dart';

class UsageService {
  final SupabaseClient _supabaseClient;
  static const String _tableName = 'roamr_user_usage';

  UsageService({SupabaseClient? supabaseClient})
      : _supabaseClient = supabaseClient ?? Supabase.instance.client;

  Future<UserUsage?> getUsage() async {
    final userId = _supabaseClient.auth.currentUser?.id;
    if (userId == null) {
      return null;
    }
    try {
      final data = await _supabaseClient
          .from(_tableName)
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();
      if (data != null) {
        return UserUsage.fromJson(data);
      } else {
        return null;
      }
    } catch (e) {
      print('Failed to get user usage: $e');
      return null;
    }
  }

  Future<void> incrementUsage(String usageType) async {
    final userId = _supabaseClient.auth.currentUser?.id;
    if (userId == null) return;
    const usageColumns = {
      'trips': 'trips_created',
      'attachments': 'attachments_created',
      'itineraries': 'itineraries_created',
    };
    final column = usageColumns[usageType];
    if (column == null) return;
    try {
      final data = await _supabaseClient
          .from(_tableName)
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();
      if (data != null) {
        final int current = data[column] ?? 0;
        await _supabaseClient
            .from(_tableName)
            .update({column: current + 1})
            .eq('user_id', userId);
      } else {
        await _supabaseClient
            .from(_tableName)
            .insert({
              'user_id': userId,
              column: 1,
            });
      }
    } catch (e) {
      print('Failed to increment user usage: $e');
    }
  }
} 