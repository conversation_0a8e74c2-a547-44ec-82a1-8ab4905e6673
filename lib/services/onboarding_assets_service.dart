import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for handling public onboarding assets from Supabase Storage.
class OnboardingAssetsService {
  final SupabaseClient _supabaseClient;
  final String _bucketName;

  static OnboardingAssetsService? _instance;
  static bool _initialized = false;

  /// Private constructor
  OnboardingAssetsService._({
    required SupabaseClient supabaseClient,
    String bucketName = 'onboarding-assets',
  })  : _supabaseClient = supabaseClient,
        _bucketName = bucketName;

  /// Initialize the singleton instance.
  /// Must be called before accessing the instance.
  static void initialize({required SupabaseClient supabaseClient}) {
    if (_initialized) return;
    _instance = OnboardingAssetsService._(supabaseClient: supabaseClient);
    _initialized = true;
  }

  /// Get the singleton instance.
  static OnboardingAssetsService get instance {
    if (!_initialized) {
      throw StateError(
          'OnboardingAssetsService has not been initialized. Call initialize() first.');
    }
    return _instance!;
  }

  /// Gets the public URL for a file in the onboarding-assets bucket.
  ///
  /// [filename] - The name of the file (e.g., 'sample.gif').
  String getPublicUrl(String filename) {
    try {
      return _supabaseClient.storage.from(_bucketName).getPublicUrl(filename);
    } catch (e) {
      // In case of an error, return an empty string or handle it as needed.
      print('Error getting public URL for $filename: $e');
      return '';
    }
  }
} 