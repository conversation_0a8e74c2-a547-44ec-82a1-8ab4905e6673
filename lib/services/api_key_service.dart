import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:roamr/config/api_keys.dart'; // For fallback keys

/// Service to securely fetch API keys from Supabase Edge Functions
class ApiKeyService {
  final SupabaseClient _client = Supabase.instance.client;

  // Cache for API keys to avoid unnecessary network requests
  final Map<String, String> _keyCache = {};

  /// Singleton instance
  static final ApiKeyService _instance = ApiKeyService._internal();

  /// Factory constructor
  factory ApiKeyService() {
    return _instance;
  }

  /// Private constructor
  ApiKeyService._internal();

  /// Get an API key by its type
  /// Returns null if the key cannot be fetched
  Future<String?> getApiKey(String keyType) async {
    try {
      // Check if the key is already in the cache
      if (_keyCache.containsKey(keyType)) {
        return _keyCache[keyType];
      }

      // Check if the user is authenticated
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        debugPrint('Cannot fetch API key from Edge Function: User not authenticated');
        // Return fallback key if user is not authenticated
        return _getFallbackKey(keyType);
      }

      try {
        // Construct the URL with query parameters
        final functionUrl = 'api-keys?key=$keyType';

        // Invoke the Edge Function to get the API key
        final response = await _client.functions.invoke(
          functionUrl,
          method: HttpMethod.get,
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ${_client.auth.currentSession?.accessToken}'
          },
        );

        // Check for errors
        if (response.status != 200) {
          debugPrint('Error fetching API key: Status ${response.status}');
          return _getFallbackKey(keyType);
        }

        // Extract the key from the response
        final key = response.data['key'] as String?;

        // If the key is null or empty, use the fallback key
        if (key == null || key.isEmpty) {
          debugPrint('Key not found in response, using fallback key');
          return _getFallbackKey(keyType);
        }

        // Cache the key for future use
        _keyCache[keyType] = key;
        return key;
      } catch (e) {
        debugPrint('Exception invoking Edge Function: $e');
        return _getFallbackKey(keyType);
      }
    } catch (e) {
      debugPrint('Exception in getApiKey: $e');
      return _getFallbackKey(keyType);
    }
  }

  /// Get a fallback API key when the Edge Function is not available
  String? _getFallbackKey(String keyType) {
    debugPrint('Using fallback key for: $keyType');
    switch (keyType) {
      case 'google_maps':
        return ApiKeys.googleMaps;
      case 'revenue_cat_ios':
        return ApiKeys.revenueCatIOS;
      case 'revenue_cat_android':
        return ApiKeys.revenueCatAndroid;
      default:
        return null;
    }
  }

  /// Get the Google Maps API key
  Future<String?> getGoogleMapsApiKey() async {
    return getApiKey('google_maps');
  }

  /// Get the RevenueCat API key for the current platform
  Future<String?> getRevenueCatApiKey() async {
    if (defaultTargetPlatform == TargetPlatform.iOS) {
      return getApiKey('revenue_cat_ios');
    } else {
      return getApiKey('revenue_cat_android');
    }
  }

  /// Get the Supabase URL
  /// Note: This is a special case - we don't need to fetch this from the Edge Function
  /// because it's already available in the ApiKeys class and is needed for initialization
  Future<String?> getSupabaseUrl() async {
    return ApiKeys.supabaseUrl;
  }

  /// Get the Supabase anon key
  /// Note: This is a special case - we don't need to fetch this from the Edge Function
  /// because it's already available in the ApiKeys class and is needed for initialization
  Future<String?> getSupabaseAnonKey() async {
    return ApiKeys.supabaseAnonKey;
  }

  /// Clear the API key cache
  void clearCache() {
    _keyCache.clear();
  }
}
