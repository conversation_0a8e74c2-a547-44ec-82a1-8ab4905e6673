import 'dart:async';
import 'package:intl/intl.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:purchases_ui_flutter/purchases_ui_flutter.dart';
import 'package:roamr/services/api_key_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

// Our custom SubscriptionStatus enum to represent subscription state
enum SubscriptionStatus {
  active,
  inactive,
  pending
}

class RevenueCatService {
  static final RevenueCatService _instance = RevenueCatService._internal();
  final _purchaseController = StreamController<SubscriptionStatus>.broadcast();

  // Use a static flag to track if we've configured RevenueCat
  static bool _configured = false;

  factory RevenueCatService() {
    return _instance;
  }

  RevenueCatService._internal();

  Stream<SubscriptionStatus> get purchaseStatus => _purchaseController.stream;

  Future<void> initialize() async {
    // If already configured, return immediately
    if (_configured) {
      return;
    }

    try {
      // Initialize RevenueCat

      // Configure debug logs
      await Purchases.setLogLevel(LogLevel.verbose);

      // Get the API key from the API key service
      final apiKeyService = ApiKeyService();
      final apiKey = await apiKeyService.getRevenueCatApiKey();

      // The API key service already handles fallbacks, so we can use it directly
      // If it returns null, there's a serious issue
      if (apiKey == null) {
        throw Exception('Failed to get RevenueCat API key');
      }

      // Configure RevenueCat with API key
      final configuration = PurchasesConfiguration(apiKey);
      configuration.shouldShowInAppMessagesAutomatically = true;
      await Purchases.configure(configuration);

      // Mark as configured
      _configured = true;
      // RevenueCat configured successfully

      // Set up customer info listener
      Purchases.addCustomerInfoUpdateListener((customerInfo) {
        _handleCustomerInfoUpdate(customerInfo);
      });

      // Check initial subscription status
      final customerInfo = await Purchases.getCustomerInfo();
      _handleCustomerInfoUpdate(customerInfo);
    } catch (e) {
      // Error initializing RevenueCat
      _purchaseController.add(SubscriptionStatus.inactive);
    }
  }

  void _handleCustomerInfoUpdate(CustomerInfo customerInfo) {
    final entitlements = customerInfo.entitlements.active;

    // Check if user has active entitlements
    if (entitlements.isNotEmpty) {
      _purchaseController.add(SubscriptionStatus.active);
    } else {
      _purchaseController.add(SubscriptionStatus.inactive);
    }
  }

  Future<List<Package>> loadProducts() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      // Fetch offerings from RevenueCat
      final offerings = await Purchases.getOfferings();

      if (offerings.current != null && offerings.current!.availablePackages.isNotEmpty) {
        return offerings.current!.availablePackages;
      }

      // Return empty list if no packages are available
      return [];
    } catch (e) {
      // Error loading products
      return [];
    }
  }

  Future<void> restorePurchases() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      _purchaseController.add(SubscriptionStatus.pending);

      // Restore purchases
      final customerInfo = await Purchases.restorePurchases();

      // Check if the restore was successful
      if (customerInfo.entitlements.active.isNotEmpty) {
        _purchaseController.add(SubscriptionStatus.active);
      } else {
        _purchaseController.add(SubscriptionStatus.inactive);
      }
    } catch (e) {
      // Error restoring purchases
      _purchaseController.add(SubscriptionStatus.inactive);
    }
  }

  Future<bool> isSubscriptionActive() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      final customerInfo = await Purchases.getCustomerInfo();
      return customerInfo.entitlements.active.isNotEmpty;
    } catch (e) {
      // Error checking subscription status
      return false;
    }
  }

  /// Get subscription details for the current user
  /// Returns a map with subscription details or null if no active subscription
  Future<Map<String, String>?> getSubscriptionDetails() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      final customerInfo = await Purchases.getCustomerInfo();

      // Check if user has any active entitlements
      if (customerInfo.entitlements.active.isEmpty) {
        return null;
      }

      // Get the first active entitlement
      final entitlement = customerInfo.entitlements.active.values.first;

      // Find the associated package for this entitlement
      final offerings = await Purchases.getOfferings();
      Package? associatedPackage;

      // Look through all offerings to find the package that provides this entitlement
      if (offerings.current != null) {
        for (final package in offerings.current!.availablePackages) {
          // Check if this package provides the entitlement
          if (package.storeProduct.identifier == entitlement.productIdentifier) {
            associatedPackage = package;
            break;
          }
        }
      }

      // Get the subscription details
      final Map<String, String> details = {};

      // Add subscription identifier
      details['product'] = entitlement.identifier;

      // Determine subscription type and if it's recurring
      String planType = 'Unknown';
      bool isRecurring = false;

      if (associatedPackage != null) {
        // Use the package type to determine if it's monthly, yearly, etc.
        switch (associatedPackage.packageType) {
          case PackageType.monthly:
            planType = 'Monthly';
            isRecurring = true;
            break;
          case PackageType.annual:
            planType = 'Yearly';
            isRecurring = true;
            break;
          case PackageType.lifetime:
            planType = 'Lifetime';
            isRecurring = false;
            break;
          case PackageType.weekly:
            planType = 'Weekly';
            isRecurring = true;
            break;
          case PackageType.sixMonth:
            planType = '6-Month';
            isRecurring = true;
            break;
          case PackageType.threeMonth:
            planType = '3-Month';
            isRecurring = true;
            break;
          case PackageType.twoMonth:
            planType = '2-Month';
            isRecurring = true;
            break;
          default:
            // Keep as Unknown if we can't determine the type
            break;
        }
      }

      // Check if the subscription will renew (more accurate than string matching)
      isRecurring = entitlement.willRenew;

      // Add plan type to details
      details['plan_type'] = planType;
      details['is_recurring'] = isRecurring.toString();

      // Add expiration/renewal date if available
      if (entitlement.expirationDate != null) {
        try {
          final expiryDate = DateTime.parse(entitlement.expirationDate!);
          details['expires'] = DateFormat.yMMMd().format(expiryDate);
        } catch (e) {
          // If we can't parse the date, don't include it
          // Could not parse expiration date
        }
      } else if (!isRecurring) {
        // Only show 'Never' for non-recurring subscriptions like lifetime
        details['expires'] = 'Never';
      }

      // Add purchase date if available
      try {
        // Use firstSeen as an approximation of purchase date
        final purchaseDate = DateTime.fromMillisecondsSinceEpoch(
          (customerInfo.firstSeen as int) * 1000, // Convert seconds to milliseconds
        );
        details['purchased'] = DateFormat.yMMMd().format(purchaseDate);
      } catch (e) {
        // If we can't determine the purchase date, don't include it
        // Could not determine purchase date
      }

      // Add subscription status (more accurate than hardcoding 'Active')
      details['status'] = entitlement.isActive ? 'Active' : 'Inactive';

      return details;
    } catch (e) {
      // Error getting subscription details
      return null;
    }
  }

  void dispose() {
    _purchaseController.close();
  }

  /// Checks if user is authenticated and returns true if they are
  /// This is used to determine if we should show the paywall or redirect to login
  bool isUserAuthenticated() {
    // Get the current user from Supabase
    final currentUser = Supabase.instance.client.auth.currentUser;
    return currentUser != null;
  }

  /// Identifies the current user with RevenueCat using their email
  /// This ties the subscription to the user's email ID
  /// Returns true if successful, false otherwise
  Future<bool> identifyUser(String email) async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      // Get the current user from Supabase
      final currentUser = Supabase.instance.client.auth.currentUser;

      if (currentUser == null) {
        // Cannot identify user with RevenueCat: No authenticated user
        return false;
      }

      // Use the email as the App User ID for RevenueCat
      // This will ensure subscriptions are tied to the user's email
      // Identifying user with RevenueCat
      final customerInfo = await Purchases.logIn(email);

      // Update subscription status based on the returned customer info
      _handleCustomerInfoUpdate(customerInfo.customerInfo);

      // User identified with RevenueCat successfully
      return true;
    } catch (e) {
      // Error identifying user with RevenueCat
      return false;
    }
  }

  /// Resets the RevenueCat user ID when the user logs out
  /// This ensures the subscription is not accessible when logged out
  Future<void> resetUser() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      // Log out from RevenueCat
      // This will generate a new anonymous ID
      // Logging out user from RevenueCat
      await Purchases.logOut();

      // Update subscription status
      final customerInfo = await Purchases.getCustomerInfo();
      _handleCustomerInfoUpdate(customerInfo);

      // User logged out from RevenueCat successfully
    } catch (e) {
      // Error logging out user from RevenueCat
    }
  }

  /// Presents the RevenueCat paywall
  /// Returns true if the purchase was successful, false otherwise
  Future<bool> presentPaywall() async {
    // Make sure RevenueCat is initialized
    if (!_configured) {
      await initialize();
    }

    try {
      // Ensure the user is properly identified with their email before showing the paywall
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser != null && currentUser.email != null) {
        // Re-identify the user to ensure the purchase is tied to their email
        // Re-identifying user before showing paywall
        await Purchases.logIn(currentUser.email!);

      } else {
        // No authenticated user found or email is null
      }

      // Fetch offerings first to ensure they're available
      final offerings = await Purchases.getOfferings();
      // Get offerings for paywall

      if (offerings.current == null && offerings.all.isEmpty) {
        // No offerings available for paywall
        return false;
      }

      // Present the paywall with the current offering
      final offering = offerings.current ?? offerings.all.values.first;
      // Using offering for paywall

      final result = await RevenueCatUI.presentPaywall(
        offering: offering,
        displayCloseButton: true,
      );

      // Process paywall result

      // Check if the purchase was successful
      if (result == PaywallResult.purchased || result == PaywallResult.restored) {
        // Refresh customer info
        final customerInfo = await Purchases.getCustomerInfo();
        _handleCustomerInfoUpdate(customerInfo);

        // RevenueCat App User ID updated after purchase

        return true;
      }

      return false;
    } catch (e) {
      // Error presenting paywall
      return false;
    }
  }
}
