import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/models/trip.dart';
import 'package:timezone/timezone.dart' as tz;
import 'package:timezone/data/latest.dart' as tz_data;
import 'package:roamr/l10n/app_localizations.dart';

class NotificationService {
  static const int inactivityNotificationId = 1001;
  final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();

  NotificationService._internal();
  static final NotificationService instance = NotificationService._internal();

  AndroidFlutterLocalNotificationsPlugin? get _androidPlugin =>
      _notificationsPlugin.resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();

  IOSFlutterLocalNotificationsPlugin? get _iosPlugin =>
      _notificationsPlugin.resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>();

  Future<void> initialize() async {
    tz_data.initializeTimeZones();
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings();
    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await _notificationsPlugin.initialize(initializationSettings);
    if (Platform.isAndroid) {
      await _createNotificationChannel();
    }
  }

  Future<void> _createNotificationChannel() async {
    const AndroidNotificationChannel channel = AndroidNotificationChannel(
      'inactivity_reminders',
      'Inactivity Reminders',
      description: 'Reminders to plan your trips and itineraries',
      importance: Importance.high,
    );
    await _notificationsPlugin
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
        ?.createNotificationChannel(channel);
  }

  Future<bool> requestNotificationPermissions() async {
    // iOS
    final iOS = await _iosPlugin?.requestPermissions(
      alert: true,
      badge: true,
      sound: true,
    ) ?? false;
    // Android 13+
    final android = Platform.isAndroid
        ? await _androidPlugin?.requestNotificationsPermission() ?? false
        : false;
    return iOS || android;
  }

  Future<bool> checkPermissions() async {
    try {
      // iOS
      final iOS = await _iosPlugin?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      ) ?? false;
      // Android 13+
      final android = Platform.isAndroid
          ? await _androidPlugin?.areNotificationsEnabled() ?? true
          : true;
      return iOS || android;
    } catch (e) {
      return false;
    }
  }

  Future<void> scheduleInactivityNotification({
    required BuildContext context,
    required TripRepository tripRepository,
    Duration delay = const Duration(hours: 4),
  }) async {
    // Cancel any existing inactivity notification
    await cancelInactivityNotification();
    // Check and request notification permissions if needed
    final hasPermission = await checkPermissions();
    if (!hasPermission) {
      await requestNotificationPermissions();
    }
    final localizations = AppLocalizations.of(context)!;
    final trips = await tripRepository.getAllTrips();
    String title;
    String body;
    if (trips.isEmpty) {
      title = localizations.notification_title_no_trips;
      body = localizations.notification_body_no_trips;
    } else {
      final Trip latestTrip = trips.first;
      final itineraries = await tripRepository.getTripItinerariesFuture(latestTrip.id);
      if (itineraries.isEmpty) {
        title = localizations.notification_title_no_itinerary;
        body = localizations.notification_body_no_itinerary(latestTrip.title);
      } else {
        final hasPlaces = itineraries.any((it) => it.category.name == 'sightseeing' || it.category.name == 'place');
        if (!hasPlaces) {
          title = localizations.notification_title_no_places;
          body = localizations.notification_body_no_places(latestTrip.title);
        } else {
          title = localizations.notification_title_keep_planning;
          body = localizations.notification_body_keep_planning(latestTrip.title);
        }
      }
    }
    final scheduledDate = DateTime.now().add(delay);
    final tzDateTime = tz.TZDateTime.from(scheduledDate, tz.local);
    final androidDetails = AndroidNotificationDetails(
      'inactivity_reminders',
      'Inactivity Reminders',
      channelDescription: 'Reminders to plan your trips and itineraries',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
    );
    final iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );
    final details = NotificationDetails(android: androidDetails, iOS: iosDetails);
    await _notificationsPlugin.zonedSchedule(
      inactivityNotificationId,
      title,
      body,
      tzDateTime,
      details,
      androidScheduleMode: AndroidScheduleMode.inexactAllowWhileIdle,
      uiLocalNotificationDateInterpretation: UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  Future<void> cancelInactivityNotification() async {
    await _notificationsPlugin.cancel(inactivityNotificationId);
  }
} 