import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:gotrue/gotrue.dart' as gotrue;
import 'package:roamr/config/api_keys.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class Auth {
  final SupabaseClient _client = Supabase.instance.client;
  final RevenueCatService _revenueCatService = RevenueCatService();

  Stream<User?> get authStateChanges => _client.auth.onAuthStateChange.map((event) => event.session?.user);

  User? get currentUser => _client.auth.currentUser;

  // Sign in with Email and Password
  Future<AuthResponse> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // First authenticate with Supabase
      final AuthResponse response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      // Identify the user with RevenueCat using their email
      // But don't let RevenueCat errors affect the sign-in process
      if (response.user != null) {
        try {
          await _revenueCatService.identifyUser(email);
        } catch (rcError) {
          // Log the error but don't fail the sign-in
        }
      }

      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Send an email OTP for authentication (login or registration)
  /// This is the first step in the authentication process
  Future<void> sendOtp({
    required String email,
    Map<String, dynamic>? data,
    bool isLogin = false,
  }) async {
    try {
      // Send OTP to the user's email
      await _client.auth.signInWithOtp(
        email: email,
        shouldCreateUser: !isLogin, // Create user only if it's registration
        data: data, // User metadata (only used for registration)
      );
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Verify the OTP sent during authentication (login or registration)
  Future<AuthResponse> verifyOtp({
    required String email,
    required String token,
    String? name,
    bool isLogin = false,
  }) async {
    try {
      // Use the appropriate OTP type based on whether it's login or registration
      final AuthResponse response = await _client.auth.verifyOTP(
        email: email,
        token: token,
        type: isLogin ? OtpType.email : OtpType.signup,
      );

      // If verification is successful and we have user data
      if (response.user != null && name != null) {
        // Update user metadata with the name
        await _client.auth.updateUser(UserAttributes(
          data: {'name': name},
        ));

        // Ensure we have the latest user data
        await getLatestUser();
      }

      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Gets the latest user data from Supabase
  /// This is particularly important for Android where state updates might be delayed
  Future<User?> getLatestUser() async {
    try {
      final response = await _client.auth.getUser();
      return response.user;
    } catch (e) {
      // Return current user if getUser fails
      return currentUser;
    }
  }

  Future<void> signOut() async {
    // Reset RevenueCat user before signing out from Supabase
    // But don't let RevenueCat errors affect the sign-out process
    try {
      await _revenueCatService.resetUser();
    } catch (rcError) {
    }

    // Sign out from Supabase
    await _client.auth.signOut();
  }

  // Get the OAuth callback URL based on the platform
  String get _oauthCallbackUrl => ApiKeys.getOAuthCallbackUrl();

  /// Sign in with Google using Supabase OAuth
  Future<void> signInWithGoogle() async {
    try {
      if (kIsWeb) {
        await _client.auth.signInWithOAuth(
          OAuthProvider.google,
          redirectTo: _oauthCallbackUrl,
          authScreenLaunchMode: LaunchMode.externalApplication,
        );
      } else {
        try {
          final GoogleSignIn googleSignIn = GoogleSignIn(
            clientId: ApiKeys.googleIosClientId,
            serverClientId: ApiKeys.googleServerClientId,
          );
          
          final googleUser = await googleSignIn.signIn();
          if (googleUser == null) {
            throw Exception('User cancelled the sign-in flow');
          }
          
          final googleAuth = await googleUser.authentication;
          final accessToken = googleAuth.accessToken;
          final idToken = googleAuth.idToken;

          if (accessToken == null || idToken == null) {
            throw Exception('Failed to get tokens from Google Sign-In');
          }

          await _client.auth.signInWithIdToken(
            provider: OAuthProvider.google,
            idToken: idToken,
            accessToken: accessToken,
          );
        } catch (e) {
          rethrow;
        }
      }
    } on gotrue.AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  /// Sign in with Apple using Supabase OAuth with nonce verification
  Future<void> signInWithApple() async {
    try {
      if (kIsWeb) {
        // For web, use the standard OAuth flow
        await _client.auth.signInWithOAuth(
          OAuthProvider.apple,
          redirectTo: _oauthCallbackUrl,
          authScreenLaunchMode: LaunchMode.externalApplication,
        );
        // The auth state will be updated automatically by Supabase
        return;
      } else {
        // For mobile, use the recommended approach with nonce verification
        final rawNonce = _client.auth.generateRawNonce();
        final hashedNonce = sha256.convert(utf8.encode(rawNonce)).toString();
        
        // Request Apple ID credential
        final credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
          nonce: hashedNonce,
        );

        // Verify we got an ID token
        final idToken = credential.identityToken;
        if (idToken == null) {
          throw const AuthException('Could not find ID Token from Apple credential');
        }

        // Sign in with the ID token and nonce
        await _client.auth.signInWithIdToken(
          provider: OAuthProvider.apple,
          idToken: idToken,
          nonce: rawNonce,
        );
        
        // Get user data from Apple (only available on first sign-in)
        if (credential.givenName != null || credential.familyName != null || credential.email != null) {
          final fullName = '${credential.givenName ?? ''} ${credential.familyName ?? ''}'.trim();
          final userData = <String, dynamic>{};
          
          if (fullName.isNotEmpty) {
            userData['full_name'] = fullName;
            userData['name'] = fullName;  // For backward compatibility
          }
          
          if (credential.email?.isNotEmpty ?? false) {
            userData['email'] = credential.email!;
          }
          
          if (userData.isNotEmpty) {
            // Update the user's metadata with the data from Apple
            await _client.auth.updateUser(
              UserAttributes(
                data: userData,
                email: credential.email?.isNotEmpty ?? false ? credential.email : null,
              ),
            );
          }
        }
        
        // The auth state will be updated automatically by Supabase
        return;
      }
    } on SignInWithAppleAuthorizationException catch (e) {
      if (e.code != AuthorizationErrorCode.canceled) {
        rethrow;
      }
      // User cancelled the sign-in flow
      throw Exception('Sign in with Apple was cancelled');
    } on gotrue.AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      rethrow;
    }
  }

  /// Handle the OAuth callback URL
  Future<gotrue.Session> handleOAuthCallback(Uri uri) async {
    try {
      // For mobile, handle the deep link
      if (!kIsWeb) {
        final response = await _client.auth.getSessionFromUrl(uri);
        if (response.session == null) {
          throw Exception('No session found in OAuth response');
        }
        return response.session!;
      }
      
      // For web, the session should be available in the current session
      final currentSession = _client.auth.currentSession;
      if (currentSession != null) {
        return currentSession;
      }
      
      throw Exception('No session found after OAuth flow');
    } on gotrue.AuthException catch (e) {
      throw _handleAuthException(e);
    } catch (e) {
      rethrow;
    }
  }

  Exception _handleAuthException(AuthException e) {


    // Use error codes instead of messages for more reliable error handling
    // Return localization keys that will be resolved in the UI
    switch (e.code) {
      case 'invalid_email':
        return Exception('error_invalid_email');
      case 'user_disabled':
        return Exception('error_user_disabled');
      case 'user_not_found':
        return Exception('error_user_not_found');
      case 'invalid_credentials':
        return Exception('error_invalid_credentials');
      case 'email_exists':
      case 'user_already_exists':
        return Exception('error_email_exists');
      case 'weak_password':
        return Exception('error_weak_password');
      case 'otp_disabled':
        if (e.message.contains('Signups not allowed')) {
          return Exception('error_email_not_registered');
        }
        return Exception('error_otp_disabled');
      default:
        // Check if the error code or message indicates account deletion
        if (e.code == 'user_banned' ||
            e.message.toLowerCase().contains('delete') ||
            e.message.toLowerCase().contains('removed')) {
          return Exception('error_user_not_found');
        }

        // Provide a more specific error message if available
        if (e.message.isNotEmpty && e.message != 'An unknown error occurred.') {
          return Exception(e.message);
        }

        return Exception('error_unknown');
    }
  }

  /// Deletes the user's account completely using a Supabase Edge Function
  /// This removes the auth record, allowing the user to re-register with the same email
  /// Returns a tuple of (success, errorMessage)
  Future<(bool, String?)> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) {
        return (false, 'No user logged in');
      }

      // Reset RevenueCat user first
      try {
        await _revenueCatService.resetUser();
      } catch (rcError) {
        // Log the error but continue with account deletion
        // RevenueCat reset error during account deletion (non-critical)
      }

      // Call the Edge Function to delete the user account
      // Pass the user ID directly to the Edge Function
      final response = await _client.functions.invoke(
        'delete-account',
        body: {
          'userId': user.id,
        },
      );

      if (response.status != 200) {
        final errorMsg = 'Error from delete-account function: ${response.data}';
        // Log error message silently
        return (false, errorMsg);
      }

      // The user will be automatically signed out after deletion
      // but we'll call signOut explicitly to ensure the local state is cleared
      await signOut();

      return (true, null);
    } catch (e) {
      final errorMsg = 'Error deleting account: $e';
      debugPrint(errorMsg);
      return (false, errorMsg);
    }
  }
}