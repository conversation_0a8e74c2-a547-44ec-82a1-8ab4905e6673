import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:roamr/services/revenue_cat_service.dart';

class Auth {
  final SupabaseClient _client = Supabase.instance.client;
  final RevenueCatService _revenueCatService = RevenueCatService();

  Stream<User?> get authStateChanges => _client.auth.onAuthStateChange.map((event) => event.session?.user);

  User? get currentUser => _client.auth.currentUser;

  // Sign in with Email and Password
  Future<AuthResponse> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // First authenticate with Supabase
      final AuthResponse response = await _client.auth.signInWithPassword(
        email: email,
        password: password,
      );

      // Identify the user with RevenueCat using their email
      // But don't let RevenueCat errors affect the sign-in process
      if (response.user != null) {
        try {
          await _revenueCatService.identifyUser(email);
        } catch (rcError) {
          // Log the error but don't fail the sign-in
          debugPrint('RevenueCat identification error (non-critical): $rcError');
        }
      }

      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<AuthResponse> createUserWithEmailAndPassword({
    required String email,
    required String password,
    String? name,
  }) async {
    try {
      final AuthResponse response = await _client.auth.signUp(
        email: email,
        password: password,
      );

      if (response.user != null) {
        // Create user profile
        await _client.from('profiles').insert({
          'id': response.user!.id,
          'email': email,
          'name': name ?? email.split('@')[0], // Use provided name or default to email prefix
          'groups': [],
        });

        // Identify the user with RevenueCat using their email
        // But don't let RevenueCat errors affect the sign-up process
        try {
          await _revenueCatService.identifyUser(email);
        } catch (rcError) {
          // Log the error but don't fail the sign-up
          debugPrint('RevenueCat identification error (non-critical): $rcError');
        }

        // Ensure we have the latest user data
        await getLatestUser();
      }

      return response;
    } on AuthException catch (e) {
      throw _handleAuthException(e);
    }
  }

  Future<void> signOut() async {
    // Reset RevenueCat user before signing out from Supabase
    // But don't let RevenueCat errors affect the sign-out process
    try {
      await _revenueCatService.resetUser();
    } catch (rcError) {
      // Log the error but don't fail the sign-out
      debugPrint('RevenueCat reset error (non-critical): $rcError');
    }

    // Sign out from Supabase
    await _client.auth.signOut();
  }

  /// Gets the latest user data from Supabase
  /// This is particularly important for Android where state updates might be delayed
  Future<User?> getLatestUser() async {
    try {
      final response = await _client.auth.getUser();
      return response.user;
    } catch (e) {
      // Return current user if getUser fails
      return currentUser;
    }
  }

  Exception _handleAuthException(AuthException e) {
    // Log the full error for debugging
    debugPrint('Auth exception: ${e.message}, code: ${e.code}, status: ${e.statusCode}');

    // Use error codes instead of messages for more reliable error handling
    switch (e.code) {
      case 'invalid_email':
        return Exception('The email address is badly formatted.');
      case 'user_disabled':
        return Exception('This user account has been disabled.');
      case 'user_not_found':
        return Exception('This account does not exist or has been deleted.');
      case 'invalid_credentials':
        return Exception('Wrong email or password provided.');
      case 'email_exists':
      case 'user_already_exists':
        return Exception('The email address is already in use by another account.');
      case 'weak_password':
        return Exception('The password is too weak.');
      default:
        // Check if the error code or message indicates account deletion
        if (e.code == 'user_banned' ||
            e.message.toLowerCase().contains('delete') ||
            e.message.toLowerCase().contains('removed')) {
          return Exception('This account has been deleted or banned.');
        }

        // Provide a more specific error message if available
        if (e.message.isNotEmpty && e.message != 'An unknown error occurred.') {
          return Exception(e.message);
        }

        return Exception('An unknown error occurred. Please try again later.');
    }
  }

  /// Deletes the user's account completely using a Supabase Edge Function
  /// This removes the auth record, allowing the user to re-register with the same email
  /// Returns a tuple of (success, errorMessage)
  Future<(bool, String?)> deleteAccount() async {
    try {
      final user = currentUser;
      if (user == null) {
        return (false, 'No user logged in');
      }

      // Reset RevenueCat user first
      try {
        await _revenueCatService.resetUser();
      } catch (rcError) {
        // Log the error but continue with account deletion
        debugPrint('RevenueCat reset error during account deletion (non-critical): $rcError');
      }

      // Call the Edge Function to delete the user account
      // Pass the user ID directly to the Edge Function
      final response = await _client.functions.invoke(
        'delete-account',
        body: {
          'userId': user.id,
        },
      );

      if (response.status != 200) {
        final errorMsg = 'Error from delete-account function: ${response.data}';
        debugPrint(errorMsg);
        return (false, errorMsg);
      }

      // The user will be automatically signed out after deletion
      // but we'll call signOut explicitly to ensure the local state is cleared
      await signOut();

      return (true, null);
    } catch (e) {
      final errorMsg = 'Error deleting account: $e';
      debugPrint(errorMsg);
      return (false, errorMsg);
    }
  }
}