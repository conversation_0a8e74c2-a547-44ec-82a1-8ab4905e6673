import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:roamr/models/place_prediction.dart';
import 'package:roamr/services/api_key_service.dart';

class GooglePlacesService {
  Future<String?> _getApiKey() async {
    final apiKeyService = ApiKeyService();
    return await apiKeyService.getGoogleMapsApiKey();
  }

  Future<List<PlacePrediction>> getAutocomplete(String input, String sessionToken, {String types = '', Map<String, String>? extraParams}) async {
    final apiKey = await _getApiKey();
    if (apiKey == null) return [];
    final params = {
      'input': input,
      'key': apiKey,
      'sessiontoken': sessionToken,
      if (types.isNotEmpty) 'types': types,
      ...?extraParams,
    };
    final url = Uri.https('maps.googleapis.com', '/maps/api/place/autocomplete/json', params);
    final response = await http.get(url);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      final predictions = (data['predictions'] as List)
          .map((e) => PlacePrediction.fromJson(e))
          .toList();
      return predictions;
    }
    return [];
  }

  Future<Map<String, dynamic>?> getPlaceDetails(String placeId, String sessionToken, {String fields = 'geometry,name,address_component,formatted_address'}) async {
    final apiKey = await _getApiKey();
    if (apiKey == null) return null;
    final params = {
      'place_id': placeId,
      'fields': fields,
      'key': apiKey,
      'sessiontoken': sessionToken,
    };
    final url = Uri.https('maps.googleapis.com', '/maps/api/place/details/json', params);
    final response = await http.get(url);
    if (response.statusCode == 200) {
      final data = json.decode(response.body);
      return data['result'];
    }
    return null;
  }
} 