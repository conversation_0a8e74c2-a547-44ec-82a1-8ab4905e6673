import 'package:flutter/material.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/category_icons_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/widgets/category_selection_screen.dart';

class CategorySelectionWidget extends StatelessWidget {
  const CategorySelectionWidget({
    super.key,
    required this.selectedCategory,
    required this.onCategorySelected,
    this.isEditMode = false,
  });

  final Category selectedCategory;
  final Function(Category) onCategorySelected;
  final bool isEditMode;

  // Main categories to show in the horizontal list
  static const List<Category> mainCategories = [
    Category.flight,
    Category.accommodation,
    Category.restaurant,
    Category.transportation,
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    // If in edit mode, only show the selected category
    // If in add mode, ensure selected category is visible by adding it at the first position
    final categoriesToShow = isEditMode
        ? [selectedCategory]
        : mainCategories.contains(selectedCategory)
            ? mainCategories
            : [selectedCategory, ...mainCategories.sublist(0, mainCategories.length - 1)];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Title
        Text(
          localizations.category,
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.primary,
          ),
        ),

        const SizedBox(height: 16),

        // Category circles with icons - evenly spaced
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: categoriesToShow.map((category) => _buildCategoryCircle(
            context,
            category,
            isSelected: category == selectedCategory,
          )).toList(),
        ),

        // "See More" button at the bottom left - only show in add mode
        if (!isEditMode) ...[
          const SizedBox(height: 16),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton.icon(
              onPressed: () async {
                final selectedCategory = await Navigator.push<Category>(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CategorySelectionScreen(
                      selectedCategory: this.selectedCategory,
                    ),
                  ),
                );

                if (selectedCategory != null) {
                  onCategorySelected(selectedCategory);
                }
              },
              icon: Text(
                localizations.see_more,
                style: textTheme.bodySmall?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
              label: Icon(
                Icons.chevron_right,
                size: 16,
                color: colorScheme.primary,
              ),
              style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                minimumSize: Size.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCategoryCircle(
    BuildContext context,
    Category category,
    {bool isSelected = false}
  ) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    // Fixed size for category circles
    const double circleSize = 70.0;

    return GestureDetector(
      onTap: isEditMode ? null : () => onCategorySelected(category),
      child: SizedBox(
        width: circleSize,
        child: Column(
          children: [
            // Circle with icon
            Container(
              width: circleSize * 0.8,
              height: circleSize * 0.8,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected
                    ? colorScheme.primary.withAlpha(50)
                    : theme.brightness == Brightness.dark
                        ? Colors.black.withAlpha(50)
                        : Colors.white,
                border: Border.all(
                  color: isSelected
                      ? colorScheme.primary
                      : colorScheme.onSurface.withAlpha(30),
                  width: 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: colorScheme.primary.withAlpha(40),
                          blurRadius: 8,
                          spreadRadius: 1,
                        )
                      ]
                    : null,
              ),
              child: Icon(
                IconUtils.getIconForCategory(category),
                color: isSelected
                    ? colorScheme.primary
                    : colorScheme.onSurface.withAlpha(180),
                size: circleSize * 0.4,
              ),
            ),

            // Category name
            const SizedBox(height: 4),
            Text(
              category.getName(context),
              style: textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? CustomTheme.getSelectedChipTextColor(context)
                    : colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
