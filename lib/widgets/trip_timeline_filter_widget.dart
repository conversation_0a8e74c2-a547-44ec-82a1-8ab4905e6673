import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/theme/custom_theme.dart';

class TripTimelineFilterWidget extends StatelessWidget {
  const TripTimelineFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    const categories = Category.values;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    final activeFilter = context.select(
      (TripTimelineBloc bloc) => bloc.state.filter,
    );

    return LimitedBox(
      maxHeight: 40,
      child: ListView.separated(
        shrinkWrap: true,
        padding: const EdgeInsets.symmetric(horizontal: 8),
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          final category = categories[index];

          return ChoiceChip(
            label: Text(category.getName(context)),
            selected: activeFilter == category,
            onSelected:
                (_) => context.read<TripTimelineBloc>().add(
                  TripTimelineCategoryFilterChanged(category: category),
                ),
            padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 0),
            selectedColor: colorScheme.secondary.withAlpha(50),
            backgroundColor: theme.cardColor,
            labelStyle: theme.textTheme.bodyMedium?.copyWith(
              color: activeFilter == category
                  ? CustomTheme.getSelectedChipTextColor(context)
                  : colorScheme.onSurface,
              fontWeight: activeFilter == category
                  ? FontWeight.bold
                  : FontWeight.normal,
            ),
          );
        },
        separatorBuilder: (context, index) => const SizedBox(width: 8),
        itemCount: Category.values.length,
      ),
    );
  }
}
