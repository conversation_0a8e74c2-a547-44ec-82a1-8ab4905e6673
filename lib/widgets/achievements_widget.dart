import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/models/achievement.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/pages/achievements_detail_screen.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/services/achievement_service.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/achievement_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class AchievementsWidget extends StatefulWidget {
  const AchievementsWidget({super.key});

  @override
  State<AchievementsWidget> createState() => _AchievementsWidgetState();
}

class _AchievementsWidgetState extends State<AchievementsWidget> {
  bool _isLoading = true;
  List<Achievement> _achievements = [];

  @override
  void initState() {
    super.initState();
    _loadAchievements();
  }

  Future<void> _loadAchievements() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get the repository first
      final repository = context.read<TripRepository>();

      // Get trips directly from the repository instead of the bloc
      final trips = await repository.getAllTrips();

      if (trips.isEmpty) {
        setState(() {
          _isLoading = false;
          _achievements = Achievements.getAllAchievements();
          _filterAchievements();
        });
        return;
      }



      // Get all itineraries for all trips
      final List<TripItinerary> allItineraries = [];

      for (final trip in trips) {
        final itineraries = await repository.getTripItinerariesFuture(trip.id);
        allItineraries.addAll(itineraries);
      }



      // Calculate achievements
      final achievements = AchievementService.calculateAchievements(
        trips,
        allItineraries,
      );



      setState(() {
        _isLoading = false;
        _achievements = achievements;
        _filterAchievements();
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _achievements = Achievements.getAllAchievements();
        _filterAchievements();
      });
    }
  }

  void _filterAchievements() {
    // First filter to only unlocked achievements
    final unlockedAchievements = _achievements.where((a) => a.isUnlocked).toList();

    // Group achievements by type
    final Map<AchievementType, List<Achievement>> achievementsByType = {};
    for (final achievement in unlockedAchievements) {
      if (!achievementsByType.containsKey(achievement.type)) {
        achievementsByType[achievement.type] = [];
      }
      achievementsByType[achievement.type]!.add(achievement);
    }

    // For each type, find the best achievement (highest target value)
    final List<Achievement> bestAchievements = [];
    achievementsByType.forEach((type, achievements) {
      if (achievements.isNotEmpty) {
        // Sort by level in descending order to get the most advanced achievement
        achievements.sort((a, b) => b.level.compareTo(a.level));
        // Add the best achievement for this type
        bestAchievements.add(achievements.first);
      }
    });

    // Sort by achievement type
    bestAchievements.sort((a, b) => a.type.index.compareTo(b.type.index));

    // Update the achievements list
    _achievements = bestAchievements;
  }



  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      elevation: 4,
      color: CustomTheme.getCardBackgroundColor(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: _isLoading
          ? const SizedBox(
              height: 200,
              child: Center(child: CircularProgressIndicator()),
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Padding(
                  padding: const EdgeInsets.fromLTRB(16.0, 12.0, 8.0, 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Title with icon
                      Row(
                        children: [
                          Icon(
                            Icons.emoji_events,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8.0),
                          Text(
                            localizations.achievements,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: CustomTheme.getPrimaryTextColor(context),
                            ),
                          ),
                        ],
                      ),

                      // View All button
                      InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const AchievementsDetailScreen(),
                            ),
                          );
                        },
                        borderRadius: BorderRadius.circular(16),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            localizations.view_all,
                            style: theme.textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Custom layout for achievements
                if (_achievements.isNotEmpty)
                  Column(
                    children: [
                      // First row
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            for (int i = 0; i < min(3, _achievements.length); i++)
                              Expanded(child: _buildAchievementItem(context, _achievements[i])),
                          ],
                        ),
                      ),

                      // Add a small space between rows
                      const SizedBox(height: 0),

                      // Second row if needed
                      if (_achievements.length > 3)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              // Show up to 2 more achievements in the second row
                              for (int i = 3; i < min(5, _achievements.length); i++)
                                Expanded(child: _buildAchievementItem(context, _achievements[i])),

                              // Add empty spaces if we have fewer than 5 achievements
                              for (int i = 0; i < 5 - min(5, _achievements.length); i++)
                                Expanded(child: Container()),
                            ],
                          ),
                        ),
                    ],
                  ),

                // Show a message if no achievements are unlocked
                if (_achievements.isEmpty)
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Center(
                      child: Text(
                        localizations.complete_trips_for_achievements,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          color: Colors.grey,
                        ),
                      ),
                    ),
                  ),

                const SizedBox(height: 8.0),
              ],
            ),
    );
  }



  // Helper method to get localized title
  String _getLocalizedTitle(BuildContext context, Achievement achievement) {
    final localizations = AppLocalizations.of(context)!;

    // Try to access the localized string directly
    switch (achievement.id) {
      case 'novice_traveller':
        return localizations.novice_traveller_title;
      case 'world_traveller':
        return localizations.world_traveller_title;
      case 'globetrotter':
        return localizations.globetrotter_title;
      case 'continental_explorer':
        return localizations.continental_explorer_title;
      case 'continental_collector':
        return localizations.continental_collector_title;
      case 'world_conqueror':
        return localizations.world_conqueror_title;
      case 'budget_tracker':
        return localizations.budget_tracker_title;
      case 'expense_manager':
        return localizations.expense_manager_title;
      case 'financial_voyager':
        return localizations.financial_voyager_title;
      case 'luxury_traveller':
        return localizations.luxury_traveller_title;
      case 'travel_beginner':
        return localizations.travel_beginner_title;
      case 'travel_enthusiast':
        return localizations.travel_enthusiast_title;
      case 'travel_addict':
        return localizations.travel_addict_title;
      case 'day_tripper':
        return localizations.day_tripper_title;
      case 'weekend_wanderer':
        return localizations.weekend_wanderer_title;
      case 'vacation_voyager':
        return localizations.vacation_voyager_title;
      case 'extended_explorer':
        return localizations.extended_explorer_title;
      case 'long_term_traveler':
        return localizations.long_term_traveler_title;
      case 'nomadic_adventurer':
        return localizations.nomadic_adventurer_title;
      case 'first_flight':
        return localizations.first_flight_title;
      case 'frequent_flyer':
        return localizations.frequent_flyer_title;
      case 'aviation_enthusiast':
        return localizations.aviation_enthusiast_title;
      default:
        return achievement.title;
    }
  }

  // Helper method to get localized description
  String _getLocalizedDescription(BuildContext context, Achievement achievement) {
    final localizations = AppLocalizations.of(context)!;

    // Try to access the localized string directly
    switch (achievement.id) {
      case 'novice_traveller':
        return localizations.novice_traveller_desc;
      case 'world_traveller':
        return localizations.world_traveller_desc;
      case 'globetrotter':
        return localizations.globetrotter_desc;
      case 'continental_explorer':
        return localizations.continental_explorer_desc;
      case 'continental_collector':
        return localizations.continental_collector_desc;
      case 'world_conqueror':
        return localizations.world_conqueror_desc;
      case 'budget_tracker':
        return localizations.budget_tracker_desc;
      case 'expense_manager':
        return localizations.expense_manager_desc;
      case 'financial_voyager':
        return localizations.financial_voyager_desc;
      case 'luxury_traveller':
        return localizations.luxury_traveller_desc;
      case 'travel_beginner':
        return localizations.travel_beginner_desc;
      case 'travel_enthusiast':
        return localizations.travel_enthusiast_desc;
      case 'travel_addict':
        return localizations.travel_addict_desc;
      case 'day_tripper':
        return localizations.day_tripper_desc;
      case 'weekend_wanderer':
        return localizations.weekend_wanderer_desc;
      case 'vacation_voyager':
        return localizations.vacation_voyager_desc;
      case 'extended_explorer':
        return localizations.extended_explorer_desc;
      case 'long_term_traveler':
        return localizations.long_term_traveler_desc;
      case 'nomadic_adventurer':
        return localizations.nomadic_adventurer_desc;
      case 'first_flight':
        return localizations.first_flight_desc;
      case 'frequent_flyer':
        return localizations.frequent_flyer_desc;
      case 'aviation_enthusiast':
        return localizations.aviation_enthusiast_desc;
      default:
        return achievement.description;
    }
  }

  Widget _buildAchievementItem(BuildContext context, Achievement achievement) {
    final theme = Theme.of(context);
    final progress = achievement.progress;
    final target = achievement.targetValue;
    final localizedDescription = _getLocalizedDescription(context, achievement);

    return Tooltip(
      message: localizedDescription,
      textStyle: TextStyle(
        fontSize: 14,
        color: Colors.white,
      ),
      child: Card(
        elevation: 0,
        margin: EdgeInsets.zero,
        color: theme.cardColor.withAlpha(150),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: theme.brightness == Brightness.dark
                ? Colors.white.withAlpha(30)
                : Colors.white.withAlpha(100),
            width: 0.5,
          ),
        ),
        child: Container(
          padding: const EdgeInsets.all(2.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Badge using the buildBadgeWithStar utility method
              AchievementUtils.buildBadgeWithStar(
                achievement.type,
                achievement.level,
                isUnlocked: true, // Always true here since we only show unlocked achievements
              ),

              const SizedBox(height: 2.0),

              // Title
              Text(
                _getLocalizedTitle(context, achievement),
                textAlign: TextAlign.center,
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: CustomTheme.getPrimaryTextColor(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              // Progress text
              Text(
                '$progress/$target',
                style: theme.textTheme.bodySmall?.copyWith(
                  fontSize: 9,
                  color: AchievementUtils.getColorForType(achievement.type),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }


}
