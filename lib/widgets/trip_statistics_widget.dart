import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_state.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/trip_repository.dart';
// Chart widgets will be provided by the parent
import 'package:roamr/widgets/trip_dropdown_widget.dart';

class TripStatisticsWidget extends StatefulWidget {
  final Widget Function(List<TripItinerary?> itineraries, double totalExpenses, Widget tripDropdown) childBuilder;

  const TripStatisticsWidget({
    super.key,
    required this.childBuilder,
  });

  @override
  State<TripStatisticsWidget> createState() => _TripStatisticsWidgetState();
}

class _TripStatisticsWidgetState extends State<TripStatisticsWidget> {
  Trip? _selectedTrip;
  late TripTimelineBloc _timelineBloc;
  List<TripItinerary> _allItineraries = [];
  double _totalExpensesAllTrips = 0;
  bool _isLoadingAllTrips = false;

  @override
  void initState() {
    super.initState();
    _timelineBloc = TripTimelineBloc(
      repository: context.read<TripRepository>(),
      tripId: '',
    );
  }

  @override
  void dispose() {
    _timelineBloc.close();
    super.dispose();
  }

  void _onTripSelected(Trip? trip) {
    if (trip != null && trip != _selectedTrip) {
      setState(() {
        _selectedTrip = trip;
      });

      if (trip.id == kAllTripsOption.id) {
        // Handle "All" trips option
        _loadAllTripsData();
      } else {
        // Load itineraries for the selected trip
        _timelineBloc = TripTimelineBloc(
          repository: context.read<TripRepository>(),
          tripId: trip.id,
        );
        _timelineBloc.add(const TripTimelineSubscriptionRequested());
      }
    }
  }

  void _loadAllTripsData() {
    // Get all trips from the AddTripBloc
    final tripsState = context.read<AddTripBloc>().state;
    if (tripsState.trips.isEmpty) return;

    setState(() {
      _isLoadingAllTrips = true;
      _allItineraries = [];
      _totalExpensesAllTrips = 0;
    });

    // Load itineraries for all trips
    int tripsLoaded = 0;
    for (final trip in tripsState.trips) {
      final repository = context.read<TripRepository>();
      repository.getTripItineraries(trip.id).listen((itineraries) {
        setState(() {
          // Add itineraries to the combined list
          _allItineraries.addAll(itineraries);

          // Calculate total expenses
          _totalExpensesAllTrips = _allItineraries.fold(
            0,
            (sum, itinerary) => sum + (itinerary.amount ?? 0)
          );

          // Mark as loaded when all trips are processed
          tripsLoaded++;
          if (tripsLoaded >= tripsState.trips.length) {
            _isLoadingAllTrips = false;
          }
        });
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Check if there are any trips
    final tripsState = context.read<AddTripBloc>().state;
    final hasTrips = tripsState.trips.isNotEmpty;

    // Create the trip dropdown widget
    final tripDropdown = TripDropdownWidget(
      selectedTrip: _selectedTrip,
      onTripSelected: _onTripSelected,
    );

    // If there are no trips, still show the chart widget with empty data
    if (!hasTrips) {
      return widget.childBuilder(
        [], // Empty itineraries list
        0.0, // Zero total expenses
        tripDropdown,
      );
    }

    // If no selection yet, initialize with default selection but still show the widget
    if (_selectedTrip == null) {
      // We'll initialize the selection in the dropdown, but still render the chart
      return widget.childBuilder(
        [], // Empty itineraries list
        0.0, // Zero total expenses
        tripDropdown,
      );
    }

    // If "All" is selected, show combined data
    if (_selectedTrip!.id == kAllTripsOption.id) {
      return _buildAllTripsChart();
    }

    // Otherwise show data for the selected trip
    return BlocProvider.value(
      value: _timelineBloc,
      child: BlocBuilder<TripTimelineBloc, TripTimelineState>(
        builder: (context, state) {
          if (state.status == TripTimelineStatus.loading) {
            // Even when loading, we still want to show the chart widget with the dropdown
            return widget.childBuilder(
              [], // Empty itineraries list while loading
              0.0, // Zero total expenses while loading
              tripDropdown,
            );
          }

          // Use the childBuilder to create the appropriate widget
          return widget.childBuilder(
            state.itineraries,
            state.totalExpenses,
            tripDropdown,
          );
        },
      ),
    );
  }

  Widget _buildAllTripsChart() {
    // Create the trip dropdown widget
    final tripDropdown = TripDropdownWidget(
      selectedTrip: _selectedTrip,
      onTripSelected: _onTripSelected,
    );

    // Even when loading, we still want to show the chart widget
    if (_isLoadingAllTrips) {
      return widget.childBuilder(
        [], // Empty itineraries list while loading
        0.0, // Zero total expenses while loading
        tripDropdown,
      );
    }

    // Use the childBuilder to create the appropriate widget
    return widget.childBuilder(
      _allItineraries,
      _totalExpensesAllTrips,
      tripDropdown,
    );
  }
}
