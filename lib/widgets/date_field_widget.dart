import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_state.dart';
import 'package:roamr/utils/date_utils.dart' as CustomDateUtils;
import 'package:roamr/l10n/app_localizations.dart';

class DateFieldWidget extends StatelessWidget {
  final String label;
  final bool isRequired;
  final DateTime? initialDate;
  final Function(DateTime?) onDateChanged;
  final DateTime? firstDate;
  final DateTime? lastDate;

  const DateFieldWidget({
    super.key,
    required this.label,
    required this.onDateChanged,
    this.isRequired = false,
    this.initialDate,
    this.firstDate,
    this.lastDate,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final state = context.watch<ItineraryFormBloc>().state;

    // Check if date is outside valid range
    final bool isDateOutOfRange = initialDate != null &&
        ((firstDate != null && initialDate!.isBefore(firstDate!)) ||
         (lastDate != null && initialDate!.isAfter(lastDate!)));

    // Format the date for display
    final formattedDate = initialDate != null
        ? CustomDateUtils.DateUtils.formatDate2(initialDate!)
        : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        RichText(
          text: TextSpan(
            text: label,
            style: textTheme.bodySmall,
            children: isRequired ? const [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ] : null,
          ),
        ),
        const SizedBox(height: 8),

        // Date selector that looks like a button
        InkWell(
          onTap: state.status.isLoaded ? () async {
            final effectiveInitialDate = initialDate ?? DateTime.now();
            final effectiveFirstDate = firstDate ?? DateTime(2000);
            final effectiveLastDate = lastDate ?? DateTime(2100);

            // Ensure initialDate is within the valid range
            final validInitialDate = effectiveInitialDate.isBefore(effectiveFirstDate)
                ? effectiveFirstDate
                : (effectiveInitialDate.isAfter(effectiveLastDate)
                    ? effectiveLastDate
                    : effectiveInitialDate);

            final selectedDate = await showDatePicker(
              context: context,
              initialDate: validInitialDate,
              firstDate: effectiveFirstDate,
              lastDate: effectiveLastDate,
              builder: (context, child) {
                return Theme(
                  data: Theme.of(context).copyWith(
                    colorScheme: colorScheme,
                  ),
                  child: child!,
                );
              },
            );
            if (selectedDate != null) {
              onDateChanged(selectedDate);
            }
          } : null,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
            decoration: BoxDecoration(
              color: theme.brightness == Brightness.dark
                  ? Colors.black.withAlpha(50)
                  : Colors.white,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: isDateOutOfRange
                    ? Colors.red
                    : colorScheme.onSurface.withAlpha(50),
                width: 1,
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  formattedDate.isNotEmpty ? formattedDate : AppLocalizations.of(context)!.select_date_range,
                  style: textTheme.bodyLarge?.copyWith(
                    color: formattedDate.isNotEmpty
                        ? colorScheme.onSurface
                        : theme.hintColor.withAlpha(150),
                  ),
                ),
                Icon(
                  Icons.calendar_today,
                  color: colorScheme.primary,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        // Error message if date is out of range
        if (isDateOutOfRange || (isRequired && formattedDate.isEmpty))
          Padding(
            padding: const EdgeInsets.only(top: 6, left: 12),
            child: Text(
              isDateOutOfRange
                  ? AppLocalizations.of(context)!.date_within_trip
                  : AppLocalizations.of(context)!.date_required,
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ),
      ],
    );
  }
}