import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// A controller for a date range text field.
class DateRangeController extends TextEditingController {
  DateTime? _startDate;
  DateTime? _endDate;
  final DateFormat _dateFormat = DateFormat('MMM d, yyyy');

  /// Get the start date of the range
  DateTime? get startDate => _startDate;

  /// Get the end date of the range
  DateTime? get endDate => _endDate;

  /// Set the date range and update the text
  void setDateRange(DateTime start, DateTime end) {
    _startDate = start;
    _endDate = end;
    _updateText();
  }

  /// Clear the date range
  void clearDateRange() {
    _startDate = null;
    _endDate = null;
    text = '';
  }

  /// Update the text based on the current date range
  void _updateText() {
    if (_startDate != null && _endDate != null) {
      final startText = _dateFormat.format(_startDate!);
      final endText = _dateFormat.format(_endDate!);
      text = '$startText - $endText';
    } else {
      text = '';
    }
  }

  @override
  void dispose() {
    super.dispose();
  }
}
