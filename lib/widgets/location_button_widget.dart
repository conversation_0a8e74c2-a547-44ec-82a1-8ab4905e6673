import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:roamr/l10n/app_localizations.dart';

class LocationButtonWidget extends StatelessWidget {
  final GoogleMapController? mapController;
  final double bottom;
  final double right;
  final bool mini;
  final Color? backgroundColor;
  final Color? foregroundColor;

  const LocationButtonWidget({
    super.key,
    required this.mapController,
    this.bottom = 16.0,
    this.right = 16.0,
    this.mini = true,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Positioned(
      bottom: bottom,
      right: right,
      child: FloatingActionButton(
        mini: mini,
        backgroundColor: backgroundColor ?? colorScheme.surface.withAlpha(204), // 0.8 opacity (204/255)
        foregroundColor: foregroundColor ?? colorScheme.onSurface,
        elevation: 4,
        onPressed: () {
          _goToCurrentLocation(context);
        },
        child: const Icon(Icons.my_location),
      ),
    );
  }

  // Method to get current location and move map to it
  Future<void> _goToCurrentLocation(BuildContext context) async {
    try {
      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          // Permissions are denied, show a snackbar
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(AppLocalizations.of(context)!.location_permission_denied)),
            );
          }
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        // Permissions are permanently denied, show a snackbar
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(AppLocalizations.of(context)!.location_permission_permanently_denied)),
          );
        }
        return;
      }

      // Get current position
      final Position position = await Geolocator.getCurrentPosition();

      // Move camera to current position
      if (mapController != null && context.mounted) {
        mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(
            LatLng(position.latitude, position.longitude),
            15,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        final errorMessage = '${AppLocalizations.of(context)!.location_error}: $e';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage)),
        );
      }
    }
  }
}
