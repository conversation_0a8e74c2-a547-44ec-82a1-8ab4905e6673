import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:geolocator/geolocator.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/widgets/location_button_widget.dart';
import 'package:roamr/widgets/map_search_box_widget.dart';
import 'package:roamr/firebase/analytics_service.dart';
import 'package:roamr/models/trip_itinerary.dart';

class MapPickerWidget extends StatefulWidget {
  final LatLng? initialLocation;
  final TripItinerary? itinerary;

  const MapPickerWidget({
    super.key,
    this.initialLocation,
    this.itinerary,
  });

  @override
  State<MapPickerWidget> createState() => _MapPickerWidgetState();
}

class _MapPickerWidgetState extends State<MapPickerWidget> {
  LatLng? _selectedLocation;
  LatLng _initialLocation = LatLng(13.7625554,100.4342089); // Default location (Bkk)
  GoogleMapController? _mapController;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();

    // If an initial location is provided, use it
    if (widget.initialLocation != null) {
      setState(() {
        _initialLocation = widget.initialLocation!;
        _selectedLocation = widget.initialLocation!;
        _isLoading = false;
      });
    } else {
      // Otherwise, try to get the user's current location
      _initializeLocationService();
    }
  }

  Future<void> _initializeLocationService() async {
    try {
      // First check if location service is enabled
      final serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Check current permission status
      var permissionGranted = await Geolocator.checkPermission();

      // If permission is denied and not permanently denied, request it
      if (permissionGranted == LocationPermission.denied) {
        permissionGranted = await Geolocator.requestPermission();
      }

      // If we have permission, get the current location
      if (permissionGranted == LocationPermission.whileInUse ||
          permissionGranted == LocationPermission.always) {
        final currentPosition = await Geolocator.getCurrentPosition();
        setState(() {
          _initialLocation = LatLng(currentPosition.latitude, currentPosition.longitude);
          _selectedLocation = _initialLocation;
          _isLoading = false;
        });
      } else {
        // If permission is denied or permanently denied, use default location
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      // Log error (replace with proper logging in production)
      debugPrint('Error initializing location service: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.pick_location),
        actions: [
          IconButton(
            icon: Icon(Icons.check),
            onPressed: () {
              if (_selectedLocation != null) {
                Navigator.pop(context, _selectedLocation);
              }
            },
          ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : Stack(
              children: [
                GoogleMap(
                  initialCameraPosition: CameraPosition(
                    target: _selectedLocation ?? _initialLocation,
                    zoom: 15,
                  ),
                  onMapCreated: (controller) {
                    setState(() {
                      _mapController = controller;
                    });

                    // Center on the selected location when map is created
                    // Add a slight delay to ensure the map is fully loaded
                    if (_selectedLocation != null) {
                      Future.delayed(Duration(milliseconds: 300), () {
                        if (_mapController != null) {
                          _mapController!.animateCamera(
                            CameraUpdate.newLatLngZoom(_selectedLocation!, 15),
                          );
                        }
                      });
                    }
                  },
                  onTap: (location) {
                    setState(() {
                      _selectedLocation = location;
                    });

                    // Center on the newly selected location
                    if (_mapController != null) {
                      _mapController!.animateCamera(
                        CameraUpdate.newLatLng(location),
                      );
                    }
                  },
                  markers: _selectedLocation != null
                      ? {
                          Marker(
                            markerId: MarkerId('selected-location'),
                            position: _selectedLocation!,
                            icon: BitmapDescriptor.defaultMarker,
                          ),
                        }
                      : {},
                  myLocationEnabled: true,
                  myLocationButtonEnabled: false,
                  zoomControlsEnabled: false,
                ),

                // Add search box at the top
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  child: MapSearchBoxWidget(
                    mapController: _mapController,
                    itinerary: widget.itinerary,
                    onLocationSelected: (location, placeName) async {
                      setState(() {
                        _selectedLocation = location;
                      });
                      
                      // Log the map search event if we have an itinerary
                      if (widget.itinerary != null) {
                        try {
                          await AnalyticsService.instance.logMapSearch(
                            itinerary: widget.itinerary!,
                            searchQuery: placeName,
                            selectedPlaceName: placeName,
                          );
                        } catch (e) {
                          debugPrint('Error logging map search: $e');
                        }
                      }
                    },
                  ),
                ),

                // Add location button
                LocationButtonWidget(
                  mapController: _mapController,
                  bottom: 16,
                  right: 16,
                ),
              ],
            ),
    );
  }
}