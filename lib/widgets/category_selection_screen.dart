import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/utils/category_icons_utils.dart';
import 'package:roamr/l10n/app_localizations.dart';

class CategorySelectionScreen extends StatefulWidget {
  const CategorySelectionScreen({
    super.key,
    required this.selectedCategory,
  });

  final Category selectedCategory;

  @override
  State<CategorySelectionScreen> createState() => _CategorySelectionScreenState();
}

class _CategorySelectionScreenState extends State<CategorySelectionScreen> {
  late Category _selectedCategory;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  // Define category groups
  final Map<String, List<Category>> _categoryGroups = {
    'travel': [
      Category.flight,
      Category.accommodation,
      Category.transportation,
      Category.carRental,
    ],
    'food_drink': [
      Category.restaurant,
    ],
    'art_fun': [
      Category.activity,
      Category.sightseeing,
      Category.entertainment,
      Category.movie,
    ],
    'other': [
      Category.shopping,
      Category.parking,
      Category.note,
      Category.other,
    ],
  };

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(localizations.select_category),
      ),
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: colorScheme.primary.withAlpha(20),
        ),
        child: Column(
          children: [
            SizedBox(height: MediaQuery.of(context).padding.top + kToolbarHeight + 8),

            // Search bar
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: BackdropFilter(
                  filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
                  child: Container(
                    decoration: BoxDecoration(
                      color: theme.cardColor.withAlpha(150),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: theme.brightness == Brightness.dark
                            ? Colors.white.withAlpha(30)
                            : Colors.white.withAlpha(150),
                        width: 0.5,
                      ),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: localizations.search_activities_and_places,
                        prefixIcon: Icon(Icons.search, color: colorScheme.onSurface.withAlpha(150)),
                        filled: false,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        contentPadding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _searchQuery = value.toLowerCase();
                        });
                      },
                    ),
                  ),
                ),
              ),
            ),

            // Category grid
            Expanded(
              child: _searchQuery.isNotEmpty
                  ? _buildSearchResults()
                  : _buildCategoryGroups(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final filteredCategories = Category.values
        .where((category) => category != Category.all)
        .where((category) =>
            category.getName(context).toLowerCase().contains(_searchQuery))
        .toList();

    if (filteredCategories.isEmpty) {
      return Center(
        child: Text(
          AppLocalizations.of(context)!.no_results_found,
          style: theme.textTheme.titleMedium,
        ),
      );
    }

    // Create a single "Search Results" group
    return ListView(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.cardColor.withAlpha(150),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.brightness == Brightness.dark
                        ? Colors.white.withAlpha(30)
                        : Colors.white.withAlpha(150),
                    width: 0.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Group header
                    Text(
                      AppLocalizations.of(context)!.search_results,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // List of categories in this group
                    Wrap(
                      spacing: 16,
                      runSpacing: 16,
                      children: filteredCategories.map((category) {
                        final isSelected = category == _selectedCategory;

                        return GestureDetector(
                          onTap: () {
                            // Immediately return the selected category
                            Navigator.pop(context, category);
                          },
                          child: SizedBox(
                            width: 70,
                            child: Column(
                              children: [
                                // Category icon
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isSelected
                                        ? colorScheme.primary.withAlpha(50)
                                        : theme.brightness == Brightness.dark
                                            ? Colors.black.withAlpha(50)
                                            : Colors.white,
                                    border: Border.all(
                                      color: isSelected
                                          ? colorScheme.primary
                                          : colorScheme.onSurface.withAlpha(30),
                                      width: 1,
                                    ),
                                  ),
                                  child: Icon(
                                    IconUtils.getIconForCategory(category),
                                    color: isSelected
                                        ? colorScheme.primary
                                        : colorScheme.onSurface.withAlpha(180),
                                    size: 24,
                                  ),
                                ),

                                // Category name
                                Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    category.getName(context),
                                    style: textTheme.bodySmall?.copyWith(
                                      fontSize: 11,
                                      color: isSelected
                                          ? colorScheme.primary
                                          : colorScheme.onSurface,
                                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryGroups() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      itemCount: _categoryGroups.length,
      itemBuilder: (context, index) {
        final groupKey = _categoryGroups.keys.elementAt(index);
        final categories = _categoryGroups[groupKey]!;

        return Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: theme.cardColor.withAlpha(150),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.brightness == Brightness.dark
                        ? Colors.white.withAlpha(30)
                        : Colors.white.withAlpha(150),
                    width: 0.5,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Group header
                    Text(
                      _getCategoryGroupTitle(groupKey),
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.primary,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // List of categories in this group
                    Wrap(
                      spacing: 16,
                      runSpacing: 16,
                      children: categories.map((category) {
                        final isSelected = category == _selectedCategory;

                        return GestureDetector(
                          onTap: () {
                            // Immediately return the selected category
                            Navigator.pop(context, category);
                          },
                          child: SizedBox(
                            width: 70,
                            child: Column(
                              children: [
                                // Category icon
                                Container(
                                  width: 50,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: isSelected
                                        ? colorScheme.primary.withAlpha(50)
                                        : theme.brightness == Brightness.dark
                                            ? Colors.black.withAlpha(50)
                                            : Colors.white,
                                    border: Border.all(
                                      color: isSelected
                                          ? colorScheme.primary
                                          : colorScheme.onSurface.withAlpha(30),
                                      width: 1,
                                    ),
                                  ),
                                  child: Icon(
                                    IconUtils.getIconForCategory(category),
                                    color: isSelected
                                        ? colorScheme.primary
                                        : colorScheme.onSurface.withAlpha(180),
                                    size: 24,
                                  ),
                                ),

                                // Category name
                                Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Text(
                                    category.getName(context),
                                    style: textTheme.bodySmall?.copyWith(
                                      fontSize: 11,
                                      color: isSelected
                                          ? colorScheme.primary
                                          : colorScheme.onSurface,
                                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                    ),
                                    textAlign: TextAlign.center,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _getCategoryGroupTitle(String groupKey) {
    final localizations = AppLocalizations.of(context)!;

    switch (groupKey) {
      case 'travel':
        return localizations.travel;
      case 'food_drink':
        return localizations.food_drink;
      case 'art_fun':
        return localizations.art_fun;
      case 'other':
        return localizations.other;
      default:
        return '';
    }
  }
}
