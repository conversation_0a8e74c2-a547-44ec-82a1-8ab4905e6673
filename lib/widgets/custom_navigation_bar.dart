import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'dart:ui';

class NavItem {
  final IconData activeIcon;
  final IconData inactiveIcon;
  final String label;

  const NavItem({
    required this.activeIcon,
    required this.inactiveIcon,
    required this.label,
  });
}

class CustomNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<NavItem>? items;

  const CustomNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.items,
  });

  List<Widget> _buildItemsFromList(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    return List.generate(items!.length, (index) {
      final item = items![index];
      return _NavBarItem(
        icon: currentIndex == index ? item.activeIcon : item.inactiveIcon,
        label: item.label,
        isSelected: currentIndex == index,
        onTap: () => onTap(index),
        colorScheme: colorScheme,
      );
    });
  }

  List<Widget> _buildDefaultItems(
    BuildContext context,
    ColorScheme colorScheme,
  ) {
    final localizations = AppLocalizations.of(context)!;

    return [
      _NavBarItem(
        icon: currentIndex == 0 ? Icons.luggage : Icons.luggage_outlined,
        label: localizations.trips,
        isSelected: currentIndex == 0,
        onTap: () => onTap(0),
        colorScheme: colorScheme,
      ),
      _NavBarItem(
        icon: currentIndex == 1 ? Icons.bar_chart : Icons.bar_chart_outlined,
        label: localizations.statistics,
        isSelected: currentIndex == 1,
        onTap: () => onTap(1),
        colorScheme: colorScheme,
      ),
      _NavBarItem(
        icon: currentIndex == 2 ? Icons.bookmark : Icons.bookmark_border,
        label: localizations.saved,
        isSelected: currentIndex == 2,
        onTap: () => onTap(2),
        colorScheme: colorScheme,
      ),
      _NavBarItem(
        icon: currentIndex == 3 ? Icons.settings : Icons.settings_outlined,
        label: localizations.settings,
        isSelected: currentIndex == 3,
        onTap: () => onTap(3),
        colorScheme: colorScheme,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Get the number of items
    final int itemCount =
        items?.length ?? 4; // Default to 4 items if not provided

    // Check if we're on a tablet/iPad
    final double screenWidth = MediaQuery.of(context).size.width;
    final bool isTablet = screenWidth > 600; // iPad/tablet detection

    // Calculate width based on device type and number of items
    double navBarWidth;
    if (isTablet) {
      // For tablets, use a fixed width that's proportional to the screen
      navBarWidth = screenWidth * 0.4; // 40% of screen width for tablets
    } else {
      // For phones, calculate based on items but cap at screen width
      final double maxWidth = screenWidth - 32; // Full width minus padding
      final double itemWidth = 60; // Smaller width per item since no labels
      navBarWidth = itemCount * itemWidth < maxWidth - 40
          ? itemCount * itemWidth + 40 // Add some padding
          : maxWidth;
    }

    // Adaptive bottom navigation bar
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(30),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
            child: Container(
              height: 60,
              width: navBarWidth,
              decoration: BoxDecoration(
                color: theme.cardColor.withAlpha(200),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color:
                      theme.brightness == Brightness.dark
                          ? Colors.white.withAlpha(30)
                          : Colors.white.withAlpha(150),
                  width: 0.5,
                ),
                boxShadow: CustomTheme.getNavBarShadows(context),
              ),
              margin: const EdgeInsets.only(bottom: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children:
                    items != null
                        ? _buildItemsFromList(context, colorScheme)
                        : _buildDefaultItems(context, colorScheme),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class _NavBarItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final ColorScheme colorScheme;

  const _NavBarItem({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
    required this.colorScheme,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          customBorder: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
          child: Center(
            child: Icon(
              icon,
              color: isSelected
                  ? CustomTheme.getPrimaryIconColor(context)
                  : CustomTheme.getSecondaryIconColor(context),
              size: isSelected ? 32 : 24,
            ),
          ),
        ),
      ),
    );
  }
}
