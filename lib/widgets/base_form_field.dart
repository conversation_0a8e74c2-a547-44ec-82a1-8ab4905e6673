import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_state.dart';

class ItineraryForm<PERSON>ield extends StatelessWidget {
  final String label;
  final String? hint;
  final String? initialValue;
  final String? prefixText;
  final bool isRequired;
  final TextEditingController? controller;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final Function(String)? onChanged;
  final int? maxLines;
  final int? minLines;

  const ItineraryFormField({
    super.key,
    required this.label,
    this.hint,
    this.initialValue,
    this.prefixText,
    this.isRequired = false,
    this.controller,
    this.keyboardType,
    this.inputFormatters,
    this.onChanged,
    this.maxLines,
    this.minLines,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final state = context.watch<ItineraryFormBloc>().state;

    return TextFormField(
      controller: controller,
      initialValue: controller == null ? initialValue : null,
      style: textTheme.bodyLarge,
      onChanged: onChanged,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      maxLines: maxLines,
      minLines: minLines,
      validator: isRequired
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'This field is required';
              }
              return null;
            }
          : null,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            text: label,
            style: textTheme.bodyLarge,
            children: isRequired ? const [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ] : null,
          ),
        ),
        hintText: hint,
        hintStyle: TextStyle(
          color: theme.hintColor.withAlpha(128),
          fontWeight: FontWeight.w300,
        ),
        prefixText: prefixText,
        enabled: state.status.isLoaded,
        border: const UnderlineInputBorder(),
        floatingLabelBehavior: FloatingLabelBehavior.always,
      ),
    );
  }
}