import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/utils/date_utils.dart' as custom_date_utils;
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/utils/document_attachment_utils.dart';
import 'package:roamr/utils/file_utils.dart';

/// A full-screen widget for viewing attachments with swipe navigation
class AttachmentViewer extends StatefulWidget {
  /// List of all attachments to display (can be File or AttachmentModel)
  final List<dynamic> attachments;

  /// The initial index to display
  final int initialIndex;

  /// The title of the itinerary these attachments belong to
  final String itineraryTitle;

  /// Whether deletion is allowed in the viewer
  final bool allowDeletion;

  const AttachmentViewer({
    super.key,
    required this.attachments,
    required this.initialIndex,
    this.itineraryTitle = "", // Default value
    this.allowDeletion = false, // Default to not allowing deletion
  });

  @override
  State<AttachmentViewer> createState() => _AttachmentViewerState();
}

class _AttachmentViewerState extends State<AttachmentViewer> {
  late PageController _pageController;
  late int _currentIndex;
  late int _totalItems;

  // Combined list of all items (pending + saved)
  late List<dynamic> _allItems;

  @override
  void initState() {
    super.initState();

    // Set the initial index
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);

    // Use the provided attachments list
    _allItems = widget.attachments;
    _totalItems = _allItems.length;

    // Set system UI overlay style to ensure good visibility
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light.copyWith(
      statusBarColor: Colors.black54,
    ));

    // Set preferred orientations to allow rotation
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
  }

  @override
  void dispose() {
    // Reset to portrait orientation when widget is disposed
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    _pageController.dispose();
    super.dispose();
  }

  // Handle attachment deletion
  void _handleDelete(BuildContext context) {
    // Show confirmation dialog using the existing utility
    showDeletionConfirmationDialog(context).then((shouldDelete) {
      if (shouldDelete == true) {
        // Use a synchronous call to pop the navigator with the current index
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Navigator.of(context).pop(_currentIndex);
          }
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.black54,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: Text(
          '${_currentIndex + 1} / $_totalItems',
          style: const TextStyle(color: Colors.white),
        ),
        actions: [
          if (widget.allowDeletion)
            IconButton(
              icon: const Icon(Icons.delete_outline, color: Colors.white),
              onPressed: () => _handleDelete(context),
            ),
        ],
        centerTitle: true,
      ),
      body: PageView.builder(
        controller: _pageController,
        itemCount: _totalItems,
        onPageChanged: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        itemBuilder: (context, index) {
          final item = _allItems[index];
          return _buildImageViewer(item, context);
        },
      ),
    );
  }

  /// Builds a viewer for a file or attachment
  Widget _buildImageViewer(dynamic item, BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    // Handle File objects (pending attachments)
    if (item is File) {
      if (FileUtils.isDocumentFile(item.path)) {
        // Show document preview in the attachment viewer
        return _buildDocumentPreviewForFile(item, context);
      } else {
        // Show image viewer for image files
        return _buildImageViewerForFile(item, context);
      }
    }
    // Handle AttachmentModel objects (saved attachments)
    else if (item is AttachmentModel) {
      if (!item.exists) {
        return _buildFileNotFoundMessage(
          icon: Icons.image_not_supported,
          message: localizations.file_not_found,
        );
      }

      if (item.type == AttachmentType.document) {
        // Show document preview in the attachment viewer
        return _buildDocumentPreviewForFile(item.file, context);
      } else {
        // Show image viewer for photo attachments
        return _buildImageViewerForFile(item.file, context);
      }
    }

    // Fallback for unexpected types
    return Center(
      child: Text(
        localizations.unsupported_attachment_type,
        style: const TextStyle(color: Colors.white),
      ),
    );
  }

  /// Builds an image viewer for a file
  Widget _buildImageViewerForFile(File file, BuildContext context) {
    // Get the filename from the file path
    final fileName = file.path.split('/').last;
    String formattedDate = custom_date_utils.DateUtils.formatDate(DateTime.now());

    return Stack(
      children: [
        // Image viewer
        InteractiveViewer(
          minScale: 0.5,
          maxScale: 4.0,
          child: Center(
            child: Image.file(
              file,
              fit: BoxFit.contain,
              width: double.infinity,
              height: double.infinity,
              errorBuilder: (context, error, stackTrace) {
                return _buildFileNotFoundMessage(
                  icon: Icons.broken_image,
                  message: AppLocalizations.of(context)!.error_loading_image,
                );
              },
            ),
          ),
        ),

        // Entry information overlay at the top - positioned below app bar
        Positioned(
          top: 120, // Increased top position to avoid overlap with app bar
          left: 16,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.image,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      fileName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                formattedDate,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Builds a document viewer for a file
  Widget _buildDocumentPreviewForFile(File file, BuildContext context) {
    final fileName = file.path.split('/').last;
    final extension = FileUtils.getFileExtension(fileName).toLowerCase();
    String formattedDate = custom_date_utils.DateUtils.formatDate(DateTime.now());

    // For PDF files, use the PDF viewer
    if (extension == 'pdf') {
      return Stack(
        children: [
          // PDF viewer
          Positioned.fill(
            child: PDFView(
              filePath: file.path,
              enableSwipe: true,
              swipeHorizontal: true,
              autoSpacing: true,
              pageFling: true,
              pageSnap: true,
              defaultPage: 0,
              fitPolicy: FitPolicy.BOTH,
              preventLinkNavigation: false,
              onError: (error) {
                // Handle PDF loading errors silently
              },
              onPageError: (page, error) {
                // Handle page loading errors silently
              },
              onViewCreated: (PDFViewController pdfViewController) {
                // PDF view created
              },
              onPageChanged: (int? page, int? total) {
                // Page changed
              },
            ),
          ),

          // Entry information overlay at the top - positioned below app bar
          Positioned(
            top: 120, // Position below app bar
            left: 16,
            right: 16,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      DocumentAttachmentUtils.getFileIcon(fileName),
                      color: Colors.white,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        fileName,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  formattedDate,
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      );
    }

    // For text-based files, display the content
    return Stack(
      children: [
        // Document content area
        Center(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: const EdgeInsets.all(16),
            child: FutureBuilder<String>(
              future: _readDocumentContent(file, extension),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                } else if (snapshot.hasError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.error_outline, color: Colors.red, size: 48),
                        const SizedBox(height: 16),
                        Text(
                          '${AppLocalizations.of(context)!.error_loading_document}: ${snapshot.error}',
                          style: const TextStyle(color: Colors.white),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  );
                } else if (snapshot.hasData) {
                  // Display document content
                  return SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Document header
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.black54,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                DocumentAttachmentUtils.getFileIcon(fileName),
                                color: Colors.white,
                                size: 24,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  fileName,
                                  style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 16),

                        // Document content
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            snapshot.data!,
                            style: const TextStyle(color: Colors.black87, fontSize: 16),
                          ),
                        ),
                      ],
                    ),
                  );
                } else {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          DocumentAttachmentUtils.getFileIcon(fileName),
                          size: 100,
                          color: Colors.white70,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          fileName,
                          style: const TextStyle(color: Colors.white, fontSize: 20, fontWeight: FontWeight.bold),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          AppLocalizations.of(context)!.no_content_available,
                          style: const TextStyle(color: Colors.white70),
                        ),
                      ],
                    ),
                  );
                }
              },
            ),
          ),
        ),

        // Entry information overlay at the top - positioned below app bar
        Positioned(
          top: 120, // Increased top position to avoid overlap with app bar
          left: 16,
          right: 16,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    DocumentAttachmentUtils.getFileIcon(fileName),
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      fileName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                formattedDate,
                style: const TextStyle(color: Colors.white70, fontSize: 14),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Read document content as text
  Future<String> _readDocumentContent(File file, String extension) async {
    final context = this.context;
    try {
      // For text-based files, read the content directly
      if (extension == 'txt' || extension == 'md' || extension == 'json' || extension == 'csv') {
        return await file.readAsString();
      }

      // For other file types, return a message that we can't display the content directly
      switch (extension) {
        case 'doc':
        case 'docx':
          return '${AppLocalizations.of(context)!.word_document}. ${AppLocalizations.of(context)!.file_available_at}:\n${file.path}';
        case 'xls':
        case 'xlsx':
          return '${AppLocalizations.of(context)!.excel_spreadsheet}. ${AppLocalizations.of(context)!.file_available_at}:\n${file.path}';
        default:
          // Try to read as text anyway, but catch any errors
          try {
            return await file.readAsString();
          } catch (e) {
            return '${AppLocalizations.of(context)!.file_type_not_supported} ($extension). ${AppLocalizations.of(context)!.file_available_at}:\n${file.path}';
          }
      }
    } catch (e) {
      throw Exception('${AppLocalizations.of(context)!.error_loading_document}: $e');
    }
  }

  /// Builds a "file not found" message
  Widget _buildFileNotFoundMessage({
    required IconData icon,
    Color iconColor = Colors.white,
    required String message,
    String? title,
  }) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: iconColor, size: 48),
          const SizedBox(height: 16),
          if (title != null) ...[
            Text(
              title,
              style: const TextStyle(color: Colors.white, fontSize: 18),
            ),
            const SizedBox(height: 32),
          ],
          Text(
            message,
            style: const TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }
}
