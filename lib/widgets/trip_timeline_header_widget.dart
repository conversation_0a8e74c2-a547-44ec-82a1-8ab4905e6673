import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/pages/trip_attachments_screen.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/country_utils.dart';
import 'dart:ui';

class TripTimelineHeaderWidget extends StatelessWidget {
  const TripTimelineHeaderWidget({super.key, required this.trip});

  final Trip trip;

  /// Gets the total number of attachments for the trip
  Future<int> _getTripAttachmentCount(BuildContext context) async {
    final tripRepository = context.read<TripRepository>();
    final attachmentRepository = context.read<AttachmentRepository>();

    // Get all itineraries for the trip
    final itineraries = await tripRepository.getTripItinerariesFuture(trip.id);

    // Get all attachments for each itinerary and count them
    int totalAttachments = 0;
    for (final itinerary in itineraries) {
      final attachments = await attachmentRepository.getAttachmentsByItineraryId(itinerary.id);
      totalAttachments += attachments.length;
    }

    return totalAttachments;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;

    final state = context.watch<TripTimelineBloc>().state;

    final totalExpenses = CurrencyUtils.formatTotalExpenses(context, state.totalExpenses);

    final dateFormat = DateFormat('dd MMM yyyy');
    final dateRange =
        '${dateFormat.format(trip.startDate)} - ${dateFormat.format(trip.endDate)}';

    // Simple translucent container with blur effect
    return GestureDetector(
      onTap: () {
        // Navigate to the trip attachments screen when tapped
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => TripAttachmentsScreen(trip: trip),
          ),
        );
      },
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
          child: Container(
            margin: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor.withAlpha(150),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).brightness == Brightness.dark
                    ? Colors.white.withAlpha(30)
                    : Colors.white.withAlpha(150),
                width: 0.5,
              ),
            ),
            child: Stack(
              children: [
                // Main content
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(trip.title, style: textTheme.headlineSmall?.copyWith(
                                  fontWeight: FontWeight.bold,
                                )),
                                const SizedBox(height: 8),
                                Text(
                                  dateRange,
                                  style: textTheme.bodyMedium?.copyWith(
                                    color: CustomTheme.getSecondaryTextColor(context),
                                  ),
                                ),

                                const SizedBox(height: 12),
                                Text(
                                  totalExpenses,
                                  style: textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: colorScheme.secondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Country flag at the top right
                if (trip.countryCode != null && trip.countryCode!.isNotEmpty)
                  Positioned(
                    right: 8,
                    child: Text(
                      CountryUtils.countryCodeToEmoji(trip.countryCode),
                      style: const TextStyle(
                        fontSize: 48,
                      ),
                    ),
                  ),

                // Photo gallery indicator in the bottom-right corner with attachment count
                Positioned(
                  bottom: 8,
                  right: 8,
                  child: FutureBuilder<int>(
                    future: _getTripAttachmentCount(context),
                    builder: (context, snapshot) {
                      // Show loading indicator while counting
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: colorScheme.primary.withAlpha(204),
                            shape: BoxShape.circle,
                          ),
                          child: const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: Colors.white,
                            ),
                          ),
                        );
                      }

                      final attachmentCount = snapshot.data ?? 0;

                      // Only show if there are attachments
                      if (attachmentCount == 0) {
                        return const SizedBox.shrink();
                      }

                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: colorScheme.primary.withAlpha(204), // 0.8 opacity (204/255)
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.photo_library,
                              color: Colors.white,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              attachmentCount.toString(),
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
