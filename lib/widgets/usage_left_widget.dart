import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/models/user_usage.dart';
import 'package:roamr/services/revenue_cat_service.dart';

const int tripLimit = 1;
const int attachmentLimit = 5;
const int itineraryLimit = 10;

class UsageLeftWidget extends StatefulWidget {
  const UsageLeftWidget({super.key});

  @override
  State<UsageLeftWidget> createState() => _UsageLeftWidgetState();
}

class _UsageLeftWidgetState extends State<UsageLeftWidget> {
  bool _isPremium = false;

  @override
  void initState() {
    super.initState();
    _loadUsage();
  }

  Future<void> _loadUsage() async {
    final isPremium = await RevenueCatService().isSubscriptionActive();
    if (isPremium) {
      setState(() {
        _isPremium = true;
      });
      return;
    }
    await context.read<UserUsageCubit>().fetchUsage();
  }

  void _showUsageDialog(UserUsage? usage) {
    showDialog(
      context: context,
      builder: (context) => UsageDetailsDialog(
        usage: usage,
        isPremium: _isPremium,
      ),
    );
  }

  int _typesLeft(UserUsage? usage) {
    int left = 0;
    if ((usage?.tripsCreated ?? 0) < tripLimit) left++;
    if ((usage?.attachmentsCreated ?? 0) < attachmentLimit) left++;
    return left;
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    if (_isPremium) {
      return const SizedBox.shrink();
    }
    return BlocBuilder<UserUsageCubit, UserUsage?>(
      builder: (context, usage) {
        if (usage == null) {
          return const SizedBox.shrink();
        }
        final typesLeft = _typesLeft(usage);
        return Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: ActionChip(
            label: Text(
              '$typesLeft ${localizations.usage_left}',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary.withOpacity(0.1),
            side: BorderSide.none,
            onPressed: () => _showUsageDialog(usage),
          ),
        );
      },
    );
  }
}

class UsageDetailsDialog extends StatelessWidget {
  final UserUsage? usage;
  final bool isPremium;

  const UsageDetailsDialog({
    super.key,
    required this.usage,
    required this.isPremium,
  });

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return AlertDialog(
      title: Text(localizations.usage_details_title),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildUsageRow(
            context,
            label: localizations.trips,
            used: usage?.tripsCreated ?? 0,
            limit: tripLimit,
            isPremium: isPremium,
          ),
          const SizedBox(height: 16),
          _buildUsageRow(
            context,
            label: localizations.attachments,
            used: usage?.attachmentsCreated ?? 0,
            limit: attachmentLimit,
            isPremium: isPremium,
          ),
          const SizedBox(height: 16),
          _buildUsageRow(
            context,
            label: localizations.itineraries,
            used: usage?.itinerariesCreated ?? 0,
            limit: itineraryLimit,
            isPremium: isPremium,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(localizations.close),
        ),
        TextButton(
          onPressed: () async {
            await RevenueCatService().presentPaywall();
          },
          child: Text(localizations.upgrade),
        ),
      ],
    );
  }

  Widget _buildUsageRow(BuildContext context, {required String label, required int used, required int limit, required bool isPremium}) {
    final localizations = AppLocalizations.of(context)!;
    final left = limit - used;
    final percent = (used / limit).clamp(0.0, 1.0);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(label, style: const TextStyle(fontWeight: FontWeight.bold)),
            isPremium
                ? Text(localizations.unlimited, style: const TextStyle(color: Colors.green))
                : Text('${localizations.used}: $used / $limit'),
          ],
        ),
        if (!isPremium)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: LinearProgressIndicator(
              value: percent,
              minHeight: 8,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
            ),
          ),
        if (!isPremium)
          Padding(
            padding: const EdgeInsets.only(top: 4.0),
            child: Text('${left} ${localizations.left}', style: const TextStyle(fontSize: 12)),
          ),
      ],
    );
  }
} 