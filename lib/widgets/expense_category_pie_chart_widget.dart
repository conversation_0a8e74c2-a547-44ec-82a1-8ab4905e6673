import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class ExpenseCategoryPieChartWidget extends StatelessWidget {
  final List<TripItinerary?> itineraries;
  final double totalExpenses;
  final Widget? tripDropdown;

  const ExpenseCategoryPieChartWidget({
    super.key,
    required this.itineraries,
    required this.totalExpenses,
    this.tripDropdown,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context)!;

    // Process data for the chart
    final expensesByCategory = _processExpenseData(itineraries);

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
      color: theme.cardColor,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trip dropdown if provided
            if (tripDropdown != null) ...[
              tripDropdown!,
              const SizedBox(height: 16),
            ],
            Text(
              localizations.expenses_by_category,
              style: theme.textTheme.titleLarge?.copyWith(
                color: CustomTheme.getPrimaryTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${localizations.total}: ${CurrencyUtils.formatTotalExpenses(context, totalExpenses)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            expensesByCategory.isEmpty
                ? SizedBox(
                    height: 300,
                    child: Center(child: Text(localizations.no_expenses_to_display)),
                  )
                : Column(
                    children: [
                      // Pie chart
                      SizedBox(
                        height: 250,
                        child: Center(
                          child: PieChart(
                            PieChartData(
                              sectionsSpace: 2,
                              centerSpaceRadius: 40,
                              sections: _createPieSections(
                                context,
                                expensesByCategory,
                                colorScheme,
                              ),
                              pieTouchData: PieTouchData(
                                touchCallback: (FlTouchEvent event, pieTouchResponse) {
                                  // Handle touch events if needed
                                },
                              ),
                            ),
                          ),
                        ),
                      ),

                      // Category distribution title
                      Padding(
                        padding: const EdgeInsets.only(top: 16.0),
                        child: Text(
                          localizations.category_distribution,
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: CustomTheme.getPrimaryTextColor(context),
                          ),
                        ),
                      ),

                      // Category labels
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: _buildCategoryLabels(
                          context,
                          expensesByCategory,
                          colorScheme,
                        ),
                      ),
                    ],
                  ),
          ],
        ),
      ),
    );
  }

  Map<Category, double> _processExpenseData(List<TripItinerary?> itineraries) {
    // Group expenses by category
    final expensesByCategory = <Category, double>{};

    for (final itinerary in itineraries) {
      if (itinerary != null && itinerary.amount != null) {
        final category = itinerary.category;
        expensesByCategory[category] = (expensesByCategory[category] ?? 0) + itinerary.amount!;
      }
    }

    return expensesByCategory;
  }

  List<PieChartSectionData> _createPieSections(
    BuildContext context,
    Map<Category, double> expensesByCategory,
    ColorScheme colorScheme,
  ) {
    final sections = <PieChartSectionData>[];
    final totalAmount = expensesByCategory.values.fold(0.0, (sum, amount) => sum + amount);

    // Define colors for categories
    final categoryColors = _getCategoryColors(colorScheme);

    // Sort entries by amount (descending) for better visual representation
    final sortedEntries = expensesByCategory.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    for (final entry in sortedEntries) {
      final category = entry.key;
      final amount = entry.value;
      final percentage = totalAmount > 0 ? (amount / totalAmount) * 100 : 0;

      // Get color from map or use primary color as fallback
      final color = categoryColors[category] ?? colorScheme.primary;

      sections.add(
        PieChartSectionData(
          color: color,
          value: amount,
          title: '${percentage.toStringAsFixed(1)}%',
          radius: 100,
          titleStyle: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: _isColorDark(color) ? Colors.white : Colors.black,
          ),
          badgeWidget: percentage < 5 ? null : null, // No badge for small sections
          badgePositionPercentageOffset: 1.2,
        ),
      );
    }

    return sections;
  }

  Widget _buildCategoryLabels(
    BuildContext context,
    Map<Category, double> expensesByCategory,
    ColorScheme colorScheme,
  ) {
    final theme = Theme.of(context);
    final categoryColors = _getCategoryColors(colorScheme);

    // Sort categories by amount (descending)
    final sortedEntries = expensesByCategory.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: sortedEntries.map((entry) {
          final category = entry.key;
          final amount = entry.value;
          final color = categoryColors[category] ?? colorScheme.primary;

          return Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0),
            child: Column(
              children: [
                // Color indicator
                Container(
                  width: 16,
                  height: 16,
                  decoration: BoxDecoration(
                    color: color,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(height: 4),
                // Category name
                Text(
                  category.getName(context),
                  style: theme.textTheme.bodySmall,
                  textAlign: TextAlign.center,
                ),
                // Amount
                Text(
                  CurrencyUtils.formatTotalExpenses(context, amount),
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Map<Category, Color> _getCategoryColors(ColorScheme colorScheme) {
    return {
      Category.accommodation: const Color(0xFF4A6572),  // Blue-gray
      Category.restaurant: const Color(0xFFF9A825),     // Amber
      Category.transportation: const Color(0xFF43A047), // Green
      Category.activity: const Color(0xFF7B1FA2),       // Purple
      Category.shopping: const Color(0xFFE91E63),       // Pink
      Category.other: const Color(0xFF00897B),          // Teal
      Category.flight: const Color(0xFF1976D2),         // Blue
      Category.sightseeing: const Color(0xFFD84315),    // Deep Orange
      Category.entertainment: const Color(0xFF6D4C41),  // Brown
      Category.parking: const Color(0xFF546E7A),        // Blue-gray light
      Category.note: const Color(0xFF78909C),           // Blue-gray lighter
      Category.movie: const Color(0xFF8E24AA),          // Purple variant
      Category.carRental: const Color(0xFF0288D1),      // Light Blue
      // Fallback to primary color for any other categories
    };
  }

  bool _isColorDark(Color color) {
    // Calculate the perceived brightness of the color
    // Using the formula: (R * 299 + G * 587 + B * 114) / 1000
    final brightness = (color.r * 299 + color.g * 587 + color.b * 114) / 1000;
    return brightness < 128; // If less than 128, consider it dark
  }
}
