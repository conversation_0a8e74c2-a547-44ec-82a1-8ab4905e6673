import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/pages/travel_summary_details_screen.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'dart:ui';

class TripSummaryStatsWidget extends StatefulWidget {
  const TripSummaryStatsWidget({super.key});

  @override
  TripSummaryStatsWidgetState createState() => TripSummaryStatsWidgetState();
}

class TripSummaryStatsWidgetState extends State<TripSummaryStatsWidget> {
  // Statistics data
  int _totalTrips = 0;
  int _totalPlacesVisited = 0;
  double _avgPlacesPerTrip = 0;
  int _totalDaysTraveled = 0;
  int _totalFlights = 0;
  double _totalExpenses = 0;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadStatistics();
  }

  Future<void> _loadStatistics() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get all trips from the AddTripBloc
      final tripsState = context.read<AddTripBloc>().state;
      final trips = tripsState.trips;

      if (trips.isEmpty) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Calculate total trips
      _totalTrips = trips.length;

      // Calculate total places visited (itineraries)
      _totalPlacesVisited = 0;
      _totalFlights = 0;
      _totalExpenses = 0;
      int tripsProcessed = 0;

      for (final trip in trips) {
        final repository = context.read<TripRepository>();
        repository.getTripItineraries(trip.id).listen((itineraries) {
          setState(() {
            _totalPlacesVisited += itineraries.length;

            // Count flights and expenses
            for (final itinerary in itineraries) {
              // Count flights
              if (itinerary.category.name == 'flight') {
                _totalFlights++;
              }

              // Sum expenses
              if (itinerary.amount != null && itinerary.amount! > 0) {
                _totalExpenses += itinerary.amount!;
              }
            }

            // Calculate average places per trip
            _avgPlacesPerTrip = _totalTrips > 0 ? _totalPlacesVisited / _totalTrips : 0;

            // Mark as loaded when all trips are processed
            tripsProcessed++;
            if (tripsProcessed >= trips.length) {
              _isLoading = false;
            }
          });
        });
      }

      // Calculate total days traveled
      _totalDaysTraveled = 0;
      for (final trip in trips) {
        // Calculate days for this trip
        final tripDays = trip.endDate.difference(trip.startDate).inDays + 1;
        _totalDaysTraveled += tripDays;
      }

      setState(() {});
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> refresh() async {
    await _loadStatistics();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;
    final colorScheme = theme.colorScheme;

    return BlocListener<AddTripBloc, AddTripState>(
      listener: (context, state) {
        if (state.status == AddTripStatus.success) {
          _loadStatistics();
        }
      },
      child: Card(
        elevation: 4,
        margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
        color: theme.cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(16),
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title with View All button
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Title
                        Text(
                          localizations.trip_summary,
                          style: theme.textTheme.titleLarge?.copyWith(
                            color: CustomTheme.getPrimaryTextColor(context),
                          ),
                        ),
                        
                        // View All button
                        InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TravelSummaryDetailsScreen(),
                              ),
                            );
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              localizations.view_all,
                              style: theme.textTheme.bodySmall?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Colors.green,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Loading indicator or stats content
                  _isLoading
                      ? const Center(child: CircularProgressIndicator())
                      : _buildStatsContent(context, theme, colorScheme, localizations),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStatsContent(
    BuildContext context,
    ThemeData theme,
    ColorScheme colorScheme,
    AppLocalizations localizations,
  ) {
    if (_totalTrips == 0) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 32.0),
          child: Text(
            localizations.no_trips,
            style: theme.textTheme.titleMedium,
          ),
        ),
      );
    }

    // We're showing 6 stats in the summary (2 rows of 3 items)

    return Column(
      children: [
        // First row: Total trips, Total places, Total flights
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                Icons.card_travel,
                _totalTrips.toString(),
                localizations.total_trips,
                Colors.teal,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                context,
                Icons.place,
                _totalPlacesVisited.toString(),
                localizations.total_places_visited,
                Colors.orange,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                context,
                Icons.flight,
                _totalFlights.toString(),
                localizations.flights,
                Colors.purple,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),

        // Second row with 3 stats
        Row(
          children: [
            Expanded(
              child: _buildStatItem(
                context,
                Icons.analytics_outlined,
                _avgPlacesPerTrip.toStringAsFixed(1),
                localizations.avg_places_per_trip,
                Colors.blue,
              ),
            ),
            Expanded(
              child: _buildStatItem(
                context,
                Icons.calendar_month,
                _totalDaysTraveled.toString(),
                localizations.total_days_traveled,
                Colors.indigo,
              ),
            ),
            // Add expense stats
            Expanded(
              child: _buildStatItem(
                context,
                Icons.attach_money,
                CurrencyUtils.formatTotalExpenses(context, _totalExpenses),
                localizations.total_expenses,
                Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatItem(
    BuildContext context,
    IconData icon,
    String value,
    String label,
    Color color,
  ) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(8),
      margin: const EdgeInsets.symmetric(horizontal: 2),
      decoration: BoxDecoration(
        color: color.withAlpha(25),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            label,
            style: theme.textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
