import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:intl/intl.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_state.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/utils/category_icons_utils.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/utils/date_utils.dart' as custom_date_utils;
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/pages/add_edit_itinerary_screen.dart';
import 'package:roamr/pages/itinerary_details_screen.dart';

class TripTimelineItemTileWidget extends StatelessWidget {
  const TripTimelineItemTileWidget({
    super.key,
    this.trip,
    required this.itinerary,
    this.showDivider = true,
    this.isFavoriteScreen = false,
    this.enableSwipeActions = true,
  });

  final TripItinerary itinerary;
  final Trip? trip;
  final bool showDivider;
  final bool isFavoriteScreen;
  final bool enableSwipeActions;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final currency = NumberFormat.currency(
      symbol: CurrencyUtils.getCurrencySymbol(context),
      decimalDigits: 0,
    );
    final price =
        itinerary.amount != null && itinerary.amount! > 0
            ? currency.format(itinerary.amount)
            : null;

    // This is the container that will have the 3D effect

    return Slidable(
      key: ValueKey(itinerary.id),
      // Disable swipe actions if needed
      enabled: enableSwipeActions,
      // Choose the appropriate action pane based on whether this is a favorite item
      endActionPane: isFavoriteScreen
        // For favorites screen, only show remove from favorites option
        ? ActionPane(
            motion: const ScrollMotion(),
            extentRatio: 0.25,
            children: [
              SlidableAction(
                flex: 1,
                onPressed: (ctx) async {
                  final favoritesBloc = context.read<FavoritesBloc>();
                  final shouldRemove =
                      await showRemoveFromFavoritesDialog(ctx) ?? false;
                  if (shouldRemove) {
                    // This is the correct action for removing from favorites
                    favoritesBloc.add(RemoveFromFavorites(itinerary.id));
                  }
                },
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                label: AppLocalizations.of(context)!.remove,
                icon: Icons.bookmark_border,
              ),
            ],
          )
        // For regular itinerary items, show edit and delete options
        : ActionPane(
            motion: const ScrollMotion(),
            extentRatio: 0.5,
            dismissible: trip != null ? DismissiblePane(
              onDismissed: () {
                context.read<TripTimelineBloc>().add(
                  TripTimelineItemDeleted(itinerary: itinerary),
                );
              },
              closeOnCancel: true,
              confirmDismiss: () async {
                return await showDeletionConfirmationDialog(context) ?? false;
              },
            ) : null,
            children: [
              // Only show edit if we have a trip context
              if (trip != null)
                SlidableAction(
                  flex: 1,
                  onPressed: (_) => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => AddEditItineraryScreen(
                        tripId: trip!.id,
                        startDate: trip!.startDate,
                        endDate: trip!.endDate,
                        itinerary: itinerary,
                      ),
                    ),
                  ),
                  backgroundColor: colorScheme.primary,
                  foregroundColor: Colors.white,
                  label: AppLocalizations.of(context)!.edit,
                  icon: Icons.edit,
                ),
              SlidableAction(
                flex: 1,
                onPressed: (ctx) async {
                  if (isFavoriteScreen) {
                    // This is a favorite item - already handled by the dedicated action pane
                    // No need to duplicate the code here
                    return;
                  } else if (trip != null) {
                    // This is a regular itinerary item
                    final bloc = context.read<TripTimelineBloc>();
                    final shouldDelete =
                        await showDeletionConfirmationDialog(ctx) ?? false;
                    if (shouldDelete) {
                      bloc.add(TripTimelineItemDeleted(itinerary: itinerary));
                    }
                  }
                },
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                label: AppLocalizations.of(context)!.delete,
                icon: Icons.delete,
              ),
            ],
          ),
      child: Stack(
        children: [
          // Main content
          Container(
            margin: EdgeInsets.symmetric(vertical: 2.0, horizontal: 8.0), // Reduced vertical margin
            decoration: BoxDecoration(
              color: theme.cardColor.withAlpha(180),
              borderRadius: BorderRadius.circular(12),
              boxShadow: CustomTheme.getCardShadows(context),
            ),
            child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 6, // Reduced vertical padding
          ),
          minVerticalPadding: 0,
          visualDensity: VisualDensity.compact,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ItineraryDetailsScreen(
                  trip: trip,
                  itinerary: itinerary,
                  isFromFavorites: isFavoriteScreen,
                ),
              ),
            );
          },
          leading: Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.secondary.withAlpha(40),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withAlpha(30),
                  blurRadius: 2,
                  offset: const Offset(0, 1),
                ),
              ],
            ),
            child: Icon(
              IconUtils.getIconForCategory(itinerary.category),
              color: colorScheme.secondary,
              size: 22,
            ),
          ),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                itinerary.title,
                style: textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              _buildCategorySpecificInfo(context),
            ],
          ),
          trailing: price != null
            ? Text(
                price,
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.secondary,
                  fontWeight: FontWeight.bold,
                ),
              )
            : null,
        ),
      ),


      ],
    ),
  );
  }

  Widget _buildCategorySpecificInfo(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    Widget? additionalInfo;

    // Check category to determine what to display
    if (itinerary.category == Category.flight) {
      additionalInfo = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (itinerary.airlineName != null)
            Text(itinerary.airlineName!, style: textTheme.bodyMedium),
          if (itinerary.flightNumber != null) ...[
            const SizedBox(height: 4),
            Text(itinerary.flightNumber!, style: textTheme.bodySmall),
          ],
        ],
      );
    } else {
      additionalInfo = Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (itinerary.category == Category.accommodation &&
              itinerary.checkoutDate != null)
            Text(
              custom_date_utils.DateUtils.formatAccommodationDateRange(
                itinerary.date,
                itinerary.checkoutDate!,
                context: context,
              ),
              style: textTheme.bodySmall,
            ),
          if (itinerary.description != null &&
              itinerary.description!.trim().isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              itinerary.description!,
              style: textTheme.bodySmall,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          if (itinerary.locationText != null &&
              itinerary.locationText!.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              itinerary.locationText!,
              style: textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.outline.withAlpha(120),
              ),
            ),
          ],
        ],
      );
    }

    return additionalInfo;
  }
}
