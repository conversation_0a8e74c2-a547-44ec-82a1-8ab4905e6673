import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

import 'package:roamr/widgets/custom_navigation_bar.dart';

class ResponsiveNavigationLayout extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<Widget> children;

  const ResponsiveNavigationLayout({
    super.key,
    required this.currentIndex,
    required this.onTap,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    // Check if we're in landscape mode on a tablet
    final isLandscape = MediaQuery.of(context).orientation == Orientation.landscape;
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;

    // Only use side navigation if we're in landscape on a tablet
    if (isLandscape && isTablet) {
      // Get the scaffold background color from the theme
      final scaffoldBackgroundColor = Theme.of(context).scaffoldBackgroundColor;

      // Landscape layout with side navigation
      return Scaffold(
        backgroundColor: scaffoldBackgroundColor,
        body: Row(
          children: [
            // Side navigation bar
            SideNavigationBar(
              currentIndex: currentIndex,
              onTap: onTap,
            ),

            // Main content area with width constraint for iPad-like appearance
            Expanded(
              child: Center(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    maxWidth: 800, // Limit the width of the content area
                  ),
                  child: IndexedStack(
                    index: currentIndex,
                    children: children,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // Portrait layout with bottom navigation
      return Scaffold(
        extendBody: true, // Make the body extend behind the bottom navigation bar
        body: IndexedStack(
          index: currentIndex,
          children: children,
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: CustomNavigationBar(
            currentIndex: currentIndex,
            onTap: onTap,
          ),
        ),
      );
    }
  }
}

class SideNavigationBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;

  const SideNavigationBar({
    super.key,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    // Navigation items
    final navItems = [
      _NavItem(
        icon: currentIndex == 0 ? Icons.luggage : Icons.luggage_outlined,
        label: localizations.trips,
        isSelected: currentIndex == 0,
        onTap: () => onTap(0),
      ),
      _NavItem(
        icon: currentIndex == 1 ? Icons.bar_chart : Icons.bar_chart_outlined,
        label: localizations.statistics,
        isSelected: currentIndex == 1,
        onTap: () => onTap(1),
      ),
      _NavItem(
        icon: currentIndex == 2 ? Icons.bookmark : Icons.bookmark_border,
        label: localizations.saved,
        isSelected: currentIndex == 2,
        onTap: () => onTap(2),
      ),
      _NavItem(
        icon: currentIndex == 3 ? Icons.settings : Icons.settings_outlined,
        label: localizations.settings,
        isSelected: currentIndex == 3,
        onTap: () => onTap(3),
      ),
    ];

    return Container(
      width: 280, // Fixed width similar to iOS settings
      height: double.infinity, // Ensure it takes full height
      decoration: BoxDecoration(
        color: theme.scaffoldBackgroundColor, // Use theme colors instead of hardcoded values
        border: Border(
          right: BorderSide(
            color: theme.dividerColor.withAlpha(128), // Use theme divider color with half opacity
            width: 0.5,
          ),
        ),
      ),
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Add some padding at the top
            const SizedBox(height: 16),

            // App logo
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Image.asset(
                'assets/roamlogo.png',
                height: 40,
                fit: BoxFit.contain,
                alignment: Alignment.centerLeft,
              ),
            ),

            const SizedBox(height: 16),

            // Navigation items
            Expanded(
              child: ListView(
                padding: const EdgeInsets.only(top: 8),
                children: navItems,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _NavItem extends StatelessWidget {
  final IconData icon;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _NavItem({
    required this.icon,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          height: 44,
          margin: const EdgeInsets.symmetric(vertical: 2, horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: isSelected ? theme.colorScheme.primary.withAlpha(25) : Colors.transparent,
          ),
          child: Row(
            children: [
              // Icon
              Icon(
                icon,
                size: 22,
                color: isSelected
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurface,
              ),
              const SizedBox(width: 16),
              // Text
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 17,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
              // Chevron icon for iOS-like appearance
              if (isSelected)
                Icon(
                  Icons.chevron_right,
                  size: 20,
                  color: theme.colorScheme.onSurface.withAlpha(150),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
