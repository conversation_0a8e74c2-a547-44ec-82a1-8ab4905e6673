import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/models/trip.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

/// Special Trip instance to represent "All trips" option
final Trip kAllTripsOption = Trip(
  id: 'all_trips',
  title: 'All', // This will be replaced with localized text in the UI
  startDate: DateTime(2000, 1, 1), // Fixed date that doesn't change
  endDate: DateTime(2000, 1, 1),   // Fixed date that doesn't change
);

class TripDropdownWidget extends StatelessWidget {
  final Trip? selectedTrip;
  final Function(Trip?) onTripSelected;
  final bool showAllOption;

  const TripDropdownWidget({
    super.key,
    required this.selectedTrip,
    required this.onTripSelected,
    this.showAllOption = true,
  });

  // Helper method to get the localized text for the "All" option
  String _getAllText(AppLocalizations localizations) {
    return localizations.all;
  }

  List<DropdownMenuItem<Trip>> _buildDropdownItems(
    List<Trip> trips,
    AppLocalizations localizations,
    ThemeData theme,
  ) {
    final items = <DropdownMenuItem<Trip>>[];

    // Add "All" option if enabled
    if (showAllOption) {
      items.add(
        DropdownMenuItem<Trip>(
          value: kAllTripsOption,
          child: Text(
            _getAllText(localizations),
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      );
    }

    // Add all trips
    items.addAll(
      trips.map((trip) {
        return DropdownMenuItem<Trip>(
          value: trip,
          child: Text(
            trip.title,
            overflow: TextOverflow.ellipsis,
          ),
        );
      }),
    );

    return items;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final localizations = AppLocalizations.of(context)!;

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dropdown width based on screen size
        final isTablet = constraints.maxWidth > 600;
        final dropdownWidth = isTablet ? 400.0 : constraints.maxWidth - 32.0;

        return Center(
          child: BlocBuilder<AddTripBloc, AddTripState>(
            builder: (context, state) {
              if (state.status == AddTripStatus.loading) {
                return const Center(child: CircularProgressIndicator());
              }

              if (state.trips.isEmpty) {
                // Instead of just showing a message, return a dropdown with no options
                return SizedBox(
                  width: dropdownWidth,
                  child: DropdownButtonFormField<Trip>(
                    value: null,
                    decoration: InputDecoration(
                      labelText: localizations.select_trip,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: theme.cardColor,
                    ),
                    // Set dropdown menu's max height
                    menuMaxHeight: 400,
                    dropdownColor: theme.cardColor,
                    // Add custom dropdown button properties
                    icon: Icon(Icons.arrow_drop_down, color: theme.colorScheme.primary),
                    // Empty items list
                    items: [],
                    onChanged: null, // Disabled
                    disabledHint: Text(localizations.no_trips),
                  ),
                );
              }

              // Set initial selected trip if not already set
              if (selectedTrip == null) {
                WidgetsBinding.instance.addPostFrameCallback((_) {
                  if (showAllOption) {
                    // Select the "All" option by default
                    onTripSelected(kAllTripsOption);
                  } else if (state.trips.isNotEmpty) {
                    // Select the first trip if "All" option is not enabled
                    onTripSelected(state.trips.first);
                  }
                });
              }

              return SizedBox(
                width: dropdownWidth,
                child: DropdownButtonFormField<Trip>(
                  value: selectedTrip,
                  decoration: InputDecoration(
                    labelText: localizations.select_trip,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: theme.cardColor,
                  ),
                  // Set dropdown menu's max height
                  menuMaxHeight: 400,
                  dropdownColor: theme.cardColor,
                  // Add custom dropdown button properties
                  icon: Icon(Icons.arrow_drop_down, color: theme.colorScheme.primary),
                  // Create dropdown items
                  items: _buildDropdownItems(state.trips, localizations, theme),
                  onChanged: onTripSelected,
                ),
              );
            },
          ),
        );
      },
    );
  }
}
