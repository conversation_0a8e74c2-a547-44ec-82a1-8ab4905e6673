import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_state.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/widgets/trip_timeline_item_tile_widget.dart';
import 'package:roamr/widgets/loading_widget.dart';
import 'package:roamr/utils/date_utils.dart' as custom_date_utils;
import 'package:roamr/theme/custom_theme.dart';

class TripTimelineWidget extends StatefulWidget {
  final Trip trip;

  const TripTimelineWidget({super.key, required this.trip});

  @override
  State<TripTimelineWidget> createState() => _TripTimelineWidgetState();
}

class _TripTimelineWidgetState extends State<TripTimelineWidget> {
  bool _isReordering = false;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TripTimelineBloc, TripTimelineState>(
      builder: (context, state) {
        if (state.status == TripTimelineStatus.loading) {
          return const LoadingWidget(radius: 12, addPadding: true);
        }

        final actualItineraries = state.filteredItineraries.toList();

        // Generate a list of all dates in the trip range (normalized to remove time)
        final allDates = List<DateTime>.generate(
          widget.trip.endDate.difference(widget.trip.startDate).inDays + 1,
          (i) {
            final date = widget.trip.startDate.add(Duration(days: i));
            // Normalize to just date part (no time)
            return DateTime(date.year, date.month, date.day);
          },
        );

        // Create a map of existing itineraries by date
        final itinerariesByDate = <DateTime, List<TripItinerary?>>{};

        for (final itinerary in actualItineraries) {
          if (itinerary != null) {
            final date = DateTime(
              itinerary.date.year,
              itinerary.date.month,
              itinerary.date.day,
            );
            if (!itinerariesByDate.containsKey(date)) {
              // add a dummy itinerary for showing the date label
              itinerariesByDate[date] = [
                TripItinerary(
                  id: 'dummy_${date.toIso8601String()}',
                  title: '',
                  amount: null,
                  date: date,
                  category: Category.other,
                ),
              ];
            }
            itinerariesByDate[date]!.add(itinerary);
          }
        }

        // Create final list with dummy itineraries for empty dates
        final itineraries = <TripItinerary?>[];
        for (final date in allDates.reversed) {
          if (itinerariesByDate[date]?.isNotEmpty ?? false) {
            itineraries.addAll(itinerariesByDate[date]!);
          }
        }

        if (state.status == TripTimelineStatus.success && itineraries.isEmpty) {
          return const EmptyListWidget();
        }

        return ReorderableListView.builder(
          itemCount: itineraries.length,
          buildDefaultDragHandles: false,

          onReorderStart: (_) {
            setState(() {
              _isReordering = true;
            });
          },
          onReorderEnd: (_) {
            setState(() {
              _isReordering = false;
            });
          },
          onReorder: (oldIndex, newIndex) {
            final movedItinerary = itineraries[oldIndex];
            if (newIndex <= 0) return;
            final targetItinerary = itineraries[newIndex-1];
            if (movedItinerary != null && targetItinerary != null) {
              context.read<TripTimelineBloc>().add(
                TripTimelineReorder(
                  movedItinerary: movedItinerary,
                  targetItinerary: targetItinerary,
                ),
              );
            }
          },
          proxyDecorator: (child, index, animation) {
            return AnimatedBuilder(
              animation: animation,
              builder: (BuildContext context, Widget? child) {
                // Scale up the selected item slightly
                final elevationValue = animation.value * 8;
                final scaleValue = 1.0 + (animation.value * 0.05); // Scale up by 5%

                return Transform.scale(
                  scale: scaleValue,
                  child: Material(
                    color: Colors.transparent,
                    elevation: elevationValue,
                    shadowColor: Theme.of(context).colorScheme.shadow.withAlpha(75),
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          itemBuilder: (context, index) {
            final itinerary = itineraries[index];
            final currentDate = custom_date_utils.DateUtils.formatDate(
              itinerary!.date,
            );
            final itineraryDate = DateTime(
              itinerary.date.year,
              itinerary.date.month,
              itinerary.date.day,
            );
            bool showDateLabel =
                itinerary.id.startsWith('dummy_') &&
                ((itinerariesByDate[itineraryDate]?.length ?? 0) > 1 ||
                    _isReordering);

            bool showItinerary = !itinerary.id.startsWith('dummy_');

            return Column(
              key: ValueKey(itinerary.id),
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (showDateLabel)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: Container(
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary.withAlpha(40),
                        boxShadow: [
                          BoxShadow(
                            color: Theme.of(context).colorScheme.shadow.withAlpha(20),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 8.0,
                        horizontal: 16.0,
                      ),
                      child: Text(
                        currentDate,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: CustomTheme.getDateIndicatorTextColor(context),
                        ),
                      ),
                    ),
                  ),

                if (showItinerary)
                  ReorderableDelayedDragStartListener(
                    index: index,
                    child: TripTimelineItemTileWidget(
                      trip: widget.trip,
                      itinerary: itinerary,
                      showDivider: false,
                    ),
                  ),
              ],
            );
          },
        );
      },
    );
  }
}

class EmptyListWidget extends StatelessWidget {
  const EmptyListWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(
          AppLocalizations.of(context)!.add_place,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.bodyLarge,
        ),
      ),
    );
  }
}
