import 'package:flutter/material.dart';

/// A widget that provides a background gradient based on the app theme.
/// This widget should be used as a parent for screens that need to follow the app theme.
class ThemedBackground extends StatelessWidget {
  final Widget child;

  const ThemedBackground({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final color = theme.colorScheme.primary;
    final gradientStart = color.withAlpha(35);  // 30% opacity
    final gradientEnd = color.withAlpha(15);    // 10% opacity

    return Scaffold(
      backgroundColor: theme.scaffoldBackgroundColor,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [gradientStart, gradientEnd],
          ),
        ),
        child: child,
      ),
    );
  }

  /// Get the app bar color based on the theme
  static Color getAppBarColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary.withAlpha(76);  // 30% opacity
  }
} 