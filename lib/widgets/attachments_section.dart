import 'dart:io';
import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/utils/document_attachment_utils.dart';
import 'package:roamr/utils/file_utils.dart';
import 'package:roamr/utils/photo_attachment_utils.dart';
import 'package:roamr/widgets/attachment_viewer.dart';

/// A reusable widget for displaying attachments in both edit and view modes
class AttachmentsSection extends StatelessWidget {
  /// List of saved attachments to display
  final List<AttachmentModel> savedAttachments;

  /// List of pending attachments (only used in edit mode)
  final List<File> pendingAttachments;

  /// Title of the itinerary these attachments belong to
  final String itineraryTitle;

  /// Whether the section is in edit mode (with add/remove functionality)
  final bool isEditMode;

  /// Callback when a photo is added (only used in edit mode)
  final Function(File)? onAddPhoto;

  /// Callback when a document is added (only used in edit mode)
  final Function(File)? onAddDocument;

  /// Callback when a pending photo is removed (only used in edit mode)
  final Function(File)? onRemovePhoto;

  /// Callback when a pending document is removed (only used in edit mode)
  final Function(File)? onRemoveDocument;

  /// Callback when a saved attachment is removed (only used in edit mode)
  final Function(AttachmentModel)? onRemoveAttachment;

  const AttachmentsSection({
    super.key,
    required this.savedAttachments,
    this.pendingAttachments = const [],
    required this.itineraryTitle,
    this.isEditMode = false,
    this.onAddPhoto,
    this.onAddDocument,
    this.onRemovePhoto,
    this.onRemoveDocument,
    this.onRemoveAttachment,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    // Don't show the section if there are no attachments and we're not in edit mode
    if (!isEditMode && savedAttachments.isEmpty && pendingAttachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 5, sigmaY: 5),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: theme.cardColor.withAlpha(150),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.brightness == Brightness.dark
                  ? Colors.white.withAlpha(30)
                  : Colors.white.withAlpha(150),
              width: 0.5,
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section title with add buttons (if in edit mode)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    localizations.attachments,
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: colorScheme.primary,
                    ),
                  ),
                  if (isEditMode)
                    IconButton(
                      icon: const Icon(Icons.add_circle_outline),
                      onPressed: () => _showAttachmentOptions(context),
                      color: colorScheme.primary,
                    ),
                ],
              ),

              const SizedBox(height: 8),

              // Photo attachments list
              if (pendingAttachments.isEmpty && savedAttachments.isEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Center(
                    child: Text(
                      'No attachments added yet',
                      style: textTheme.bodyMedium?.copyWith(
                        color: Colors.grey,
                      ),
                    ),
                  ),
                )
              else
                SizedBox(
                  height: 100,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: [
                      // Pending photo attachments (only in edit mode)
                      ...pendingAttachments.map((file) => _buildPendingPhotoPreview(context, file)),

                      // Pending document attachments (only in edit mode)
                      ...pendingAttachments.map((file) => _buildPendingDocumentPreview(context, file)),

                      // Saved photo attachments
                      ...savedAttachments
                          .where((attachment) => attachment.type == AttachmentType.photo)
                          .map((attachment) => _buildSavedAttachmentPreview(context, attachment)),

                      // Saved document attachments
                      ...savedAttachments
                          .where((attachment) => attachment.type == AttachmentType.document)
                          .map((attachment) => _buildSavedAttachmentPreview(context, attachment)),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPendingPhotoPreview(BuildContext context, File file) {
    // Skip document files
    if (FileUtils.isDocumentFile(file.path)) {
      return const SizedBox.shrink();
    }

    // Verify file exists before displaying
    final fileExists = file.existsSync();

    return Stack(
      children: [
        GestureDetector(
          onTap: fileExists ? () => _viewPhoto(context, file) : null,
          child: Container(
            width: 80,
            height: 80,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[300],
              image: fileExists ? DecorationImage(
                image: FileImage(file),
                fit: BoxFit.cover,
              ) : null,
            ),
            child: !fileExists ? const Center(
              child: Icon(Icons.broken_image, color: Colors.grey),
            ) : null,
          ),
        ),
        if (isEditMode && onRemovePhoto != null)
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () => _confirmAndRemovePhoto(context, file),
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// Get a gradient for the document background based on file type
  LinearGradient _getDocumentGradient(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();

    // Different gradients for different file types
    switch (extension) {
      case 'pdf':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFE53935), Color(0xFFC62828)], // Red gradient for PDFs
        );
      case 'doc':
      case 'docx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF1565C0), Color(0xFF0D47A1)], // Blue gradient for Word docs
        );
      case 'xls':
      case 'xlsx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF2E7D32), Color(0xFF1B5E20)], // Green gradient for Excel
        );
      case 'ppt':
      case 'pptx':
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFFFF8F00), Color(0xFFEF6C00)], // Orange gradient for PowerPoint
        );
      default:
        return const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [Color(0xFF455A64), Color(0xFF263238)], // Grey gradient for other files
        );
    }
  }

  Widget _buildPendingDocumentPreview(BuildContext context, File file) {
    // Skip non-document files
    if (!FileUtils.isDocumentFile(file.path)) {
      return const SizedBox.shrink();
    }

    // Verify file exists before displaying
    final fileExists = file.existsSync();
    if (!fileExists) {
      return const SizedBox.shrink();
    }

    final fileName = file.path.split('/').last;
    IconData iconData = DocumentAttachmentUtils.getFileIcon(fileName);

    return Stack(
      children: [
        GestureDetector(
          onTap: () => _viewDocument(context, file),
          child: Container(
            width: 80,
            height: 80,
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.transparent,
            ),
            child: Stack(
              children: [
                // Document background
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      gradient: _getDocumentGradient(file.path),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(51),
                          blurRadius: 3,
                          offset: const Offset(1, 1),
                        ),
                      ],
                    ),
                  ),
                ),

                // Document content
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Document icon
                    Expanded(
                      flex: 3,
                      child: Center(
                        child: Icon(
                          iconData,
                          color: Colors.white,
                          size: 30,
                        ),
                      ),
                    ),

                    // Document filename
                    Expanded(
                      flex: 1,
                      child: Container(
                        alignment: Alignment.center,
                        padding: const EdgeInsets.symmetric(horizontal: 4),
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(153),
                          borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(4),
                            bottomRight: Radius.circular(4),
                          ),
                        ),
                        child: Text(
                          fileName,
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (isEditMode && onRemoveDocument != null)
          Positioned(
            top: 0,
            right: 0,
            child: GestureDetector(
              onTap: () => _confirmAndRemoveDocument(context, file),
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.close,
                  size: 16,
                  color: Colors.white,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildSavedAttachmentPreview(BuildContext context, AttachmentModel attachment) {
    // Verify file exists before displaying
    final fileExists = attachment.exists;
    if (!fileExists) {
      return const SizedBox.shrink();
    }

    // Handle different attachment types
    if (attachment.type == AttachmentType.document) {
      // Document preview
      final fileName = attachment.file.path.split('/').last;
      final iconData = DocumentAttachmentUtils.getFileIcon(fileName);

      return Stack(
        children: [
          GestureDetector(
            onTap: () => _viewSavedDocument(context, attachment),
            child: Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.transparent,
              ),
              child: Stack(
                children: [
                  // Document background
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        gradient: _getDocumentGradient(attachment.file.path),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 3,
                            offset: const Offset(1, 1),
                          ),
                        ],
                      ),
                    ),
                  ),

                  // Document content
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Document icon
                      Expanded(
                        flex: 3,
                        child: Center(
                          child: Icon(
                            iconData,
                            color: Colors.white,
                            size: 30,
                          ),
                        ),
                      ),

                      // Document filename
                      Expanded(
                        flex: 1,
                        child: Container(
                          alignment: Alignment.center,
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withAlpha(153),
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(4),
                              bottomRight: Radius.circular(4),
                            ),
                          ),
                          child: Text(
                            fileName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 8,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isEditMode && onRemoveAttachment != null)
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () => _confirmAndRemoveAttachment(context, attachment),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      );
    } else {
      // Photo preview
      return Stack(
        children: [
          GestureDetector(
            onTap: () => _viewSavedAttachment(context, attachment),
            child: Container(
              width: 80,
              height: 80,
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: Colors.grey[300],
                image: DecorationImage(
                  image: FileImage(attachment.file),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          ),
          if (isEditMode && onRemoveAttachment != null)
            Positioned(
              top: 0,
              right: 0,
              child: GestureDetector(
                onTap: () => _confirmAndRemoveAttachment(context, attachment),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
        ],
      );
    }
  }

  Future<void> _viewPhoto(BuildContext context, File file) async {
    final index = pendingAttachments.indexOf(file);
    if (index != -1) {
      await _openAttachmentViewer(context, true, index);
    }
  }

  Future<void> _viewDocument(BuildContext context, File file) async {
    final index = pendingAttachments.indexOf(file);
    if (index != -1) {
      await _openAttachmentViewer(context, true, index);
    }
  }

  Future<void> _viewSavedDocument(BuildContext context, AttachmentModel attachment) async {
    final index = savedAttachments.indexOf(attachment);
    if (index != -1) {
      await _openAttachmentViewer(context, false, index);
    }
  }

  Future<void> _viewSavedAttachment(BuildContext context, AttachmentModel attachment) async {
    final index = savedAttachments.indexOf(attachment);
    if (index != -1) {
      await _openAttachmentViewer(context, false, index);
    }
  }

  Future<void> _confirmAndRemovePhoto(BuildContext context, File file) async {
    final shouldDelete = await showDeletionConfirmationDialog(context);
    if (shouldDelete == true && onRemovePhoto != null) {
      onRemovePhoto!(file);
    }
  }

  Future<void> _confirmAndRemoveDocument(BuildContext context, File file) async {
    final shouldDelete = await showDeletionConfirmationDialog(context);
    if (shouldDelete == true && onRemoveDocument != null) {
      onRemoveDocument!(file);
    }
  }

  Future<void> _confirmAndRemoveAttachment(BuildContext context, AttachmentModel attachment) async {
    final shouldDelete = await showDeletionConfirmationDialog(context);
    if (shouldDelete == true && onRemoveAttachment != null) {
      onRemoveAttachment!(attachment);
    }
  }

  /// Shows a bottom sheet with attachment options
  Future<void> _showAttachmentOptions(BuildContext context) async {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final localizations = AppLocalizations.of(context)!;

    await showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: Text(
                  localizations.select_category,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListTile(
                leading: Icon(Icons.photo_library, color: colorScheme.primary),
                title: Text(localizations.gallery),
                onTap: () async {
                  Navigator.pop(context);
                  if (onAddPhoto != null) {
                    final file = await PhotoAttachmentUtils.pickImageFromGallery();
                    if (file != null && file.existsSync()) {
                      onAddPhoto!(file);
                    }
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.camera_alt, color: colorScheme.primary),
                title: Text(localizations.camera),
                onTap: () async {
                  Navigator.pop(context);
                  if (onAddPhoto != null) {
                    final file = await PhotoAttachmentUtils.pickImageFromCamera();
                    if (file != null && file.existsSync()) {
                      onAddPhoto!(file);
                    }
                  }
                },
              ),
              ListTile(
                leading: Icon(Icons.file_present, color: colorScheme.primary),
                title: Text(localizations.documents),
                onTap: () async {
                  Navigator.pop(context);
                  if (onAddDocument != null) {
                    final file = await DocumentAttachmentUtils.pickDocument();
                    if (file != null && file.existsSync()) {
                      onAddDocument!(file);
                    }
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _openAttachmentViewer(BuildContext context, bool isPending, int index) async {
    // Create a combined list of all attachments in the same order as they appear in the viewer
    List<dynamic> allAttachments = [...pendingAttachments, ...savedAttachments];

    // Calculate the correct initial index in the combined list
    int initialIndex = isPending ? index : pendingAttachments.length + index;

    final result = await Navigator.push<int>(
      context,
      MaterialPageRoute(
        builder: (builderContext) => AttachmentViewer(
          attachments: allAttachments,
          initialIndex: initialIndex,
          itineraryTitle: itineraryTitle,
          allowDeletion: isEditMode, // Only allow deletion in edit mode
        ),
      ),
    );

    // If an attachment was deleted in the viewer and we're in edit mode
    if (result != null && isEditMode) {
      // Get the item that was deleted from the combined list
      dynamic item = allAttachments[result];

      // Handle deletion based on the type of the item
      if (item is File) {
        // It's a pending attachment
        if (FileUtils.isDocumentFile(item.path)) {
          // It's a document
          if (onRemoveDocument != null) {
            onRemoveDocument!(item);
          }
        } else {
          // It's a photo
          if (onRemovePhoto != null) {
            onRemovePhoto!(item);
          }
        }
      } else if (item is AttachmentModel) {
        // It's a saved attachment
        if (onRemoveAttachment != null) {
          onRemoveAttachment!(item);
        }
      }
    }
  }
}
