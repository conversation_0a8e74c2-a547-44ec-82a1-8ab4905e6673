import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';

/// A custom date range picker using table_calendar with very distinct disabled dates
class TableCalendarDateRangePicker extends StatefulWidget {
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime? initialStartDate;
  final DateTime? initialEndDate;
  final Function(DateTime, DateTime) onDateRangeSelected;

  const TableCalendarDateRangePicker({
    Key? key,
    required this.firstDate,
    required this.lastDate,
    this.initialStartDate,
    this.initialEndDate,
    required this.onDateRangeSelected,
  }) : super(key: key);

  @override
  State<TableCalendarDateRangePicker> createState() => _TableCalendarDateRangePickerState();
}

class _TableCalendarDateRangePickerState extends State<TableCalendarDateRangePicker> {
  late DateTime _focusedDay;
  DateTime? _rangeStart;
  DateTime? _rangeEnd;
  RangeSelectionMode _rangeSelectionMode = RangeSelectionMode.toggledOn;

  @override
  void initState() {
    super.initState();
    _focusedDay = widget.initialStartDate ?? DateTime.now();
    _rangeStart = widget.initialStartDate;
    _rangeEnd = widget.initialEndDate;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final l10n = AppLocalizations.of(context)!;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      backgroundColor: theme.brightness == Brightness.dark
          ? Color.alphaBlend(theme.cardColor.withAlpha(230), Colors.black)
          : Color.alphaBlend(theme.cardColor.withAlpha(240), Colors.white),
      elevation: 8,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    l10n.select_date_range,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          TableCalendar(
            firstDay: DateTime(widget.firstDate.year - 1, 1, 1),
            lastDay: DateTime(widget.lastDate.year + 1, 12, 31),
            focusedDay: _focusedDay,
            rangeStartDay: _rangeStart,
            rangeEndDay: _rangeEnd,
            calendarFormat: CalendarFormat.month,
            rangeSelectionMode: _rangeSelectionMode,
            startingDayOfWeek: StartingDayOfWeek.monday,
            // Treat weekends the same as weekdays
            weekendDays: const [],
            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: theme.textTheme.titleMedium!.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            // Customize the calendar style
            calendarStyle: CalendarStyle(
              // Make the range colors match the app's theme
              rangeHighlightColor: colorScheme.primary.withAlpha(40),
              rangeStartDecoration: BoxDecoration(
                color: colorScheme.primary,
                shape: BoxShape.circle,
              ),
              rangeEndDecoration: BoxDecoration(
                color: colorScheme.primary,
                shape: BoxShape.circle,
              ),
              // Style for today's date - different for light and dark mode
              todayDecoration: BoxDecoration(
                color: Colors.transparent,
                shape: BoxShape.circle,
                border: Border.all(color: colorScheme.primary, width: 1.5),
              ),
              todayTextStyle: TextStyle(
                color: theme.brightness == Brightness.dark
                    ? Colors.white
                    : Colors.black,
                fontWeight: FontWeight.bold,
              ),
              // Style for days within the selected range
              withinRangeTextStyle: TextStyle(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.w500,
              ),
              // Make disabled dates visually distinct but with appropriate colors
              disabledTextStyle: TextStyle(
                color: theme.brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade400,
                decoration: TextDecoration.lineThrough,
                decorationColor: theme.brightness == Brightness.dark
                    ? Colors.grey.shade600
                    : Colors.grey.shade400,
                decorationThickness: 1.5,
              ),
              // Style for days outside the current month
              outsideTextStyle: TextStyle(
                color: theme.disabledColor,
              ),
              // Style for weekend days - same as weekdays
              weekendTextStyle: TextStyle(
                color: colorScheme.onSurface,
              ),
              // Ensure weekend days have the same decoration as weekdays
              defaultTextStyle: TextStyle(
                color: colorScheme.onSurface,
              ),
              // Style for selected days
              selectedTextStyle: TextStyle(
                color: colorScheme.onPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            // Enable only dates within the allowed range
            enabledDayPredicate: (day) {
              return !day.isBefore(widget.firstDate) && !day.isAfter(widget.lastDate);
            },
            // Handle day selection
            onDaySelected: (selectedDay, focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
                // Update range selection
                if (_rangeSelectionMode == RangeSelectionMode.toggledOn) {
                  if (_rangeStart == null || _rangeEnd != null) {
                    _rangeStart = selectedDay;
                    _rangeEnd = null;
                  } else if (selectedDay.isAfter(_rangeStart!)) {
                    _rangeEnd = selectedDay;
                  } else {
                    _rangeEnd = _rangeStart;
                    _rangeStart = selectedDay;
                  }
                }
              });
            },
            // Handle range selection
            onRangeSelected: (start, end, focusedDay) {
              setState(() {
                _focusedDay = focusedDay;
                _rangeStart = start;
                _rangeEnd = end;
              });
            },
            // Update focused day when page changes
            onPageChanged: (focusedDay) {
              _focusedDay = focusedDay;
            },
          ),
          const SizedBox(height: 16),
          // Display selected range
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    readOnly: true,
                    controller: TextEditingController(
                      text: _rangeStart != null
                          ? DateFormat.yMMMd().format(_rangeStart!)
                          : '',
                    ),
                    decoration: InputDecoration(
                      labelText: l10n.start_date,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextField(
                    readOnly: true,
                    controller: TextEditingController(
                      text: _rangeEnd != null
                          ? DateFormat.yMMMd().format(_rangeEnd!)
                          : '',
                    ),
                    decoration: InputDecoration(
                      labelText: l10n.end_date,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // Action buttons
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(l10n.cancel),
                ),
                const SizedBox(width: 16),
                ElevatedButton(
                  onPressed: (_rangeStart != null && _rangeEnd != null)
                      ? () {
                          widget.onDateRangeSelected(_rangeStart!, _rangeEnd!);
                          Navigator.of(context).pop();
                        }
                      : null,
                  child: Text(l10n.save),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// Shows a dialog with a custom date range picker
Future<DateTimeRange?> showTableCalendarDateRangePicker({
  required BuildContext context,
  required DateTime firstDate,
  required DateTime lastDate,
  DateTime? initialStartDate,
  DateTime? initialEndDate,
}) async {
  DateTimeRange? result;

  await showDialog(
    context: context,
    builder: (context) => TableCalendarDateRangePicker(
      firstDate: firstDate,
      lastDate: lastDate,
      initialStartDate: initialStartDate,
      initialEndDate: initialEndDate,
      onDateRangeSelected: (start, end) {
        // Normalize dates to remove time components
        final normalizedStart = DateTime(start.year, start.month, start.day);
        final normalizedEnd = DateTime(end.year, end.month, end.day);
        result = DateTimeRange(start: normalizedStart, end: normalizedEnd);
      },
    ),
  );

  return result;
}
