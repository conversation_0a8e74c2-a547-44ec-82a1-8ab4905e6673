import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:roamr/services/google_places_service.dart';
import 'package:roamr/models/place_prediction.dart';
import 'package:roamr/l10n/app_localizations.dart';

class CityPickerWidget extends StatefulWidget {
  final Function(String city, String countryCode, String countryName) onCitySelected;
  final String? initialCity;

  const CityPickerWidget({
    super.key,
    required this.onCitySelected,
    this.initialCity,
  });

  @override
  State<CityPickerWidget> createState() => _CityPickerWidgetState();
}

class _CityPickerWidgetState extends State<CityPickerWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<PlacePrediction> _predictions = [];
  bool _isSearching = false;
  String? _sessionToken;
  Timer? _debounce;
  bool _isInitialized = false;
  final GooglePlacesService _placesService = GooglePlacesService();

  @override
  void initState() {
    super.initState();
    _initializeApiKey();
    _sessionToken = const Uuid().v4();
    if (widget.initialCity != null) {
      _searchController.text = widget.initialCity!;
    }
  }

  Future<void> _initializeApiKey() async {
    setState(() {
      _isInitialized = true;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _autocomplete(String input) async {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      if (input.isEmpty) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
        return;
      }
      setState(() { _isSearching = true; });
      try {
        if (_predictions.isEmpty) {
          _sessionToken = const Uuid().v4();
        }
        final predictions = await _placesService.getAutocomplete(input, _sessionToken!, types: '(cities)');
        setState(() {
          _predictions = predictions;
          _isSearching = false;
        });
      } catch (e) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
      }
    });
  }

  Future<void> _selectPlace(String placeId, String description) async {
    try {
      final result = await _placesService.getPlaceDetails(placeId, _sessionToken!);
      if (result != null) {
        String cityName = result['name'] ?? description.split(',')[0].trim();
        String countryCode = '';
        String countryName = '';
        final addressComponents = result['address_components'] as List?;
        if (addressComponents != null) {
          for (var component in addressComponents) {
            final types = (component['types'] as List?)?.cast<String>() ?? [];
            if (types.contains('country')) {
              countryCode = component['short_name'] ?? '';
              countryName = component['long_name'] ?? '';
              break;
            }
          }
        }
        setState(() {
          _searchController.text = cityName;
          _predictions = [];
          _focusNode.unfocus();
        });
        widget.onCitySelected(cityName, countryCode, countryName);
        _sessionToken = const Uuid().v4();
      }
    } catch (e) {
      // Optionally handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context)!;

    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Search box
        TextField(
          controller: _searchController,
          focusNode: _focusNode,
          decoration: InputDecoration(
            labelText: localizations.city,
            hintText: localizations.city_hint,
            border: const OutlineInputBorder(),
            floatingLabelBehavior: FloatingLabelBehavior.always,
            prefixIcon: const Icon(Icons.location_city),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                        _predictions = [];
                      });
                      widget.onCitySelected('', '', '');
                    },
                  )
                : null,
            filled: true,
            fillColor: theme.cardColor.withAlpha(100),
          ),
          onChanged: (value) {
            _autocomplete(value);
          },
        ),

        // Predictions list
        if (_predictions.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: _predictions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: colorScheme.outline.withAlpha(50),
              ),
              itemBuilder: (context, index) {
                final prediction = _predictions[index];
                return ListTile(
                  dense: true,
                  leading: const Icon(Icons.location_city),
                  title: Text(
                    prediction.description,
                    style: theme.textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    if (prediction.placeId.isNotEmpty) {
                      _selectPlace(prediction.placeId, prediction.description);
                    }
                  },
                );
              },
            ),
          ),

        if (_isSearching)
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }
}
