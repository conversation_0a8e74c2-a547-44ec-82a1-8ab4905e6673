import 'package:flutter/material.dart';
import 'package:google_place/google_place.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:roamr/services/api_key_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class CityPickerWidget extends StatefulWidget {
  final Function(String city, String countryCode, String countryName) onCitySelected;
  final String? initialCity;

  const CityPickerWidget({
    super.key,
    required this.onCitySelected,
    this.initialCity,
  });

  @override
  State<CityPickerWidget> createState() => _CityPickerWidgetState();
}

class _CityPickerWidgetState extends State<CityPickerWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  late GooglePlace _googlePlace;
  List<AutocompletePrediction> _predictions = [];
  bool _isSearching = false;
  String? _sessionToken;
  Timer? _debounce;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeGooglePlace();
    // Generate a new session token
    _sessionToken = const Uuid().v4();

    // Set initial value if provided
    if (widget.initialCity != null) {
      _searchController.text = widget.initialCity!;
    }
  }

  Future<void> _initializeGooglePlace() async {
    // Get the API key from the API key service
    final apiKeyService = ApiKeyService();
    final apiKey = await apiKeyService.getGoogleMapsApiKey();

    // Initialize Google Place with the API key
    _googlePlace = GooglePlace(apiKey ?? '');
    setState(() {
      _isInitialized = true;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _autocomplete(String input) async {
    // Cancel any previous debounce timer
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    // Set a new debounce timer
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      if (input.isEmpty) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
        return;
      }

      setState(() {
        _isSearching = true;
      });

      try {
        // Generate a new session token for each new autocomplete session
        if (_predictions.isEmpty) {
          _sessionToken = const Uuid().v4();
        }

        // Use the cities type to restrict results to cities
        final result = await _googlePlace.autocomplete.get(
          input,
          sessionToken: _sessionToken,
          types: '(cities)', // Restrict to cities only
        );

        if (result != null && result.predictions != null) {
          setState(() {
            _predictions = result.predictions!;
            _isSearching = false;
          });
        } else {
          setState(() {
            _predictions = [];
            _isSearching = false;
          });
        }
      } catch (e) {
        debugPrint('Error in autocomplete: $e');
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
      }
    });
  }

  Future<void> _selectPlace(String placeId, String description) async {
    try {
      final result = await _googlePlace.details.get(
        placeId,
        sessionToken: _sessionToken,
        fields: 'address_component,name,formatted_address',
      );

      if (result != null && result.result != null) {
        String cityName = result.result!.name ?? description.split(',')[0].trim();
        String countryCode = '';
        String countryName = '';

        // Extract country information from address components
        if (result.result!.addressComponents != null) {
          for (var component in result.result!.addressComponents!) {
            if (component.types != null && component.types!.contains('country')) {
              countryCode = component.shortName ?? '';
              countryName = component.longName ?? '';
              break;
            }
          }
        }

        // Update the text and clear predictions
        setState(() {
          _searchController.text = cityName;
          _predictions = [];
          _focusNode.unfocus();
        });

        // Call the callback with the city and country information
        widget.onCitySelected(cityName, countryCode, countryName);

        // Generate a new session token for the next search
        _sessionToken = const Uuid().v4();
      }
    } catch (e) {
      debugPrint('Error getting place details: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (!_isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Search box
        TextField(
          controller: _searchController,
          focusNode: _focusNode,
          decoration: InputDecoration(
            labelText: 'City',
            hintText: 'Enter a city',
            border: const OutlineInputBorder(),
            floatingLabelBehavior: FloatingLabelBehavior.always,
            prefixIcon: const Icon(Icons.location_city),
            suffixIcon: _searchController.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      setState(() {
                        _searchController.clear();
                        _predictions = [];
                      });
                      widget.onCitySelected('', '', '');
                    },
                  )
                : null,
            filled: true,
            fillColor: theme.cardColor.withAlpha(100),
          ),
          onChanged: (value) {
            _autocomplete(value);
          },
        ),

        // Predictions list
        if (_predictions.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 4),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: _predictions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: colorScheme.outline.withAlpha(50),
              ),
              itemBuilder: (context, index) {
                final prediction = _predictions[index];
                return ListTile(
                  dense: true,
                  leading: const Icon(Icons.location_city),
                  title: Text(
                    prediction.description ?? '',
                    style: theme.textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    if (prediction.placeId != null) {
                      _selectPlace(prediction.placeId!, prediction.description ?? '');
                    }
                  },
                );
              },
            ),
          ),

        if (_isSearching)
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: CircularProgressIndicator(),
          ),
      ],
    );
  }
}
