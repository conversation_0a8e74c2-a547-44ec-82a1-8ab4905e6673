import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_places_flutter/google_places_flutter.dart';
import 'package:google_places_flutter/model/prediction.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:roamr/services/api_key_service.dart';
import 'package:roamr/models/trip_itinerary.dart';

class MapSearchBoxWidget extends StatefulWidget {
  final Function(LatLng location, String placeName) onLocationSelected;
  final GoogleMapController? mapController;
  final TripItinerary? itinerary;

  const MapSearchBoxWidget({
    super.key,
    required this.onLocationSelected,
    required this.mapController,
    this.itinerary,
  });

  @override
  State<MapSearchBoxWidget> createState() => _MapSearchBoxWidgetState();
}

class _MapSearchBoxWidgetState extends State<MapSearchBoxWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  String? _apiKey;
  List<Prediction> _predictions = [];
  bool _isSearching = false;
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _initializeApiKey();
  }

  Future<void> _initializeApiKey() async {
    // Get the API key from the API key service
    final apiKeyService = ApiKeyService();
    final apiKey = await apiKeyService.getGoogleMapsApiKey();
    setState(() {
      _apiKey = apiKey;
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _autocomplete(String input, BuildContext context) async {
    // Cancel any previous debounce timer
    if (_debounce?.isActive ?? false) _debounce!.cancel();

    // Set a new debounce timer
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      if (input.isEmpty) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
        return;
      }

      // Map search is now available for all users

      setState(() {
        _isSearching = true;
      });

      try {
        // Generate a new session token for each new autocomplete session
        if (_predictions.isEmpty) {
          _sessionToken = const Uuid().v4();
        }

        // Add component restrictions to limit results to a specific country if needed
        // You can customize this based on your app's requirements
        final result = await _googlePlace.autocomplete.get(
          input,
          sessionToken: _sessionToken,
          // Uncomment and modify if you want to restrict results to specific countries
          // components: [Component("country", "th")],
        );

        if (result != null && result.predictions != null) {
          debugPrint('Found ${result.predictions!.length} predictions');
          setState(() {
            _predictions = result.predictions!;
            _isSearching = false;
          });
        } else {
          debugPrint('No predictions found or result is null');
          setState(() {
            _predictions = [];
            _isSearching = false;
          });
        }
      } catch (e) {
        debugPrint('Error in autocomplete: $e');
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
      }
    });
  }

  Future<void> _selectPlace(String placeId) async {
    try {
      final result = await _googlePlace.details.get(
        placeId,
        sessionToken: _sessionToken,
        fields: 'geometry,name',
      );

      if (result != null &&
          result.result != null &&
          result.result!.geometry != null &&
          result.result!.geometry!.location != null) {

        final lat = result.result!.geometry!.location!.lat;
        final lng = result.result!.geometry!.location!.lng;

        if (lat != null && lng != null) {
          final location = LatLng(lat, lng);
          debugPrint('Selected location: $lat, $lng');

          // Move camera to the selected location
          if (widget.mapController != null) {
            widget.mapController!.animateCamera(
              CameraUpdate.newLatLngZoom(location, 15),
            );
          }

          final placeName = result.result!.name ?? '';
          
          // Call the callback to update the selected location with place name
          widget.onLocationSelected(location, placeName);

          // Update the text and clear predictions in a single operation
          setState(() {
            _searchController.text = placeName;
            _predictions = [];
            _focusNode.unfocus();
          });

          // Generate a new session token for the next search
          _sessionToken = const Uuid().v4();
        }
      } else {
        debugPrint('Place details result is null or incomplete');
      }
    } catch (e) {
      debugPrint('Error getting place details: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Search box
        Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.surface.withAlpha(230),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)!.location_hint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _predictions = [];
                        });
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              _autocomplete(value, context);
            },
          ),
        ),

        // Predictions list
        if (_predictions.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: colorScheme.surface.withAlpha(230),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: _predictions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: colorScheme.outline.withAlpha(50),
              ),
              itemBuilder: (context, index) {
                final prediction = _predictions[index];
                return ListTile(
                  dense: true,
                  leading: const Icon(Icons.location_on_outlined),
                  title: Text(
                    prediction.description ?? '',
                    style: theme.textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    if (prediction.placeId != null) {
                      _selectPlace(prediction.placeId!);
                    }
                  },
                );
              },
            ),
          ),

        // Loading indicator
        if (_isSearching)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.surface.withAlpha(230),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
