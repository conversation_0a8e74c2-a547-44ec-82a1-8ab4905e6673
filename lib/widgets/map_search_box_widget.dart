import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:roamr/services/google_places_service.dart';
import 'package:roamr/models/place_prediction.dart';
import 'package:roamr/models/trip_itinerary.dart';

class MapSearchBoxWidget extends StatefulWidget {
  final Function(LatLng location, String placeName) onLocationSelected;
  final GoogleMapController? mapController;
  final TripItinerary? itinerary;

  const MapSearchBoxWidget({
    super.key,
    required this.onLocationSelected,
    required this.mapController,
    this.itinerary,
  });

  @override
  State<MapSearchBoxWidget> createState() => _MapSearchBoxWidgetState();
}

class _MapSearchBoxWidgetState extends State<MapSearchBoxWidget> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  List<PlacePrediction> _predictions = [];
  bool _isSearching = false;
  String? _sessionToken;
  Timer? _debounce;
  final GooglePlacesService _placesService = GooglePlacesService();

  @override
  void initState() {
    super.initState();
    _sessionToken = const Uuid().v4();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _focusNode.dispose();
    _debounce?.cancel();
    super.dispose();
  }

  Future<void> _autocomplete(String input, BuildContext context) async {
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    _debounce = Timer(const Duration(milliseconds: 500), () async {
      if (input.isEmpty) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
        return;
      }
      setState(() { _isSearching = true; });
      try {
        if (_predictions.isEmpty) {
          _sessionToken = const Uuid().v4();
        }
        final predictions = await _placesService.getAutocomplete(input, _sessionToken!);
        setState(() {
          _predictions = predictions;
          _isSearching = false;
        });
      } catch (e) {
        setState(() {
          _predictions = [];
          _isSearching = false;
        });
      }
    });
  }

  Future<void> _selectPlace(String placeId) async {
    try {
      final result = await _placesService.getPlaceDetails(placeId, _sessionToken!);
      if (result != null) {
        final geometry = result['geometry'];
        final location = geometry != null ? geometry['location'] : null;
        final lat = location != null ? location['lat'] : null;
        final lng = location != null ? location['lng'] : null;
        if (lat != null && lng != null) {
          final LatLng latLng = LatLng(lat, lng);
          final placeName = result['name'] ?? '';
          if (widget.mapController != null) {
            widget.mapController!.animateCamera(
              CameraUpdate.newLatLngZoom(latLng, 15),
            );
          }
          widget.onLocationSelected(latLng, placeName);
          setState(() {
            _searchController.text = placeName;
            _predictions = [];
            _focusNode.unfocus();
          });
          _sessionToken = const Uuid().v4();
        }
      }
    } catch (e) {
      // Optionally handle error
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Search box
        Container(
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.surface.withAlpha(230),
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(25),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _searchController,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: AppLocalizations.of(context)!.location_hint,
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () {
                        setState(() {
                          _searchController.clear();
                          _predictions = [];
                        });
                      },
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
            onChanged: (value) {
              _autocomplete(value, context);
            },
          ),
        ),

        // Predictions list
        if (_predictions.isNotEmpty)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: colorScheme.surface.withAlpha(230),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(25),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ListView.separated(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: _predictions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: colorScheme.outline.withAlpha(50),
              ),
              itemBuilder: (context, index) {
                final prediction = _predictions[index];
                return ListTile(
                  dense: true,
                  leading: const Icon(Icons.location_on_outlined),
                  title: Text(
                    prediction.description,
                    style: theme.textTheme.bodyMedium,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  onTap: () {
                    if (prediction.placeId.isNotEmpty) {
                      _selectPlace(prediction.placeId);
                    }
                  },
                );
              },
            ),
          ),

        // Loading indicator
        if (_isSearching)
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.surface.withAlpha(230),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
          ),
      ],
    );
  }
}
