import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/theme/custom_theme.dart';
import 'package:roamr/utils/currency_utils.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:intl/intl.dart';

class ExpenseChartWidget extends StatelessWidget {
  final List<TripItinerary?> itineraries;
  final double totalExpenses;
  final Widget? tripDropdown;

  const ExpenseChartWidget({
    super.key,
    required this.itineraries,
    required this.totalExpenses,
    this.tripDropdown,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final localizations = AppLocalizations.of(context)!;

    // Process data for the chart
    final expensesByDate = _processExpenseData(itineraries);

    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
      color: theme.cardColor, // Use theme's card color for consistency
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Trip dropdown if provided
            if (tripDropdown != null) ...[
              tripDropdown!,
              const SizedBox(height: 16),
            ],
            Text(
              localizations.expense_over_time,
              style: theme.textTheme.titleLarge?.copyWith(
                color: CustomTheme.getPrimaryTextColor(context),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '${localizations.total}: ${CurrencyUtils.formatTotalExpenses(context, totalExpenses)}',
              style: theme.textTheme.titleMedium?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            expensesByDate.isEmpty
                ? SizedBox(
                    height: 300,
                    child: Center(child: Text(localizations.no_expenses_to_display)),
                  )
                : SizedBox(
                    height: 300,
                    child: SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      physics: const BouncingScrollPhysics(),
                      child: SizedBox(
                        // Make the chart wider for better horizontal scrolling experience
                        width: _calculateChartWidth(expensesByDate),
                        child: _buildExpenseChart(context, expensesByDate, colorScheme),
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  Map<DateTime, double> _processExpenseData(List<TripItinerary?> itineraries) {
    // Group expenses by date
    final expensesByDate = <DateTime, double>{};

    for (final itinerary in itineraries) {
      if (itinerary != null && itinerary.amount != null) {
        // Normalize date to remove time component
        final date = DateTime(
          itinerary.date.year,
          itinerary.date.month,
          itinerary.date.day,
        );

        expensesByDate[date] = (expensesByDate[date] ?? 0) + itinerary.amount!;
      }
    }

    // Sort dates
    final sortedDates = expensesByDate.keys.toList()..sort();

    // Create a new map with sorted dates
    final sortedExpensesByDate = <DateTime, double>{};
    for (final date in sortedDates) {
      sortedExpensesByDate[date] = expensesByDate[date]!;
    }

    return sortedExpensesByDate;
  }

  /// Calculate the width of the chart based on the number of data points
  /// Ensures there's enough space for each data point and some padding
  double _calculateChartWidth(Map<DateTime, double> expensesByDate) {
    // Base width for the chart
    const double baseWidth = 400.0;

    // If we have more than 5 data points, add extra width for each additional point
    if (expensesByDate.length > 5) {
      return baseWidth + (expensesByDate.length - 5) * 50.0;
    }

    return baseWidth;
  }

  Widget _buildExpenseChart(BuildContext context, Map<DateTime, double> expensesByDate, ColorScheme colorScheme) {
    final theme = Theme.of(context);
    // We already handle empty state in the build method, so we can assume data exists here

    // Convert map to list of FlSpot for the chart
    final spots = <FlSpot>[];
    final dates = expensesByDate.keys.toList()..sort();

    // Calculate x-axis values (days since start)
    final startDate = dates.first;
    for (int i = 0; i < dates.length; i++) {
      final date = dates[i];
      final daysSinceStart = date.difference(startDate).inDays.toDouble();
      spots.add(FlSpot(daysSinceStart, expensesByDate[date]!));
    }

    final dateFormat = DateFormat('MMM d');

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 100,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: colorScheme.outline.withAlpha(80),
              strokeWidth: 1,
              dashArray: [5, 5],
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: colorScheme.outline.withAlpha(80),
              strokeWidth: 1,
              dashArray: [5, 5],
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < dates.length) {
                  final date = dates[value.toInt()];
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(
                      dateFormat.format(date),
                      style: TextStyle(
                        color: colorScheme.onSurface.withAlpha(179),
                        fontWeight: FontWeight.bold,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 100,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(
                    color: colorScheme.onSurface.withAlpha(179),
                    fontWeight: FontWeight.bold,
                    fontSize: 10,
                  ),
                );
              },
              reservedSize: 42,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: colorScheme.outline.withAlpha(80), width: 1),
        ),
        // Use transparent background since we're already in a card
        backgroundColor: Colors.transparent,
        minX: 0,
        maxX: (dates.length - 1).toDouble(),
        minY: 0,
        maxY: expensesByDate.values.reduce((a, b) => a > b ? a : b) * 1.2,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: false,
            color: colorScheme.primary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                return FlDotCirclePainter(
                  radius: 5,
                  color: colorScheme.primary,
                  strokeWidth: 2,
                  strokeColor: theme.cardColor, // Use theme's card color for consistency
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              color: colorScheme.primary.withAlpha(40),
            ),
          ),
        ],
        lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData(
            tooltipBgColor: theme.cardColor.withAlpha(240), // Use theme's card color for consistency
            tooltipRoundedRadius: 12,
            tooltipPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            tooltipMargin: 8,
            getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
              return touchedBarSpots.map((barSpot) {
                final index = barSpot.x.toInt();
                if (index >= 0 && index < dates.length) {
                  final date = dates[index];
                  final amount = expensesByDate[date]!;
                  return LineTooltipItem(
                    '${dateFormat.format(date)}\n${NumberFormat.currency(symbol: CurrencyUtils.getCurrencySymbol(context), decimalDigits: 0).format(amount)}',
                    TextStyle(
                      color: colorScheme.onSurface,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }
                return null;
              }).toList();
            },
          ),
          handleBuiltInTouches: true,
        ),
      ),
    );
  }
}
