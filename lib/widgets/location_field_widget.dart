import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/constants/app_constants.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/utils/location_utils.dart';
import 'package:roamr/widgets/map_picker_widget.dart';

class LocationFieldWidget extends StatefulWidget {
  final TextEditingController locationController;
  final TextEditingController titleController;
  final Function(LatLng, String) onLocationChanged;
  final Function(String) onTitleChanged;
  final bool isRequired;
  final bool showError;
  final LatLng? currentLocation;

  const LocationFieldWidget({
    required this.locationController,
    required this.titleController,
    required this.onLocationChanged,
    required this.onTitleChanged,
    this.isRequired = false,
    this.showError = false,
    this.currentLocation,
    Key? key,
  }) : super(key: key);

  @override
  LocationFieldWidgetState createState() => LocationFieldWidgetState();
}

class LocationFieldWidgetState extends State<LocationFieldWidget> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final textTheme = theme.textTheme;

    return TextFormField(
      readOnly: true,
      onTap: () async {
        final LatLng? selectedLocation = await Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => MapPickerWidget(
            initialLocation: widget.currentLocation,
          )),
        );
        if (selectedLocation != null && mounted) {
          final locationDetails = await getLocationDetails(selectedLocation);
          final locationText = '${locationDetails[AppConstants.name] ?? ''}, ${locationDetails[AppConstants.address] ?? ''}';

          if (mounted) {
            setState(() {
              widget.locationController.text = locationText;
              // No longer automatically update the title field
            });

            widget.onLocationChanged(selectedLocation, locationText);
            // We're no longer calling onTitleChanged
          }
        }
      },
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            text: AppLocalizations.of(context)!.location,
            style: textTheme.bodyLarge,
            children: widget.isRequired ? const [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ] : null,
          ),
        ),
        hintText: AppLocalizations.of(context)!.location_hint,
        hintStyle: TextStyle(
          color: theme.hintColor.withAlpha(128),
          fontWeight: FontWeight.w300,
        ),
        border: UnderlineInputBorder(),
        floatingLabelBehavior: FloatingLabelBehavior.always,
        suffixIcon: widget.locationController.text.isNotEmpty
            ? IconButton(
                icon: Icon(Icons.clear),
                onPressed: () {
                  setState(() {
                    widget.locationController.clear();
                  });
                },
              )
            : null,
        errorText: (widget.isRequired && widget.showError && widget.locationController.text.isEmpty)
            ? 'Location is required'
            : null,
      ),
      style: textTheme.bodyLarge,
      controller: widget.locationController,
      validator: widget.isRequired
          ? (value) {
              if (value == null || value.isEmpty) {
                return 'Location is required';
              }
              return null;
            }
          : null,
      autovalidateMode: AutovalidateMode.onUserInteraction,
    );
  }
}
