import 'package:flutter/material.dart';

class AppLogo extends StatelessWidget {
  final double height;
  final Alignment alignment;
  final EdgeInsets padding;

  const AppLogo({
    super.key,
    this.height = 80,
    this.alignment = Alignment.centerLeft,
    this.padding = EdgeInsets.zero,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Align(
        alignment: alignment,
        child: Image.asset(
          'assets/plane.png',
          height: height,
        ),
      ),
    );
  }
} 