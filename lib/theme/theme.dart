import 'package:flutter/material.dart';

class AppTheme {
  // Define our color constants
  static const Color _secondary = Color(0xFF00BFA5);  // Teal green
  static const Color _lightPrimary = Color(0xFF007F5F);  // Dark green for light theme
  static const Color _darkPrimary = Color(0xFF005F46);   // Darker green for dark theme
  static const Color _tertiary = Color(0xFFFF6B6B);   // Coral red

  // Define accent colors for specific UI elements - made public for CustomTheme access
  static const Color darkGreen = Color(0xFF006D54);  // Darker green for selected text in light theme
  static const Color lightGreenAccent = Color(0xFF4CDFBC);  // Light green accent for dark theme

  // Light theme colors
  static const Color _lightBackground = Color(0xFFF0F2F5);  // Slightly darker background
  static const Color _lightSurface = Color(0xFFf8f8f8);     // Grey cards
  static const Color _lightPrimaryText = Colors.black;      // Primary text color for light theme
  static const Color _lightSecondaryText = Colors.grey;     // Secondary text color for light theme

  // Dark theme colors
  static const Color _darkBackground = Color(0xFF121212);   // Dark background
  static const Color _darkSurface = Color(0xFF2A2A2A);      // Grey cards
  static const Color _darkPrimaryText = Colors.white;       // Primary text color for dark theme
  static const Color _darkSecondaryText = Color(0xFFF0F2F5);  // Secondary text color for dark theme

  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: _lightPrimary,
        primary: _lightPrimary,
        secondary: _secondary,
        tertiary: _tertiary,
        brightness: Brightness.light,
      ).copyWith(
        surface: _lightSurface,
        surfaceContainer: _lightBackground,
      ),
      scaffoldBackgroundColor: _lightBackground,
      cardColor: _lightSurface,
      textTheme: TextTheme(
        // iOS-style large title for navigation bars
        headlineLarge: TextStyle(color: _lightPrimaryText, fontSize: 34, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        headlineMedium: TextStyle(color: _lightPrimaryText, fontSize: 28, fontWeight: FontWeight.bold),
        titleLarge: TextStyle(color: _lightPrimaryText, fontSize: 22, fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(color: _lightPrimaryText),
        bodyMedium: TextStyle(color: _lightSecondaryText),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: _lightPrimary,
        unselectedItemColor: Colors.grey[600],
        type: BottomNavigationBarType.fixed,
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          minimumSize: const Size(double.infinity, 48),
          backgroundColor: _lightPrimary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        selectedColor: _lightPrimary.withAlpha(38),
        side: BorderSide.none,
        labelStyle: const TextStyle(fontSize: 14),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        showCheckmark: false,
      ),
      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      dividerTheme: const DividerThemeData(
        space: 1,
        thickness: 1,
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      colorScheme: ColorScheme.fromSeed(
        seedColor: _darkPrimary,
        primary: _darkPrimary,
        secondary: _secondary,
        tertiary: _tertiary,
        brightness: Brightness.dark,
      ).copyWith(
        surface: _darkSurface,
        surfaceContainer: _darkBackground,
      ),
      scaffoldBackgroundColor: _darkBackground,
      cardColor: _darkSurface,
      textTheme: TextTheme(
        // iOS-style large title for navigation bars
        headlineLarge: TextStyle(color: _darkPrimaryText, fontSize: 34, fontWeight: FontWeight.bold, letterSpacing: -0.5),
        headlineMedium: TextStyle(color: _darkPrimaryText, fontSize: 28, fontWeight: FontWeight.bold),
        titleLarge: TextStyle(color: _darkPrimaryText, fontSize: 22, fontWeight: FontWeight.bold),
        bodyLarge: TextStyle(color: _darkPrimaryText),
        bodyMedium: TextStyle(color: _darkSecondaryText),
      ),
      bottomNavigationBarTheme: BottomNavigationBarThemeData(
        selectedItemColor: _darkPrimary,
        unselectedItemColor: Colors.grey[400],
        type: BottomNavigationBarType.fixed,
      ),
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          minimumSize: const Size(double.infinity, 48),
          backgroundColor: _darkPrimary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
            vertical: 12,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        selectedColor: _darkPrimary.withAlpha(76),
        side: BorderSide.none,
        labelStyle: const TextStyle(fontSize: 14),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        labelPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
        showCheckmark: false,
      ),
      cardTheme: CardThemeData(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      dividerTheme: const DividerThemeData(
        space: 1,
        thickness: 1,
      ),
    );
  }
}