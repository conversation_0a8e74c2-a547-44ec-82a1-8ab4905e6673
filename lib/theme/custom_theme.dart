import 'package:flutter/material.dart';
import 'package:roamr/theme/theme.dart';

/// Custom theme extensions for components that need specific styling
/// beyond what's available in the standard theme.
class CustomTheme {
  /// Get shadow colors appropriate for the current theme brightness
  static Color getShadowColor(BuildContext context, {double opacity = 0.3}) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.black.withAlpha((opacity * 255).round())
        : Colors.black.withAlpha((opacity * 0.7 * 255).round());
  }

  /// Get highlight colors appropriate for the current theme brightness
  static Color getHighlightColor(BuildContext context, {double opacity = 0.3}) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white.withAlpha((opacity * 255).round())
        : Colors.white.withAlpha((opacity * 255).round());
  }

  /// Get border colors appropriate for the current theme brightness
  static Color getBorderColor(BuildContext context, {double opacity = 0.2}) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white.withAlpha((opacity * 255).round())
        : Colors.black.withAlpha((opacity * 255).round());
  }

  /// Get card background color appropriate for the current theme
  static Color getCardBackgroundColor(BuildContext context) {
    return Theme.of(context).cardColor;
  }

  /// Get primary text color appropriate for the current theme
  static Color getPrimaryTextColor(BuildContext context) {
    return Theme.of(context).textTheme.bodyLarge?.color ??
        (Theme.of(context).brightness == Brightness.dark ? Colors.white : Colors.grey);
  }

  /// Get secondary text color appropriate for the current theme
  static Color getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400] ?? Colors.white70
        : Colors.grey[600] ?? Colors.black54;
  }

  /// Get primary text color appropriate for the current theme
  static Color getPrimaryIconColor(BuildContext context) {
    return Theme.of(context).colorScheme.primary;
  }

  /// Get secondary text color appropriate for the current theme
  static Color getSecondaryIconColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.grey[400] ?? Colors.white70
        : Colors.grey[600] ?? Colors.black54;
  }

  /// Get shadow configuration for translucent bottom navigation bar
  static List<BoxShadow> getNavBarShadows(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return [
      // Subtle shadow for depth
      BoxShadow(
        color: Colors.black.withAlpha(isDark ? 50 : 30),
        blurRadius: 6,
        spreadRadius: 0,
        offset: const Offset(0, 3),
      ),
    ];
  }

  /// Get 3D shadow configuration for cards
  static List<BoxShadow> getCardShadows(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return [
      // Bottom shadow - creates depth
      BoxShadow(
        color: Colors.black.withAlpha(isDark ? 120 : 40),
        blurRadius: 4,
        spreadRadius: 0,
        offset: const Offset(0, 2),
      ),
      // Top highlight - enhances 3D effect
      BoxShadow(
        color: isDark
            ? Colors.white.withAlpha(10)
            : Colors.white,
        blurRadius: 0,
        spreadRadius: 0,
        offset: const Offset(0, 1),
      ),
    ];
  }

  /// Get selected chip text color appropriate for the current theme
  static Color getSelectedChipTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? AppTheme.darkGreen // Use the theme's dark green for light theme
        : Theme.of(context).colorScheme.secondary.withAlpha(200); // Lighter for dark theme
  }

  /// Get date indicator text color appropriate for the current theme
  static Color getDateIndicatorTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.light
        ? AppTheme.darkGreen // Use the theme's dark green for light theme
        : Theme.of(context).colorScheme.secondary; // Full brightness for dark theme
  }
}
