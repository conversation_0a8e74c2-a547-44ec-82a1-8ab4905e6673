import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/currency_bloc/currency_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/blocs/purchase_bloc/purchase_bloc.dart';
import 'package:roamr/blocs/theme_bloc/theme_bloc.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/data/settings_preference.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/di/service_locator.dart';
import 'package:roamr/firebase/analytics_service.dart';
import 'package:roamr/firebase/route_observer.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:roamr/pages/favorites_screen.dart';
import 'package:roamr/pages/home_screen.dart';
import 'package:roamr/pages/onboarding/onboarding_screen.dart';
import 'package:roamr/pages/settings_screen.dart';
import 'package:roamr/pages/statistics_screen.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/theme/theme.dart';
import 'package:roamr/widgets/responsive_navigation_layout.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'blocs/language_bloc/language_bloc.dart';
import 'blocs/navigation_bloc.dart';
import 'package:roamr/services/notification_service.dart';

class TripPlannerApp extends StatefulWidget {
  const TripPlannerApp({super.key});

  @override
  State<TripPlannerApp> createState() => _TripPlannerAppState();
}

class _TripPlannerAppState extends State<TripPlannerApp> {
  final GlobalKey<NavigatorState> _navigatorKey = GlobalKey<NavigatorState>();
  final FirebaseRouteObserver _routeObserver = FirebaseRouteObserver();
  bool _onboardingCompleted = false;
  bool _isLoading = true;
  StreamSubscription<AuthState>? _authStateSubscription;
  bool _notificationScheduled = false;

  @override
  void initState() {
    super.initState();
    // Initialize app lifecycle tracking
    _setupAppLifecycleTracking();
    // Initialize notification service
    NotificationService.instance.initialize();
    // Check if onboarding has been completed
    _checkOnboardingStatus();
    // Listen for Supabase auth state changes
    _authStateSubscription = Supabase.instance.client.auth.onAuthStateChange.listen((event) {
      if (mounted) {
        setState(() {});
      }
    });
  }

  void _setupAppLifecycleTracking() {
    // Track app open
    AnalyticsService.instance.logAppOpen();
    // Track app lifecycle changes
    SystemChannels.lifecycle.setMessageHandler((msg) async {
      if (msg != null) {
        if (msg == AppLifecycleState.paused.toString()) {
          AnalyticsService.instance.logAppBackground();
        } else if (msg == AppLifecycleState.resumed.toString()) {
          AnalyticsService.instance.logAppOpen();
        }
      }
      return null;
    });
  }

  @override
  void dispose() {
    _authStateSubscription?.cancel();
    _isLoading = false;
    super.dispose();
  }

  void _handleOnboardingCompleted() {
    if (mounted) {
      setState(() {
        _onboardingCompleted = true;
      });
    }
  }

  Widget _buildHome() {
    if (_isLoading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (!_onboardingCompleted) {
      return OnboardingScreen(
        key: const ValueKey('onboarding_screen'),
        onOnboardingCompleted: _handleOnboardingCompleted,
        startAtSignIn: false,
      );
    }
    final isAuthenticated = Supabase.instance.client.auth.currentUser != null;
    if (!isAuthenticated) {
      return OnboardingScreen(
        key: const ValueKey('onboarding_signin'),
        onOnboardingCompleted: _handleOnboardingCompleted,
        startAtSignIn: true,
      );
    }

    return BlocBuilder<NavigationBloc, int>(
      builder: (context, currentIndex) {
        return ResponsiveNavigationLayout(
          currentIndex: currentIndex,
          onTap: (index) {
            if (index == 0) {
              context.read<NavigationBloc>().add(NavigationEvent.trips);
            } else if (index == 1) {
              context.read<NavigationBloc>().add(NavigationEvent.statistics);
            } else if (index == 2) {
              context.read<NavigationBloc>().add(NavigationEvent.saved);
            } else if (index == 3) {
              context.read<NavigationBloc>().add(NavigationEvent.settings);
            }
          },
          children: const [
            HomeScreen(),
            StatisticsScreen(),
            FavoritesScreen(),
            SettingsScreen(),
          ],
        );
      },
    );
  }

  Future<void> _checkOnboardingStatus() async {
    final onboardingCompleted = await SettingsPreferences.isOnboardingCompleted();
    if (mounted) {
      setState(() {
        _onboardingCompleted = onboardingCompleted;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final tripDatabase = TripDatabase();
    final tripRepository = TripRepository(tripDatabase: tripDatabase);
    final attachmentRepository = AttachmentRepositoryImpl(tripDatabase);
    final revenueCatService = RevenueCatService();

    // Create MaterialApp with route observer
    Widget _buildMaterialApp(ThemeMode themeMode, Locale locale) {
      return MaterialApp(
        title: 'RoamR',
        debugShowCheckedModeBanner: false,
        navigatorKey: _navigatorKey,
        navigatorObservers: [_routeObserver],
        themeMode: themeMode,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        restorationScopeId: 'app',
        localizationsDelegates: const [
          AppLocalizations.delegate,
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        locale: locale,
        supportedLocales: const [
          Locale('en', ''), // English
          Locale('es', ''), // Spanish
          Locale('hi', ''), // Hindi
          Locale('de', ''), // German
          Locale('fr', ''), // French
          Locale('th', ''), // Thai
        ],
        onGenerateRoute: (settings) {
          // Handle deep links here if needed
          return MaterialPageRoute(builder: (context) => _buildHome());
        },
        home: _buildHome(),
        builder: (context, child) {
          if (!_notificationScheduled) {
            _notificationScheduled = true;
            final tripRepository = getIt<TripRepository>();
            NotificationService.instance.scheduleInactivityNotification(
              context: context,
              tripRepository: tripRepository,
              delay: const Duration(hours: 4),
            );
          }
          return child!;
        },
      );
    }

    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<TripRepository>.value(value: tripRepository),
        RepositoryProvider<AttachmentRepository>.value(
          value: attachmentRepository,
        ),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider<UserUsageCubit>.value(
            value: getIt<UserUsageCubit>(),
          ),
          BlocProvider(create: (_) => ThemeBloc()),
          BlocProvider(create: (_) => NavigationBloc()),
          BlocProvider(
            create: (_) {
              final cubit = PurchaseCubit(revenueCatService: revenueCatService);
              cubit.initialize();
              return cubit;
            },
          ),
          BlocProvider(
            create:
                (_) =>
                    AddTripBloc(tripRepository: tripRepository)
                      ..add(LoadTrips()),
          ),
          BlocProvider(
            create:
                (_) =>
                    FavoritesBloc(tripRepository: tripRepository)
                      ..add(const LoadFavorites()),
          ),
          BlocProvider(create: (_) => LanguageBloc()),
          BlocProvider(create: (_) => CurrencyCubit()),
        ],
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, state) {
            return _buildMaterialApp(
              state.themeMode,
              context.watch<LanguageBloc>().state.locale,
            );
          },
        ),
      ),
    );
  }
}
