import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/widgets/responsive_navigation_layout.dart';
import 'blocs/navigation_bloc.dart';
import 'package:roamr/pages/home_screen.dart';
import 'package:roamr/pages/favorites_screen.dart';
import 'package:roamr/pages/statistics_screen.dart';
import 'package:roamr/pages/settings_screen.dart';
import 'package:roamr/theme/theme.dart';
import 'package:roamr/blocs/theme_bloc/theme_bloc.dart';
import 'blocs/language_bloc/language_bloc.dart';
import 'package:roamr/blocs/currency_bloc/currency_bloc.dart';
import 'package:roamr/blocs/purchase_bloc/purchase_bloc.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';


class TripPlannerApp extends StatelessWidget {
  const TripPlannerApp({super.key});

  @override
  Widget build(BuildContext context) {
    final tripDatabase = TripDatabase();
    final tripRepository = TripRepository(tripDatabase: tripDatabase);
    final attachmentRepository = AttachmentRepositoryImpl(tripDatabase);

    // Get the already initialized RevenueCat service
    final revenueCatService = RevenueCatService();

    return MultiRepositoryProvider(
      providers: [
        RepositoryProvider<TripRepository>.value(value: tripRepository),
        RepositoryProvider<AttachmentRepository>.value(value: attachmentRepository),
      ],
      child: MultiBlocProvider(
        providers: [
          BlocProvider(create: (_) => ThemeBloc()),
          BlocProvider(create: (_) => NavigationBloc()),
          BlocProvider(create: (_) {
            // RevenueCat is already initialized in main.dart
            final cubit = PurchaseCubit(revenueCatService: revenueCatService);
            // Load products and check subscription status
            cubit.initialize();
            return cubit;
          }),
          BlocProvider(
            create:
                (_) => AddTripBloc(
                  tripRepository: tripRepository,
                )..add(LoadTrips()),
          ),
          BlocProvider(
            create: (_) => FavoritesBloc(
              tripRepository: tripRepository,
            )..add(const LoadFavorites()),
          ),
          BlocProvider(create: (_) => LanguageBloc()),
          BlocProvider(create: (_) => CurrencyCubit()),
        ],
        child: BlocBuilder<ThemeBloc, ThemeState>(
          builder: (context, state) {
            return MaterialApp(
              title: 'RoamR',
              debugShowCheckedModeBanner: false,
              themeMode: state.themeMode,
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              localizationsDelegates: [
                AppLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              locale: context.watch<LanguageBloc>().state.locale,
              supportedLocales: [
                const Locale('en', ''), // English
                const Locale('es', ''), // Spanish
                const Locale('hi', ''), // Hindi
                const Locale('th', ''), // Thai
                const Locale('de', ''), // German
                const Locale('fr', ''), // French
              ],
              home: BlocBuilder<NavigationBloc, int>(
                builder: (context, currentIndex) {
                  return ResponsiveNavigationLayout(
                    currentIndex: currentIndex,
                    onTap: (index) {
                      // Direct navigation based on index
                      if (index == 0) {
                        context.read<NavigationBloc>().add(NavigationEvent.trips);
                      } else if (index == 1) {
                        context.read<NavigationBloc>().add(NavigationEvent.statistics);
                      } else if (index == 2) {
                        context.read<NavigationBloc>().add(NavigationEvent.saved);
                      } else if (index == 3) {
                        context.read<NavigationBloc>().add(NavigationEvent.settings);
                      }
                    },
                    children: [
                      const HomeScreen(),
                      const StatisticsScreen(),
                      const FavoritesScreen(),
                      const SettingsScreen(),
                    ],
                  );
                },
              ),
            );
          }
        ),
      ),
    );
  }
}
