import 'package:get_it/get_it.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/services/usage_service.dart';
import 'package:roamr/services/gemini_chat_service.dart';
import 'package:roamr/services/common_prompt_service.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Services
  getIt.registerLazySingleton<UsageService>(() => UsageService());
  getIt.registerLazySingleton<RevenueCatService>(() => RevenueCatService());
  getIt.registerLazySingleton<CommonPromptService>(() => CommonPromptService(Supabase.instance.client));
  getIt.registerLazySingleton<GeminiChatService>(() => GeminiChatService.instance);

  // Load prompts from Supabase or create default
  try {
    final promptService = getIt<CommonPromptService>();
    await promptService.loadAllPrompts();
  } catch (e) {
    print('Failed to load prompts from Supabase: $e');
    // Create default chat prompt
    final defaultPrompt = {
      'id': '1',
      'name': 'chat_service_roamr',
      'prompt': '',
      'model': 'gemini-1.5-flash',
      'analysis_structure': '',
      'response_structure': '',
      'system_instructions': '''You are RoamR AI, a helpful travel assistant integrated into the RoamR travel planning app. Your role is to help users with:

1. Travel planning and itinerary suggestions
2. Destination recommendations based on preferences
3. Travel tips and advice
4. Local culture and customs information
5. Budget planning for trips
6. Packing suggestions
7. Transportation options
8. Accommodation recommendations
9. Food and dining suggestions
10. Activity and attraction recommendations

Guidelines:
- Be friendly, enthusiastic, and helpful
- Provide practical, actionable advice
- Ask clarifying questions when needed
- Consider budget, time constraints, and user preferences
- Suggest specific places, activities, and experiences
- Be concise but informative
- Use emojis sparingly and appropriately
- If you don't know something specific, be honest and suggest reliable sources

Remember: You're helping users plan amazing travel experiences through the RoamR app!'''
    };
    await CommonPromptService.savePromptToPrefs(defaultPrompt);
  }

  // Repositories
  // TODO: If TripRepository or AttachmentRepository require arguments, add them here
  // getIt.registerLazySingleton<TripRepository>(() => TripRepository());
  // getIt.registerLazySingleton<AttachmentRepository>(() => AttachmentRepository());

  // Auth service
  getIt.registerLazySingleton<Auth>(() => Auth());

  // Register UserUsageCubit
  getIt.registerLazySingleton<UserUsageCubit>(() => UserUsageCubit());

  // Register your TripDatabase and any other core singletons here
  getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());
  getIt.registerLazySingleton<TripRepository>(() => TripRepository(tripDatabase: getIt<TripDatabase>()));

  // TODO: Register any other core singletons here
  // Example:
  // getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());
} 