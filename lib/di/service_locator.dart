import 'package:get_it/get_it.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/services/usage_service.dart';
import 'package:roamr/services/gemini_chat_service.dart';
import 'package:roamr/services/common_prompt_service.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Services
  getIt.registerLazySingleton<UsageService>(() => UsageService());
  getIt.registerLazySingleton<RevenueCatService>(() => RevenueCatService());
  getIt.registerLazySingleton<CommonPromptService>(() => CommonPromptService(Supabase.instance.client));
  getIt.registerLazySingleton<GeminiChatService>(() => GeminiChatService.instance);

  // Load prompts from Supabase
  final promptService = getIt<CommonPromptService>();
  await promptService.loadAllPrompts();

  // Repositories
  // TODO: If TripRepository or AttachmentRepository require arguments, add them here
  // getIt.registerLazySingleton<TripRepository>(() => TripRepository());
  // getIt.registerLazySingleton<AttachmentRepository>(() => AttachmentRepository());

  // Auth service
  getIt.registerLazySingleton<Auth>(() => Auth());

  // Register UserUsageCubit
  getIt.registerLazySingleton<UserUsageCubit>(() => UserUsageCubit());

  // Register your TripDatabase and any other core singletons here
  getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());
  getIt.registerLazySingleton<TripRepository>(() => TripRepository(tripDatabase: getIt<TripDatabase>()));

  // TODO: Register any other core singletons here
  // Example:
  // getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());
} 