import 'package:get_it/get_it.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/services/usage_service.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:roamr/services/gemini_chat_service.dart';

final getIt = GetIt.instance;

Future<void> setupServiceLocator() async {
  // Services
  getIt.registerLazySingleton<UsageService>(() => UsageService());
  getIt.registerLazySingleton<RevenueCatService>(() => RevenueCatService());

  // Repositories
  // TODO: If TripRepository or AttachmentRepository require arguments, add them here
  // getIt.registerLazySingleton<TripRepository>(() => TripRepository());
  // getIt.registerLazySingleton<AttachmentRepository>(() => AttachmentRepository());

  // Auth service
  getIt.registerLazySingleton<Auth>(() => Auth());

  // Register UserUsageCubit
  getIt.registerLazySingleton<UserUsageCubit>(() => UserUsageCubit());

  // Register your TripDatabase and any other core singletons here
  getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());
  getIt.registerLazySingleton<TripRepository>(() => TripRepository(tripDatabase: getIt<TripDatabase>()));

  // TODO: Register any other core singletons here
  // Example:
  // getIt.registerLazySingleton<TripDatabase>(() => TripDatabase());

  getIt.registerLazySingleton<GeminiChatService>(() => GeminiChatService.instance);
} 