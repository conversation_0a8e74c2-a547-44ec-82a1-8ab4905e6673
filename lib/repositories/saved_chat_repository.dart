import 'package:eunoia/data/database/app_database.dart';
import 'package:drift/drift.dart' as drift;

class SavedChat {
  final String id;
  final String title;
  final String content;
  final DateTime createdAt;
  final DateTime updatedAt;
  SavedChat({required this.id, required this.title, required this.content, required this.createdAt, required this.updatedAt});

  factory SavedChat.fromDbRow(row) => SavedChat(
    id: row.id,
    title: row.title,
    content: row.content,
    createdAt: row.createdAt,
    updatedAt: row.updatedAt,
  );
}

abstract class SavedChatRepository {
  Future<void> saveChat({required String id, required String title, required String content, required DateTime createdAt, required DateTime updatedAt});
  Future<List<SavedChat>> getAllChats();
  Future<void> clearAllChats();
  Future<List<SavedChat>> getChatsBatch({required int offset, required int limit});
}

class SavedChatRepositoryImpl implements SavedChatRepository {
  final AppDatabase _db;
  SavedChatRepositoryImpl(this._db);

  @override
  Future<void> saveChat({required String id, required String title, required String content, required DateTime createdAt, required DateTime updatedAt}) async {
    await _db.into(_db.savedChats).insert(
      SavedChatsCompanion(
        id: drift.Value(id),
        title: drift.Value(title),
        content: drift.Value(content),
        createdAt: drift.Value(createdAt),
        updatedAt: drift.Value(updatedAt),
      ),
    );
  }

  @override
  Future<List<SavedChat>> getAllChats() async {
    final rows = await _db.select(_db.savedChats).get();
    return rows.map((row) => SavedChat.fromDbRow(row)).toList();
  }

  @override
  Future<void> clearAllChats() async {
    await _db.delete(_db.savedChats).go();
  }

  @override
  Future<List<SavedChat>> getChatsBatch({required int offset, required int limit}) async {
    final query = _db.select(_db.savedChats)
      ..orderBy([(tbl) => drift.OrderingTerm(expression: tbl.createdAt, mode: drift.OrderingMode.desc)])
      ..limit(limit, offset: offset);
    final rows = await query.get();
    // Reverse so oldest is at the top, newest at the bottom
    return rows.map((row) => SavedChat.fromDbRow(row)).toList().reversed.toList();
  }
} 