import 'package:drift/drift.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/data/trip_database.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';

class TripRepository {
  final TripDatabase _tripDatabase;

  TripRepository({required TripDatabase tripDatabase})
    : _tripDatabase = tripDatabase;

  // Trips
  Future<void> createTrip(Trip trip) async {
    final tripCompanion = TripsCompanion(
      title: Value(trip.title),
      startDate: Value(trip.startDate),
      endDate: Value(trip.endDate),
      city: Value(trip.city),
      countryCode: Value(trip.countryCode),
      countryName: Value(trip.countryName),
    );
    await _tripDatabase.insertTrip(tripCompanion);
  }

  Future<void> deleteTrip(String id) async {
    // Get all itineraries for this trip
    final itineraries = await _tripDatabase.getItinerariesForTrip(id);

    // Delete attachments for each itinerary
    for (final itinerary in itineraries) {
      await _tripDatabase.deleteAttachmentsByItineraryId(itinerary.id);
    }

    // Delete the trip (which will also delete its itineraries)
    await _tripDatabase.deleteTrip(id);
  }

  Future<List<Trip>> getAllTrips() async {
    final trips = await _tripDatabase.getAllTrips();
    return Future.wait(
      trips.map((trip) async {
        return Trip.fromTripItem(trip);
      }),
    );
  }

  Future<void> editTrip(Trip trip) async {
    final tripCompanion = TripsCompanion(
      id: Value(trip.id),
      title: Value(trip.title),
      startDate: Value(trip.startDate),
      endDate: Value(trip.endDate),
      city: Value(trip.city),
      countryCode: Value(trip.countryCode),
      countryName: Value(trip.countryName),
    );
    await _tripDatabase.updateTrip(tripCompanion);
  }

  // Itineraries
  Future<String> createItinerary(TripItinerary itinerary, String tripId) async {
    final itineraryCompanion = ItinerariesCompanion(
      id: Value(itinerary.id),
      tripId: Value(tripId),
      title: Value(itinerary.title),
      amount: Value(itinerary.amount),
      date: Value(itinerary.date),
      category: Value(itinerary.category.name),
      location:
          itinerary.location != null
              ? Value(
                '${itinerary.location!.latitude},${itinerary.location!.longitude}',
              )
              : const Value.absent(),
      locationText: Value(itinerary.locationText),
      airlineName: Value(itinerary.airlineName),
      flightNumber: Value(itinerary.flightNumber),
      checkoutDate: Value(itinerary.checkoutDate),
      description: Value(itinerary.description),
      rank: Value(itinerary.rank),
    );
    // Let the database generate the ID and return it
    final actualId = await _tripDatabase.insertItinerary(itineraryCompanion);
    return actualId;
  }

  Future<void> editItinerary(TripItinerary itinerary, String tripId) async {
    final itineraryCompanion = ItinerariesCompanion(
      id: Value(itinerary.id),
      tripId: Value(tripId),
      title: Value(itinerary.title),
      amount: Value(itinerary.amount),
      date: Value(itinerary.date),
      category: Value(itinerary.category.name),
      location:
          itinerary.location != null
              ? Value(
                '${itinerary.location!.latitude},${itinerary.location!.longitude}',
              )
              : const Value.absent(),
      locationText: Value(itinerary.locationText),
      airlineName: Value(itinerary.airlineName),
      flightNumber: Value(itinerary.flightNumber),
      checkoutDate: Value(itinerary.checkoutDate),
      description: Value(itinerary.description),
      rank: Value(itinerary.rank),
    );
    await _tripDatabase.updateItinerary(itineraryCompanion);
  }

  Future<void> deleteItinerary(TripItinerary itinerary) async {
    // First delete all attachments associated with this itinerary
    await _tripDatabase.deleteAttachmentsByItineraryId(itinerary.id);

    // Then delete the itinerary itself
    final itineraryCompanion = ItinerariesCompanion(
      id: Value(itinerary.id),
      title: Value(itinerary.title),
      amount: Value(itinerary.amount),
      date: Value(itinerary.date),
      category: Value(itinerary.category.name),
    );
    await _tripDatabase.deleteItinerary(itineraryCompanion);
  }

  Future<void> reorderItineraries(
    TripItinerary movedItinerary,
    TripItinerary targetItinerary,
    String tripId,
  ) async {
    final itineraryCompanion = ItinerariesCompanion(
      id: Value(movedItinerary.id),
      tripId: Value(tripId),
      title: Value(movedItinerary.title),
      amount: Value(movedItinerary.amount),
      date: Value(movedItinerary.date),
      category: Value(movedItinerary.category.name),
      location:
          movedItinerary.location != null
              ? Value(
                '${movedItinerary.location!.latitude},${movedItinerary.location!.longitude}',
              )
              : const Value.absent(),
      locationText: Value(movedItinerary.locationText),
      airlineName: Value(movedItinerary.airlineName),
      flightNumber: Value(movedItinerary.flightNumber),
      checkoutDate: Value(movedItinerary.checkoutDate),
      description: Value(movedItinerary.description),
    );

    final targetItineraryCompanion = ItinerariesCompanion(
      id: Value(targetItinerary.id),
      tripId: Value(tripId),
      title: Value(targetItinerary.title),
      amount: Value(targetItinerary.amount),
      date: Value(targetItinerary.date),
      category: Value(targetItinerary.category.name),
      location:
          targetItinerary.location != null
              ? Value(
                '${targetItinerary.location!.latitude},${targetItinerary.location!.longitude}',
              )
              : const Value.absent(),
      locationText: Value(targetItinerary.locationText),
      airlineName: Value(targetItinerary.airlineName),
      flightNumber: Value(targetItinerary.flightNumber),
      checkoutDate: Value(targetItinerary.checkoutDate),
      description: Value(targetItinerary.description),
    );

    // Get all attachments for the itinerary being moved before deleting it
    final attachments = await _tripDatabase.getAttachmentsByItineraryId(movedItinerary.id);

    String newItineraryId;

    // Delete the itinerary from its current position
    await _tripDatabase.deleteItinerary(itineraryCompanion);

    // if we are inserting an itinerary at the begining of a new date
    final updatedCompanion = itineraryCompanion.copyWith(
      date: Value(targetItinerary.date),
    );
    if (targetItinerary.id.startsWith("dummy_")) {
      newItineraryId = await _tripDatabase.insertItinerary(updatedCompanion);
    }
    // if we are isnerting the itinerary below a particular exisiting Itinerary
    else {
      newItineraryId = await _tripDatabase.insertItineraryBelow(
        updatedCompanion,
        targetItineraryCompanion,
      );
    }

    // Update all attachments to point to the new itinerary ID
    for (final attachment in attachments) {
      await _tripDatabase.updateAttachmentItineraryId(attachment.id, newItineraryId);
    }
  }

  Stream<List<TripItinerary>> getTripItineraries(String tripId) {
    return _tripDatabase
        .watchItinerariesForTrip(tripId)
        .map(
          (itineraries) =>
              itineraries.map((itinerary) {
                final locationParts = itinerary.location?.split(',');
                final location =
                    locationParts != null && locationParts.length == 2
                        ? LatLng(
                          double.parse(locationParts[0]),
                          double.parse(locationParts[1]),
                        )
                        : null;
                return TripItinerary(
                  id: itinerary.id,
                  title: itinerary.title,
                  amount: itinerary.amount,
                  date: itinerary.date,
                  category: CategoryX.fromString(itinerary.category),
                  location: location,
                  locationText: itinerary.locationText,
                  airlineName: itinerary.airlineName,
                  flightNumber: itinerary.flightNumber,
                  checkoutDate: itinerary.checkoutDate,
                  description: itinerary.description,
                  rank: itinerary.rank,
                );
              }).toList(),
        );
  }

  /// Get trip itineraries as a Future instead of a Stream
  Future<List<TripItinerary>> getTripItinerariesFuture(String tripId) async {
    final itineraries = await _tripDatabase.getItinerariesForTrip(tripId);
    return itineraries.map((itinerary) {
      final locationParts = itinerary.location?.split(',');
      final location =
          locationParts != null && locationParts.length == 2
              ? LatLng(
                  double.parse(locationParts[0]),
                  double.parse(locationParts[1]),
                )
              : null;
      return TripItinerary(
        id: itinerary.id,
        title: itinerary.title,
        amount: itinerary.amount,
        date: itinerary.date,
        category: CategoryX.fromString(itinerary.category),
        location: location,
        locationText: itinerary.locationText,
        airlineName: itinerary.airlineName,
        flightNumber: itinerary.flightNumber,
        checkoutDate: itinerary.checkoutDate,
        description: itinerary.description,
        rank: itinerary.rank,
      );
    }).toList();
  }

  // Favorites
  Future<void> addToFavorites(TripItinerary itinerary) async {
    // Convert TripItinerary to ItineraryItem
    final locationStr = itinerary.location != null
        ? '${itinerary.location!.latitude},${itinerary.location!.longitude}'
        : null;

    final itineraryItem = ItineraryItem(
      id: itinerary.id,
      tripId: '', // Not needed for favorites
      title: itinerary.title,
      amount: itinerary.amount,
      date: itinerary.date,
      category: itinerary.category.name,
      location: locationStr,
      locationText: itinerary.locationText,
      airlineName: itinerary.airlineName,
      flightNumber: itinerary.flightNumber,
      checkoutDate: itinerary.checkoutDate,
      description: itinerary.description,
      rank: itinerary.rank,
    );

    await _tripDatabase.addToFavorites(itineraryItem);
  }

  Future<void> removeFromFavorites(String itineraryId) async {
    await _tripDatabase.removeFromFavorites(itineraryId);
  }

  Future<bool> isItineraryFavorite(String itineraryId) async {
    return await _tripDatabase.isItineraryFavorite(itineraryId);
  }

  Stream<List<TripItinerary>> getFavoriteItineraries() {
    return _tripDatabase.watchFavoriteItineraries().map(
      (favorites) =>
          favorites.map((favorite) {
            final locationParts = favorite.location?.split(',');
            final location =
                locationParts != null && locationParts.length == 2
                    ? LatLng(
                      double.parse(locationParts[0]),
                      double.parse(locationParts[1]),
                    )
                    : null;

            return TripItinerary(
              id: favorite.id,
              title: favorite.title,
              amount: favorite.amount,
              date: favorite.date,
              category: CategoryX.fromString(favorite.category),
              location: location,
              locationText: favorite.locationText,
              airlineName: favorite.airlineName,
              flightNumber: favorite.flightNumber,
              checkoutDate: favorite.checkoutDate,
              description: favorite.description,
              rank: 0, // Rank doesn't matter for favorites
            );
          }).toList(),
    );
  }
}
