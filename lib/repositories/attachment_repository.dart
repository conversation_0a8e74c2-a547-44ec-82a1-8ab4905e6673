import 'dart:io';
import 'package:drift/drift.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../data/trip_database.dart';
import '../models/attachment_model.dart';

abstract class AttachmentRepository {
  Future<List<AttachmentModel>> getAttachmentsByItineraryId(String itineraryId);
  Future<AttachmentModel?> getAttachmentById(String id);
  Future<AttachmentModel> saveAttachment(String itineraryId, AttachmentType type, File file);
  Future<void> deleteAttachment(String id);
  Future<void> deleteAttachmentsByItineraryId(String itineraryId);
}

class AttachmentRepositoryImpl implements AttachmentRepository {
  final TripDatabase _database;

  AttachmentRepositoryImpl(this._database);

  @override
  Future<List<AttachmentModel>> getAttachmentsByItineraryId(String itineraryId) async {
    final attachments = await _database.getAttachmentsByItineraryId(itineraryId);
    final appDir = await getApplicationDocumentsDirectory();

    // Create models with the app documents path
    return attachments.map((attachment) {
      return AttachmentModel(
        id: attachment.id,
        itineraryId: attachment.itineraryId,
        type: _stringToType(attachment.type),
        filePath: attachment.filePath,
        createdAt: attachment.createdAt,
        updatedAt: attachment.updatedAt,
        appDocumentsPath: appDir.path,
      );
    }).toList();
  }

  @override
  Future<AttachmentModel?> getAttachmentById(String id) async {
    final attachment = await _database.getAttachmentById(id);
    if (attachment == null) return null;

    final appDir = await getApplicationDocumentsDirectory();
    return AttachmentModel(
      id: attachment.id,
      itineraryId: attachment.itineraryId,
      type: _stringToType(attachment.type),
      filePath: attachment.filePath,
      createdAt: attachment.createdAt,
      updatedAt: attachment.updatedAt,
      appDocumentsPath: appDir.path,
    );
  }

  @override
  Future<AttachmentModel> saveAttachment(String itineraryId, AttachmentType type, File file) async {
    // Generate a unique ID for the attachment
    final id = const Uuid().v4();

    // Create a directory for attachments if it doesn't exist
    final appDir = await getApplicationDocumentsDirectory();
    final attachmentsDir = Directory(p.join(appDir.path, 'attachments'));
    if (!await attachmentsDir.exists()) {
      await attachmentsDir.create(recursive: true);
    }

    // Create a subdirectory for the itinerary if it doesn't exist
    final itineraryDir = Directory(p.join(attachmentsDir.path, itineraryId));
    if (!await itineraryDir.exists()) {
      await itineraryDir.create(recursive: true);
    }

    // Generate a filename based on the attachment type and ID
    final extension = _getFileExtension(file.path);
    final filename = '$id$extension';

    // Store a relative path in the database (relative to app documents directory)
    final relativePath = p.join('attachments', itineraryId, filename);

    // Get the absolute path for file operations
    final absolutePath = p.join(appDir.path, relativePath);

    // Copy the file to the attachments directory
    await file.copy(absolutePath);

    // Create the attachment record in the database
    final now = DateTime.now();
    await _database.insertAttachment(
      AttachmentsCompanion(
        id: Value(id),
        itineraryId: Value(itineraryId),
        type: Value(_typeToString(type)),
        filePath: Value(relativePath), // Store relative path
        createdAt: Value(now),
        updatedAt: Value(now),
      ),
    );

    // Return the attachment model
    return AttachmentModel(
      id: id,
      itineraryId: itineraryId,
      type: type,
      filePath: relativePath,
      createdAt: now,
      updatedAt: now,
      appDocumentsPath: appDir.path,
    );
  }

  @override
  Future<void> deleteAttachment(String id) async {
    // Get the attachment to get the file path
    final attachment = await _database.getAttachmentById(id);
    if (attachment != null) {
      // Get the app documents directory
      final appDir = await getApplicationDocumentsDirectory();

      // Get the absolute file path
      final absolutePath = p.join(appDir.path, attachment.filePath);

      // Delete the file
      final file = File(absolutePath);
      if (await file.exists()) {
        await file.delete();
      }

      // Delete the attachment record
      await _database.deleteAttachment(id);
    }
  }

  @override
  Future<void> deleteAttachmentsByItineraryId(String itineraryId) async {
    // Get all attachments for the itinerary
    final attachments = await _database.getAttachmentsByItineraryId(itineraryId);

    // Get the app documents directory
    final appDir = await getApplicationDocumentsDirectory();

    // Delete each attachment file
    for (final attachment in attachments) {
      // Get the absolute file path
      final absolutePath = p.join(appDir.path, attachment.filePath);

      final file = File(absolutePath);
      if (await file.exists()) {
        await file.delete();
      }
    }

    // Delete the attachment records
    await _database.deleteAttachmentsByItineraryId(itineraryId);

    // Delete the itinerary directory if it exists
    final itineraryDir = Directory(p.join(appDir.path, 'attachments', itineraryId));
    if (await itineraryDir.exists()) {
      await itineraryDir.delete(recursive: true);
    }
  }

  // Helper method to convert AttachmentType enum to string
  String _typeToString(AttachmentType type) {
    switch (type) {
      case AttachmentType.photo:
        return 'photo';
      case AttachmentType.document:
        return 'document';
    }
  }

  // Helper method to convert string to AttachmentType enum
  AttachmentType _stringToType(String type) {
    switch (type) {
      case 'photo':
        return AttachmentType.photo;
      case 'document':
        return AttachmentType.document;
      default:
        return AttachmentType.photo; // Default to photo
    }
  }

  // Helper method to get file extension from path
  String _getFileExtension(String path) {
    final extension = p.extension(path);
    if (extension.isEmpty) {
      // Default extension for photos
      return '.jpg';
    }
    return extension;
  }
}
