import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/utils/dialog_utils.dart';
import 'package:roamr/widgets/usage_left_widget.dart';

Future<bool> checkAttachmentLimitAndShowDialog(BuildContext context) async {
  final usage = context.read<UserUsageCubit>().state;
  final isPremium = await RevenueCatService().isSubscriptionActive();
  if (!isPremium && (usage?.attachmentsCreated ?? 0) >= attachmentLimit) {
    final result = await showPremiumRequiredDialog(context);
    if (result == true) {
      await RevenueCatService().presentPaywall();
    }
    return false;
  }
  return true;
}

Future<bool> checkItineraryLimitAndShowDialog(BuildContext context, String tripId) async {
  final usage = context.read<UserUsageCubit>().state;
  final isPremium = await RevenueCatService().isSubscriptionActive();
  if (!isPremium && (usage?.itinerariesCreated ?? 0) >= itineraryLimit) {
    final result = await showPremiumRequiredDialog(context);
    if (result == true) {
      await RevenueCatService().presentPaywall();
    }
    return false;
  }
  return true;
} 