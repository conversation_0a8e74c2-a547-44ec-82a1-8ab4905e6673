import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:roamr/l10n/app_localizations.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:roamr/data/settings_preference.dart';
import 'package:roamr/widgets/translucent_dialog.dart';

/// Utility class for voucher code functionality
class VoucherUtils {
  // Supabase client
  static final SupabaseClient _client = Supabase.instance.client;

  /// Checks if a voucher code is valid by querying Supabase
  static Future<bool> isValidVoucherCode(String code) async {
    try {
      // Convert to uppercase for case-insensitive comparison
      final upperCaseCode = code.toUpperCase();

      // Query the vouchers table in Supabase
      final response = await _client
          .from('vouchers')
          .select()
          .eq('code', upperCaseCode)
          .limit(1)
          .maybeSingle();

      // If we got a response, the code is valid
      return response != null;
    } catch (e) {
      debugPrint('Error checking voucher code: $e');
      return false;
    }
  }

  /// Applies a voucher code if valid
  /// Returns true if the code was valid and applied successfully
  static Future<bool> applyVoucherCode(String code) async {
    // Check if the code is valid
    final isValid = await isValidVoucherCode(code);
    if (isValid) {
      // Save the voucher code locally for better user experience
      await SettingsPreferences.saveVoucherCode(code.toUpperCase());
      return true;
    }
    return false;
  }

  /// Checks if a valid voucher code has been applied
  /// Retrieves the saved code and verifies it's still valid in Supabase
  static Future<bool> hasValidVoucherCode() async {
    final savedCode = await SettingsPreferences.getVoucherCode();
    if (savedCode != null) {
      // Verify the saved code is still valid in Supabase
      return await isValidVoucherCode(savedCode);
    }
    return false;
  }

  /// Shows a dialog to enter a voucher code
  static Future<void> showVoucherCodeDialog(BuildContext context) async {
    final textController = TextEditingController();
    String? errorText;
    bool isProcessing = false;

    await showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            final theme = Theme.of(context);
            final isDarkMode = theme.brightness == Brightness.dark;
            final textColor = isDarkMode ? Colors.white : Colors.black;
            final placeholderColor = isDarkMode ? Colors.grey[400] : Colors.grey[600];
            final backgroundColor = isDarkMode
                ? Colors.grey[800]
                : CupertinoColors.white;
            final borderColor = isDarkMode
                ? Colors.grey[600]
                : CupertinoColors.systemGrey;

            return TranslucentDialog(
              title: AppLocalizations.of(context)!.enter_voucher_code,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Text field for voucher code
                  CupertinoTextField(
                    controller: textController,
                    placeholder: AppLocalizations.of(context)!.enter_voucher_code_hint,
                    placeholderStyle: TextStyle(color: placeholderColor),
                    style: TextStyle(color: textColor),
                    textCapitalization: TextCapitalization.characters,
                    padding: const EdgeInsets.all(12.0),
                    decoration: BoxDecoration(
                      color: backgroundColor,
                      border: Border.all(color: borderColor!),
                      borderRadius: BorderRadius.circular(8.0),
                    ),
                    enabled: !isProcessing,
                  ),

                  // Error text if any
                  if (errorText != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        errorText!,
                        style: const TextStyle(color: CupertinoColors.destructiveRed),
                      ),
                    ),

                  const SizedBox(height: 16),

                  // Action buttons
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // Cancel button
                      CupertinoButton(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        onPressed: () {
                          Navigator.pop(context);
                        },
                        child: Text(AppLocalizations.of(context)!.cancel),
                      ),

                      // Apply button or loading indicator
                      if (isProcessing)
                        const CupertinoActivityIndicator()
                      else
                        CupertinoButton(
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          onPressed: () {
                            final code = textController.text.trim();
                            if (code.isEmpty) {
                              setState(() {
                                errorText = AppLocalizations.of(context)!.please_enter_voucher_code;
                              });
                              return;
                            }

                            setState(() {
                              isProcessing = true;
                              errorText = null;
                            });

                            // Use Future.delayed to avoid blocking the UI
                            Future.delayed(Duration.zero, () async {
                              final success = await applyVoucherCode(code);

                              if (context.mounted) {
                                if (success) {
                                  Navigator.pop(context);
                                  ScaffoldMessenger.of(context).showSnackBar(
                                    SnackBar(content: Text(AppLocalizations.of(context)!.voucher_code_applied)),
                                  );
                                } else {
                                  setState(() {
                                    isProcessing = false;
                                    errorText = AppLocalizations.of(context)!.invalid_voucher_code;
                                  });
                                }
                              }
                            });
                          },
                          child: Text(
                            AppLocalizations.of(context)!.apply,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
