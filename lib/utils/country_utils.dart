import 'package:flutter/material.dart';

class CountryUtils {
  /// Convert country code to emoji flag
  static String countryCodeToEmoji(String? countryCode) {
    if (countryCode == null || countryCode.isEmpty) return '';

    // Convert country code to uppercase
    final code = countryCode.toUpperCase();

    // Check if the code is valid (2 letters)
    if (code.length != 2) return '';

    // Convert each letter to the corresponding regional indicator symbol
    final int firstLetter = code.codeUnitAt(0) - 0x41 + 0x1F1E6;
    final int secondLetter = code.codeUnitAt(1) - 0x41 + 0x1F1E6;

    // Convert the code points to characters and return the flag
    return String.fromCharCode(firstLetter) + String.fromCharCode(secondLetter);
  }

  /// Build a flag widget with proper styling
  static Widget buildFlagWidget(String? countryCode, {double size = 24.0}) {
    final flag = countryCodeToEmoji(countryCode);

    if (flag.isEmpty) {
      return SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(4.0),
      decoration: BoxDecoration(
        color: Colors.white.withAlpha(51),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Text(
        flag,
        style: TextStyle(
          fontSize: size,
        ),
      ),
    );
  }

  /// Build a profile-style avatar with country flag or first letter fallback
  static Widget buildFlagAvatar(String? countryCode, String title, {double size = 40.0, Color? backgroundColor}) {
    final flag = countryCodeToEmoji(countryCode);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.grey.withAlpha(50),
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(15),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Center(
        child: flag.isNotEmpty
            ? Text(
                flag,
                style: TextStyle(
                  fontSize: size * 0.65,
                ),
              )
            : Text(
                title.isNotEmpty ? title[0].toUpperCase() : '?',
                style: TextStyle(
                  fontSize: size * 0.4,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
      ),
    );
  }
}
