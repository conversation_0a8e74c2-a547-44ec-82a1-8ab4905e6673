import 'package:in_app_review/in_app_review.dart';
import 'package:package_info_plus/package_info_plus.dart';

class RatingUtils {
  static final InAppReview _inAppReview = InAppReview.instance;

  static Future<void> requestReview() async {
    if (await _inAppReview.isAvailable()) {
      _inAppReview.requestReview();
    }
  }

  // Optional: Method to open the store listing for the app
  static Future<void> openStoreListing() async {
    if (await _inAppReview.isAvailable()) {
      final packageInfo = await PackageInfo.fromPlatform();
      _inAppReview.openStoreListing(appStoreId: packageInfo.packageName);
    }
  }
}