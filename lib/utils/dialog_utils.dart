import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:roamr/l10n/app_localizations.dart';

Future<void> showNoLocationsDialog(BuildContext context) {
  return showDialog<void>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(AppLocalizations.of(context)!.map_not_available),
      content: Text(AppLocalizations.of(context)!.add_location_details),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(AppLocalizations.of(context)!.ok),
        ),
      ],
    ),
  );
}

Future<bool?> showDeletionConfirmationDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(AppLocalizations.of(context)!.confirmDeletion),
      content: Text(AppLocalizations.of(context)!.confirmDeletionMessage),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(AppLocalizations.of(context)!.cancel),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(true),
          isDestructiveAction: true,
          child: Text(AppLocalizations.of(context)!.delete),
        ),
      ],
    ),
  );
}

Future<bool?> showSignOutConfirmationDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(AppLocalizations.of(context)!.sign_out),
      content: Text(AppLocalizations.of(context)!.sign_out_confirmation),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(AppLocalizations.of(context)!.cancel),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(true),
          child: Text(AppLocalizations.of(context)!.sign_out),
        ),
      ],
    ),
  );
}

Future<bool?> showDeleteAccountConfirmationDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(AppLocalizations.of(context)!.delete_account_confirmation),
      content: Text(AppLocalizations.of(context)!.delete_account_message),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(AppLocalizations.of(context)!.cancel),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(true),
          isDestructiveAction: true,
          child: Text(AppLocalizations.of(context)!.delete_account),
        ),
      ],
    ),
  );
}

Future<bool?> showRemoveFromFavoritesDialog(BuildContext context) {
  return showDialog<bool>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(AppLocalizations.of(context)!.remove_from_favorites_confirmation),
      content: Text(AppLocalizations.of(context)!.remove_from_favorites_message),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(AppLocalizations.of(context)!.cancel),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(true),
          isDestructiveAction: true,
          child: Text(AppLocalizations.of(context)!.remove),
        ),
      ],
    ),
  );
}

Future<bool?> showPremiumRequiredDialog(BuildContext context) {
  final localizations = AppLocalizations.of(context)!;
  return showDialog<bool>(
    context: context,
    builder: (context) => CupertinoAlertDialog(
      title: Text(localizations.usage_details_title),
      content: Text(localizations.premium_required_message),
      actions: [
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(false),
          child: Text(localizations.close),
        ),
        CupertinoDialogAction(
          onPressed: () => Navigator.of(context).pop(true),
          isDefaultAction: true,
          child: Text(localizations.upgrade),
        ),
      ],
    ),
  );
}
