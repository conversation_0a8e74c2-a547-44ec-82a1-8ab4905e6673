import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/currency_bloc/currency_bloc.dart';
import 'package:intl/intl.dart';

class CurrencyUtils {
  static const String defaultCurrency = 'USD';

  static Map<String, String> currencySymbols = {
    'USD': '\$',
    'EUR': '€',
    'GBP': '£',
    'JPY': '¥',
    'THB': '฿',
    'INR': '₹',
  };

  static String getCurrencySymbol(BuildContext context) {
    final selectedCurrency = context.read<CurrencyCubit>().state.selectedCurrency;
    return currencySymbols[selectedCurrency.split(" ")[0]] ?? currencySymbols[defaultCurrency]!;
  }

  static List<String> getAvailableCurrencies() {
    return currencySymbols.entries
        .map((entry) => '${entry.key} (${entry.value})')
        .toList();
  }

  static String formatTotalExpenses(BuildContext context, double totalExpenses) {
    final currencySymbol = getCurrencySymbol(context);
    final currencyFormatter = NumberFormat.currency(symbol: currencySymbol, decimalDigits: 0);
    final formattedValue = currencyFormatter.format(totalExpenses.abs());
    return totalExpenses <= 0 ? formattedValue : "$formattedValue";
  }
}
