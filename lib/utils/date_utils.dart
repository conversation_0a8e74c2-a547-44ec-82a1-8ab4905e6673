import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';

class DateUtils {
  static String formatDate(DateTime date) {
    return DateFormat('EEEE, d MMMM, yyyy').format(date);
  }

  static String formatDate2(DateTime date) {
    return DateFormat('EE, d MMMM, yyyy').format(date);
  }

  static String formatDateRange(DateTime startDate, DateTime endDate, {BuildContext? context}) {
    final dateFormat = DateFormat('d MMM');
    final start = dateFormat.format(startDate);
    final end = dateFormat.format(endDate);
    final duration =
        endDate.difference(startDate).inDays +
        1; // Include the end date in the duration

    // Use localized strings if context is provided
    String daysText;
    if (context != null) {
      final l10n = AppLocalizations.of(context)!;
      daysText = duration == 1 ? l10n.day : l10n.days;
    } else {
      // Fallback if context is not available
      daysText = duration == 1 ? 'day' : 'days';
    }

    return '$start - $end ($duration $daysText)';
  }

  /// Format date range specifically for accommodation where checkout date is not counted as a stay day
  static String formatAccommodationDateRange(DateTime checkInDate, DateTime checkOutDate, {BuildContext? context}) {
    final dateFormat = DateFormat('d MMM');
    final start = dateFormat.format(checkInDate);
    final end = dateFormat.format(checkOutDate);
    final duration = checkOutDate.difference(checkInDate).inDays; // Don't add 1 for checkout day

    // Use localized strings if context is provided
    String nightsText;
    if (context != null) {
      final l10n = AppLocalizations.of(context)!;
      nightsText = duration == 1 ? l10n.night : l10n.nights;
    } else {
      // Fallback if context is not available
      nightsText = duration == 1 ? 'night' : 'nights';
    }

    return '$start - $end ($duration $nightsText)';
  }

  static String extractTime(DateTime date) {
    return DateFormat('hh:mm a').format(date);
  }
}
