import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:roamr/models/achievement.dart';

/// Utility class to provide consistent colors and icons for achievement types
class AchievementUtils {
  /// Get the color for a specific achievement type
  static Color getColorForType(AchievementType type) {
    switch (type) {
      case AchievementType.destination:
        return Colors.orange;
      case AchievementType.expense:
        return Colors.red;
      case AchievementType.frequency:
        return Colors.teal;
      case AchievementType.duration:
        return Colors.indigo;
      case AchievementType.flight:
        return Colors.purple;
    }
  }

  /// Get the icon for a specific achievement type
  static IconData getIconForType(AchievementType type) {
    switch (type) {
      case AchievementType.destination:
        return Icons.place;
      case AchievementType.expense:
        return Icons.attach_money;
      case AchievementType.frequency:
        return Icons.card_travel;
      case AchievementType.duration:
        return Icons.calendar_month;
      case AchievementType.flight:
        return Icons.flight;
    }
  }

  /// Get the SVG asset path for a specific achievement type
  static String getSvgForType(AchievementType type) {
    switch (type) {
      case AchievementType.destination:
        return 'assets/destination_badge.svg';
      case AchievementType.expense:
        return 'assets/expense_badge.svg';
      case AchievementType.frequency:
        return 'assets/frequency_badge.svg';
      case AchievementType.duration:
        return 'assets/duration_badge.svg';
      case AchievementType.flight:
        return 'assets/flight_badge.svg';
    }
  }

  /// Build a badge widget with a star showing the count
  /// If isUnlocked is false, the badge will be greyed out with a lock icon
  static Widget buildBadgeWithStar(
    AchievementType type,
    int starCount, {
    bool isUnlocked = true,
  }) {
    // Fixed badge size
    const double badgeWidth = 80.0;
    const double badgeHeight = 100.0;

    // Ensure star count is between 1 and 6
    final validStarCount = starCount.clamp(1, 6);

    // Get the appropriate SVG file for this achievement type
    final String svgAsset = getSvgForType(type);

    // Get the appropriate icon for this achievement type
    final IconData icon = getIconForType(type);

    return Stack(
      alignment: Alignment.center,
      children: [
        // The badge SVG with color filter for locked state
        SizedBox(
          width: badgeWidth,
          height: badgeHeight,
          child:
              isUnlocked
                  ? SvgPicture.asset(svgAsset)
                  : ColorFiltered(
                    colorFilter: const ColorFilter.matrix([
                      0.4, 0.4, 0.4, 0, 60, // Red channel with boost
                      0.4, 0.4, 0.4, 0, 60, // Green channel with boost
                      0.4, 0.4, 0.4, 0, 60, // Blue channel with boost
                      0, 0, 0, 0.7, 0, // Alpha channel
                    ]),
                    child: SvgPicture.asset(svgAsset),
                  ),
        ),

        // Icon in the center with white circular border
        Positioned(
          top: 45,
          child: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: Colors.transparent,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 2),
            ),
            child: Center(child: Icon(icon, color: Colors.white, size: 24)),
          ),
        ),

        // Single star with number at the top
        Positioned(
          top: -8,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  Icon(
                    Icons.star,
                    color: isUnlocked ? Colors.yellow : Colors.grey.shade300,
                    size: 60,
                  ),
                  Icon(
                    Icons.star_border_sharp,
                    color: isUnlocked ? Colors.black45 : Colors.grey.shade300,
                    size: 60,
                  ),
                ],
              ),
              // Number inside star
              Text(
                validStarCount.toString(),
                style: TextStyle(
                  color: isUnlocked ? Colors.black45 : Colors.grey.shade700,
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),

        // Lock icon for locked achievements
        if (!isUnlocked)
          Positioned(
            right: 20,
            bottom: 20,
            child: Container(
              padding: const EdgeInsets.all(2.0),
              decoration: BoxDecoration(
                color: Colors.black54,
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.lock, color: Colors.white, size: 16.0),
            ),
          ),
      ],
    );
  }
}
