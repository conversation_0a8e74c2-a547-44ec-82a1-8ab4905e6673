import 'dart:async';
import 'package:flutter/material.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/config/api_keys.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Handles deep linking and OAuth callbacks for the app
class DeepLinkHandler {
  static final DeepLinkHandler _instance = DeepLinkHandler._internal();
  final Auth _auth = Auth();
  final supabase = Supabase.instance.client;
  StreamSubscription<AuthState>? _authSubscription;
  final _navigatorKey = GlobalKey<NavigatorState>();
  final _deepLinkStreamController = StreamController<Uri>.broadcast();

  factory DeepLinkHandler() => _instance;

  DeepLinkHandler._internal();

  /// Get the navigator key for navigation from anywhere in the app
  GlobalKey<NavigatorState> get navigatorKey => _navigatorKey;

  /// Stream of deep links
  Stream<Uri> get deepLinkStream => _deepLinkStreamController.stream;

  /// Initialize deep link handling
  void init() {
    // Cancel any existing subscription
    _authSubscription?.cancel();
    
    // Listen for auth state changes
    _authSubscription = supabase.auth.onAuthStateChange.listen((data) async {
      final session = data.session;
      final event = data.event;
      
      if (session != null && event == AuthChangeEvent.signedIn) {
        await _handlePostLogin();
      }
    });
  }

  /// Handle a deep link URL
  Future<void> handleDeepLink(Uri uri) async {
    _deepLinkStreamController.add(uri);
    
    if (_isOAuthCallback(uri)) {
      await _handleOAuthCallback(uri);
    }
    
    // Add more deep link handling as needed
  }

  /// Check if a URI is an OAuth callback
  bool _isOAuthCallback(Uri uri) {
    final callbackUrl = ApiKeys.getOAuthCallbackUrl();
    final callbackUri = Uri.parse(callbackUrl);
    
    return uri.scheme == callbackUri.scheme && 
           uri.host == callbackUri.host &&
           uri.path == callbackUri.path;
  }

  /// Handle OAuth callback
  Future<void> _handleOAuthCallback(Uri uri) async {
    try {
      await _auth.handleOAuthCallback(uri);
      _navigateToHome();
    } catch (e) {
      _showErrorSnackBar('Failed to sign in with Google: $e');
    }
  }
  
  /// Navigate to home screen
  void _navigateToHome() {
    if (_navigatorKey.currentContext != null) {
      Navigator.of(_navigatorKey.currentContext!).pushNamedAndRemoveUntil(
        '/',
        (route) => false,
      );
    }
  }

  /// Handle post-login logic
  Future<void> _handlePostLogin() async {
    try {
      // Identify the user with RevenueCat
      final user = supabase.auth.currentUser;
      if (user != null && user.email != null) {
        await RevenueCatService().identifyUser(user.email!);
      }
      
      // Navigate to home screen
      _navigateToHome();
    } catch (e) {
    }
    
    // Close any open dialogs or bottom sheets
    if (_navigatorKey.currentContext != null) {
      Navigator.of(_navigatorKey.currentContext!, rootNavigator: true).popUntil((route) => route.isFirst);
    }
  }



  /// Show error message to user
  void _showErrorSnackBar(String message) {
    if (_navigatorKey.currentContext == null) return;
    
    ScaffoldMessenger.of(_navigatorKey.currentContext!).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  /// Handle app initialization with deep links
  static Future<void> handleInitialDeepLink() async {
    try {
      final currentSession = Supabase.instance.client.auth.currentSession;
      if (currentSession != null) {
        // Handle any initial deep link logic here if needed
      }
    } catch (e) {

    }
  }
  
  /// Clean up resources
  void dispose() {
    _authSubscription?.cancel();
  }
}
