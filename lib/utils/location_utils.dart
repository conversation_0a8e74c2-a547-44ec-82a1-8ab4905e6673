import 'package:geocoding/geocoding.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/constants/app_constants.dart'; // Add this import

Future<Map<String, String>> getLocationDetails(LatLng location) async {
  try {
    List<Placemark> placemarks = await placemarkFromCoordinates(location.latitude, location.longitude);
    if (placemarks.isNotEmpty) {
      Placemark placemark = placemarks.first;
      String locationName = placemark.name ?? '';
      String remainingAddress = '${placemark.street}, ${placemark.locality}, ${placemark.administrativeArea}, ${placemark.country}';
      return {
        AppConstants.name: locationName,
        AppConstants.address: remainingAddress,
      };
    }
  } catch (e) {
    print('Error getting location details: $e');
  }
  return {
    AppConstants.name: '',
    AppConstants.address: '',
  };
}
