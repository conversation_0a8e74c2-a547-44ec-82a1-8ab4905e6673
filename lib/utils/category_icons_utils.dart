import 'package:flutter/material.dart';
import 'package:roamr/models/category.dart';

class IconUtils {
  static IconData getIconForCategory(Category category) {
    switch (category) {
      case Category.entertainment:
        return Icons.movie;
      case Category.restaurant:
        return Icons.restaurant;
      case Category.other:
        return Icons.luggage;
      case Category.accommodation:
        return Icons.hotel;
      case Category.transportation:
        return Icons.directions_car;
      case Category.sightseeing:
        return Icons.camera_alt;
      case Category.shopping:
        return Icons.shopping_cart;
      case Category.activity:
        return Icons.directions_run;
      case Category.parking:
        return Icons.local_parking;
      case Category.note:
        return Icons.note;
      case Category.movie:
        return Icons.local_movies;
      case Category.flight:
        return Icons.flight;
      case Category.carRental:
        return Icons.car_rental;
      default:
        return Icons.help_outline;
    }
  }
}