/// Extension to add null-safe operations on Map
/// 
/// Example:
/// ```dart
/// final params = <String, dynamic>{
///   'required': 'value',
///   'optional': null,
///   'another': 'another_value',
/// }.filterNulls();
/// // Result: {'required': 'value', 'another': 'another_value'}
/// ```
extension MapNullFilter on Map<String, dynamic> {
  /// Returns a new map with all null values removed
  Map<String, dynamic> filterNulls() {
    return Map<String, dynamic>.fromEntries(
      entries.where((entry) => entry.value != null),
    );
  }
  
  /// Returns a new map with all null values removed and cast to Map<String, Object>
  Map<String, Object> filterAndCast() {
    // Create a new map and only add non-null values
    final result = <String, Object>{};
    forEach((key, value) {
      if (value != null) {
        result[key] = value as Object;
      }
    });
    return result;
  }
}

/// Creates a map with all non-null key-value pairs from the provided map
Map<String, Object> createMapWithNonNulls(Map<String, dynamic> source) {
  // Create a new map and only add non-null values
  final result = <String, Object>{};
  source.forEach((key, value) {
    if (value != null) {
      result[key] = value as Object;
    }
  });
  return result;
}
