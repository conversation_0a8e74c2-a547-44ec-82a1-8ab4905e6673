import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_gen/gen_l10n/app_localizations.dart';
import 'package:roamr/pages/auth/login_register_page.dart';
import 'package:roamr/services/auth.dart';
import 'package:roamr/services/revenue_cat_service.dart';
import 'package:roamr/utils/debug_util.dart';
import 'package:roamr/utils/voucher_utils.dart';

/// Utility class for premium-related functions
class PremiumUtils {
  /// Checks if the user is premium and shows the paywall if not
  ///
  /// Returns true if the user is premium, false otherwise
  /// If the user is not premium, it will show the login screen or paywall
  static Future<bool> checkPremiumStatus(BuildContext context) async {
      // Check if user has a valid voucher code
    final hasValidVoucher = await VoucherUtils.hasValidVoucherCode();
    if (hasValidVoucher) {
      return true;
    }

    // Check if user is premium directly from RevenueCat
    final revenueCatService = RevenueCatService();
    final isPremium = await revenueCatService.isSubscriptionActive();

    // Allow Android users to access premium features
    if (defaultTargetPlatform == TargetPlatform.android) {
      return true;
    }

    if (!isPremium) {
      // User is not premium, check if they are signed in
      final auth = Auth();

      // Check if user is signed in
      if (auth.currentUser == null) {
        // User is not signed in, navigate to login page
        if (context.mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => const LoginRegisterPage(fromPremium: true),
            ),
          );
        }
        return false;
      }

      // User is signed in but not premium, show paywall
      // Show loading indicator
      final scaffoldMessenger = ScaffoldMessenger.of(context);
      final loadingSnackBar = SnackBar(
        content: Row(
          children: [
            const SizedBox(width: 12, height: 12, child: CircularProgressIndicator(strokeWidth: 2)),
            const SizedBox(width: 16),
            Text(AppLocalizations.of(context)!.loading_premium_options),
          ],
        ),
        duration: const Duration(seconds: 1),
      );
      scaffoldMessenger.showSnackBar(loadingSnackBar);

      // Present the paywall
      final revenueCatService = RevenueCatService();
      final success = await revenueCatService.presentPaywall();

      // Show success message if purchase was successful
      if (success && context.mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(content: Text(AppLocalizations.of(context)!.premium_features_unlocked)),
        );

        // Return true if the purchase was successful
        return true;
      }

      return false;
    }

    // User is premium
    return true;
  }
}
