import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/trip_repository.dart';

/// Utility class for debug-related functionality
class DebugSettingsUtil {
  static const List<String> _tripJsonFiles = [
    'assets/data/trip_japan_adventure.json',
    'assets/data/trip_european_getaway.json',
    'assets/data/trip_southeast_asia_tour.json',
    'assets/data/trip_california_road_trip.json',
    'assets/data/trip_australian_exploration.json',
  ];

  /// Generate trips with itineraries from JSON files
  static Future<String?> generateRandomTrips(TripRepository tripRepository) async {
    try {
      // Step 1: Load all JSON files and extract trip data
      final List<Map<String, dynamic>> allTripData = [];
      for (final String jsonFile in _tripJsonFiles) {
        try {
          final String jsonString = await rootBundle.loadString(jsonFile);
          final Map<String, dynamic> tripData = json.decode(jsonString);
          allTripData.add(tripData);
        } catch (e) {
          return 'Error loading $jsonFile: $e';
        }
      }

      // Step 2: Create all trips first (without itineraries)
      final List<Trip> createdTrips = [];
      for (final tripData in allTripData) {
        try {
          final Map<String, dynamic> tripJson = tripData['trip'];

          // Create a new trip
          final Trip trip = Trip(
            id: "", // Empty ID so the database will generate one
            title: tripJson['title'],
            startDate: DateTime.parse(tripJson['startDate']),
            endDate: DateTime.parse(tripJson['endDate']),
            city: tripJson['city'],
            countryCode: tripJson['countryCode'],
            countryName: tripJson['countryName'],
          );

          // Save trip to database
          await tripRepository.createTrip(trip);

          // Get the newly created trip with its generated ID
          final allTrips = await tripRepository.getAllTrips();
          final newTrip = allTrips.firstWhere((t) => t.title == tripJson['title']);
          createdTrips.add(newTrip);
        } catch (e) {
          return 'Error creating trip: $e';
        }
      }

      // Step 3: Add itineraries to each trip
      for (int i = 0; i < createdTrips.length; i++) {
        final Trip trip = createdTrips[i];
        final Map<String, dynamic> tripData = allTripData[i];

        try {
          final List<dynamic> itinerariesJson = tripData['itineraries'];

          for (final itineraryJson in itinerariesJson) {
            try {
              // Create itinerary object
              final TripItinerary itinerary = TripItinerary(
                id: "", // Empty ID so the database will generate one
                title: itineraryJson['title'],
                amount: itineraryJson['amount']?.toDouble(),
                date: DateTime.fromMillisecondsSinceEpoch(itineraryJson['date']),
                category: Category.values.firstWhere(
                  (c) => c.name == itineraryJson['category'],
                  orElse: () => Category.other,
                ),
                location: itineraryJson['location'] != null
                    ? LatLng(
                        itineraryJson['location']['lat'],
                        itineraryJson['location']['lng'],
                      )
                    : null,
                locationText: itineraryJson['locationText'],
                airlineName: itineraryJson['airlineName'],
                flightNumber: itineraryJson['flightNumber'],
                checkoutDate: itineraryJson['checkoutDate'] != null
                    ? DateTime.fromMillisecondsSinceEpoch(itineraryJson['checkoutDate'])
                    : null,
                description: itineraryJson['description'],
                rank: itineraryJson['rank'],
              );

              // Save itinerary
              await tripRepository.createItinerary(itinerary, trip.id);
            } catch (e) {
              // Continue with next itinerary even if one fails
            }
          }
        } catch (e) {
          return 'Error adding itineraries to trip ${trip.title}: $e';
        }
      }

      return null; // Success (no error)
    } catch (e) {
      return 'Error generating trips: $e';
    }
  }
}
