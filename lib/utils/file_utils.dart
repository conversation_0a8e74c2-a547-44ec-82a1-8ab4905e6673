import 'dart:io';

/// Utility class for handling file operations
class FileUtils {
  /// Checks if a file is a document type (PDF, DOC, DOCX, TXT, XLS, XLSX)
  static bool isDocumentFile(String path) {
    final extension = path.split('.').last.toLowerCase();
    return ['pdf', 'doc', 'docx', 'txt', 'xls', 'xlsx'].contains(extension);
  }

  /// Get the appropriate icon for a file based on its extension
  static String getFileExtension(String path) {
    return path.split('.').last.toLowerCase();
  }
}
