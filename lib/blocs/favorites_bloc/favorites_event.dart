import 'package:equatable/equatable.dart';
import 'package:roamr/models/trip_itinerary.dart';

abstract class FavoritesEvent extends Equatable {
  const FavoritesEvent();

  @override
  List<Object?> get props => [];
}

class LoadFavorites extends FavoritesEvent {
  const LoadFavorites();
}

class AddToFavorites extends FavoritesEvent {
  final TripItinerary itinerary;

  const AddToFavorites(this.itinerary);

  @override
  List<Object?> get props => [itinerary];
}

class RemoveFromFavorites extends FavoritesEvent {
  final String itineraryId;

  const RemoveFromFavorites(this.itineraryId);

  @override
  List<Object?> get props => [itineraryId];
}

class CheckFavoriteStatus extends FavoritesEvent {
  final String itineraryId;

  const CheckFavoriteStatus(this.itineraryId);

  @override
  List<Object?> get props => [itineraryId];
}

class LoadFavoritesSuccess extends FavoritesEvent {
  final List<TripItinerary> favorites;

  const LoadFavoritesSuccess(this.favorites);

  @override
  List<Object?> get props => [favorites];
}
