import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_event.dart';
import 'package:roamr/blocs/favorites_bloc/favorites_state.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/trip_repository.dart';

class FavoritesBloc extends Bloc<FavoritesEvent, FavoritesState> {
  final TripRepository _tripRepository;
  StreamSubscription? _favoritesSubscription;

  FavoritesBloc({required TripRepository tripRepository})
      : _tripRepository = tripRepository,
        super(const FavoritesState()) {
    on<LoadFavorites>(_onLoadFavorites);
    on<LoadFavoritesSuccess>(_onLoadFavoritesSuccess);
    on<AddToFavorites>(_onAddToFavorites);
    on<RemoveFromFavorites>(_onRemoveFromFavorites);
    on<CheckFavoriteStatus>(_onCheckFavoriteStatus);
  }

  Future<void> _onLoadFavorites(
    LoadFavorites event,
    Emitter<FavoritesState> emit,
  ) async {
    emit(state.copyWith(status: FavoritesStatus.loading));

    await _favoritesSubscription?.cancel();
    _favoritesSubscription = _tripRepository.getFavoriteItineraries().listen(
      (favorites) {
        add(LoadFavoritesSuccess(favorites));
      },
      onError: (error) {
        emit(state.copyWith(status: FavoritesStatus.failure));
      },
    );
  }

  void _onLoadFavoritesSuccess(
    LoadFavoritesSuccess event,
    Emitter<FavoritesState> emit,
  ) {
    emit(state.copyWith(
      favorites: event.favorites,
      status: FavoritesStatus.success,
    ));
  }

  Future<void> _onAddToFavorites(
    AddToFavorites event,
    Emitter<FavoritesState> emit,
  ) async {
    try {
      await _tripRepository.addToFavorites(event.itinerary);

      // Update the favorite status in the state
      final updatedStatus = Map<String, bool>.from(state.favoriteStatus);
      updatedStatus[event.itinerary.id] = true;

      // Update the state with the new favorite status
      emit(state.copyWith(favoriteStatus: updatedStatus));

      // Also reload the favorites list to ensure UI is updated
      add(const LoadFavorites());
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _onRemoveFromFavorites(
    RemoveFromFavorites event,
    Emitter<FavoritesState> emit,
  ) async {
    try {
      await _tripRepository.removeFromFavorites(event.itineraryId);

      // Update the favorite status in the state
      final updatedStatus = Map<String, bool>.from(state.favoriteStatus);
      updatedStatus[event.itineraryId] = false;

      // Update the state with the new favorite status
      emit(state.copyWith(favoriteStatus: updatedStatus));

      // Also reload the favorites list to ensure UI is updated
      add(const LoadFavorites());
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _onCheckFavoriteStatus(
    CheckFavoriteStatus event,
    Emitter<FavoritesState> emit,
  ) async {
    try {
      final isFavorite = await _tripRepository.isItineraryFavorite(event.itineraryId);

      // Update the favorite status in the state
      final updatedStatus = Map<String, bool>.from(state.favoriteStatus);
      updatedStatus[event.itineraryId] = isFavorite;

      emit(state.copyWith(favoriteStatus: updatedStatus));
    } catch (e) {
      // Handle error
    }
  }

  @override
  Future<void> close() {
    _favoritesSubscription?.cancel();
    return super.close();
  }
}


