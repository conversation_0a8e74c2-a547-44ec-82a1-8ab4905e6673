import 'package:equatable/equatable.dart';
import 'package:roamr/models/trip_itinerary.dart';

enum FavoritesStatus { initial, loading, success, failure }

class FavoritesState extends Equatable {
  final List<TripItinerary> favorites;
  final FavoritesStatus status;
  final Map<String, bool> favoriteStatus; // Map of itineraryId to favorite status

  const FavoritesState({
    this.favorites = const [],
    this.status = FavoritesStatus.initial,
    this.favoriteStatus = const {},
  });

  FavoritesState copyWith({
    List<TripItinerary>? favorites,
    FavoritesStatus? status,
    Map<String, bool>? favoriteStatus,
  }) {
    return FavoritesState(
      favorites: favorites ?? this.favorites,
      status: status ?? this.status,
      favoriteStatus: favoriteStatus ?? this.favoriteStatus,
    );
  }

  @override
  List<Object?> get props => [favorites, status, favoriteStatus];
}
