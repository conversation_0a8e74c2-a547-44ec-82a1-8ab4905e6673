import 'package:flutter_bloc/flutter_bloc.dart';

enum NavigationEvent {
  trips,
  statistics,
  saved,
  settings,
}

class NavigationBloc extends Bloc<NavigationEvent, int> {
  NavigationBloc() : super(0) {
    on<NavigationEvent>((event, emit) {
      if (event == NavigationEvent.trips) {
        emit(0);
      }
      else if (event == NavigationEvent.statistics) {
        emit(1);
      }
      else if (event == NavigationEvent.saved) {
        emit(2);
      }
      else if (event == NavigationEvent.settings) {
        emit(3);
      }
    });
  }
}
