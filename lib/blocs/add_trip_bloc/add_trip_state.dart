// Trip State
import 'package:equatable/equatable.dart';
import 'package:roamr/models/trip.dart';

enum AddTripStatus { initial, loading, success, failure }

class AddTripState extends Equatable {
  const AddTripState({this.trips = const [], this.status = AddTripStatus.initial});

  final List<Trip> trips;
  final AddTripStatus status;

  factory AddTripState.initial() {
    return const AddTripState();
  }

  AddTripState copyWith({List<Trip>? trips, AddTripStatus? status}) {
    return AddTripState(trips: trips ?? this.trips, status: status ?? this.status);
  }

  @override
  List<Object?> get props => [trips, status];
}
