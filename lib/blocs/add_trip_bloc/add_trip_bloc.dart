import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_event.dart';
import 'package:roamr/blocs/add_trip_bloc/add_trip_state.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';
import 'package:roamr/di/service_locator.dart';
import 'package:roamr/firebase/analytics_service.dart';
import 'package:roamr/models/trip.dart';
import 'package:roamr/repositories/trip_repository.dart';

class AddTripBloc extends Bloc<AddTripEvent, AddTripState> {
  final TripRepository _tripRepository;

  AddTripBloc({required TripRepository tripRepository})
      : _tripRepository = tripRepository,
        super(AddTripState.initial()) {
    on<LoadTrips>(_onLoadTrips);
    on<AddTrip>(_onAddTrip);
    on<UpdateTrip>(_onUpdateTrip);
    on<DeleteTrip>(_onDeleteTrip);
  }

  Future<void> _onLoadTrips(LoadTrips event, Emitter<AddTripState> emit) async {
    emit(state.copyWith(status: AddTripStatus.loading));
    try {
      final trips = await _tripRepository.getAllTrips();
      emit(state.copyWith(trips: trips, status: AddTripStatus.success));
    } catch (e) {
      emit(state.copyWith(status: AddTripStatus.failure));
    }
  }

  Future<void> _onAddTrip(AddTrip event, Emitter<AddTripState> emit) async {
    emit(state.copyWith(status: AddTripStatus.loading));
    try {
      final trip = Trip(
        id: "",
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        city: event.city,
        countryCode: event.countryCode,
        countryName: event.countryName,
      );
      
      await _tripRepository.createTrip(trip);
      final trips = await _tripRepository.getAllTrips();
      
      // Log after getAllTrips
      await AnalyticsService.instance.logTripCreated(trip: trip);
      
      emit(state.copyWith(trips: trips, status: AddTripStatus.success));
      
       // Increment trip usage via cubit
      await getIt<UserUsageCubit>().incrementUsage('trips');
    } catch (e) {
      emit(state.copyWith(status: AddTripStatus.failure));
    }
  }

  Future<void> _onUpdateTrip(UpdateTrip event, Emitter<AddTripState> emit) async {
    emit(state.copyWith(status: AddTripStatus.loading));
    try {
      final trip = Trip(
        id: event.id,
        title: event.title,
        startDate: event.startDate,
        endDate: event.endDate,
        city: event.city,
        countryCode: event.countryCode,
        countryName: event.countryName,
      );
      
      await _tripRepository.editTrip(trip);
      final trips = await _tripRepository.getAllTrips();
      
      // Log after getAllTrips
      await AnalyticsService.instance.logTripUpdated(trip: trip);
      
      emit(state.copyWith(trips: trips, status: AddTripStatus.success));
    } catch (e) {
      emit(state.copyWith(status: AddTripStatus.failure));
    }
  }

  Future<void> _onDeleteTrip(DeleteTrip event, Emitter<AddTripState> emit) async {
    emit(state.copyWith(status: AddTripStatus.loading));
    try {
      await _tripRepository.deleteTrip(event.tripId);
      final trips = await _tripRepository.getAllTrips();
      emit(state.copyWith(trips: trips, status: AddTripStatus.success)); 
      await AnalyticsService.instance.logTripDeleted(tripId: event.tripId);
     
    } catch (e) {
      emit(state.copyWith(status: AddTripStatus.failure));
    }
  }
}
