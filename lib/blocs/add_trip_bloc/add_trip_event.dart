// Trip Events
import 'package:equatable/equatable.dart';

sealed class AddTripEvent extends Equatable {
  const AddTripEvent();

  @override
  List<Object?> get props => [];
}

final class LoadTrips extends AddTripEvent {
  const LoadTrips();
}

final class AddTrip extends AddTripEvent {
  const AddTrip({
    required this.title,
    required this.startDate,
    required this.endDate,
    this.city,
    this.countryCode,
    this.countryName,
  });

  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String? city;
  final String? countryCode;
  final String? countryName;

  @override
  List<Object?> get props => [title, startDate, endDate, city, countryCode, countryName];
}

final class UpdateTrip extends AddTripEvent {
  const UpdateTrip({
    required this.id,
    required this.title,
    required this.startDate,
    required this.endDate,
    this.city,
    this.countryCode,
    this.countryName,
  });

  final String id;
  final String title;
  final DateTime startDate;
  final DateTime endDate;
  final String? city;
  final String? countryCode;
  final String? countryName;

  @override
  List<Object?> get props => [id, title, startDate, endDate, city, countryCode, countryName];
}

final class DeleteTrip extends AddTripEvent {
  const DeleteTrip({
    required this.tripId,
  });

  final String tripId;

  @override
  List<Object> get props => [tripId];
}