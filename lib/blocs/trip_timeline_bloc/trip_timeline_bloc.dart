import 'package:bloc/bloc.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_event.dart';
import 'package:roamr/blocs/trip_timeline_bloc/trip_timeline_state.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/trip_repository.dart';

class TripTimelineBloc extends Bloc<TripTimelineEvent, TripTimelineState> {
  TripTimelineBloc({required String tripId, required TripRepository repository})
    : _repository = repository,
      _tripId = tripId,
      super(const TripTimelineState()) {
    on<TripTimelineSubscriptionRequested>(_onSubscriptionRequested);
    on<TripTimelineItemDeleted>(_onTimelineItemDeleted);
    on<TripTimelineCategoryFilterChanged>(_onFilterChanged);
    on<TripTimelineReorder>(_onReorder);
  }

  final TripRepository _repository;
  final String _tripId;

  Future<void> _onReorder(
    TripTimelineReorder event,
    Emitter<TripTimelineState> emit,
  ) async {
    try {
      await _repository.reorderItineraries(
        event.movedItinerary,
        event.targetItinerary,
        _tripId,
      );
      emit(state.copyWith(status: () => TripTimelineStatus.success));
    } catch (e) {
      emit(state.copyWith(status: () => TripTimelineStatus.failure));
    }
  }

  Future<void> _onSubscriptionRequested(
    TripTimelineSubscriptionRequested event,
    Emitter<TripTimelineState> emit,
  ) async {
    emit(state.copyWith(status: () => TripTimelineStatus.loading));

    final stream = _repository.getTripItineraries(_tripId);
    await emit.forEach<List<TripItinerary?>>(
      stream,
      onData:
          (itineraries) => state.copyWith(
            status: () => TripTimelineStatus.success,
            itineraries: () => itineraries,
            totalExpenses:
                () => itineraries
                    .map((currentItinerary) => currentItinerary?.amount ?? 0.0)
                    .fold(0.0, (a, b) => a + b),
          ),
    );
  }

  Future<void> _onTimelineItemDeleted(
    TripTimelineItemDeleted event,
    Emitter<TripTimelineState> emit,
  ) async {
    await _repository.deleteItinerary(event.itinerary);
  }

  Future<void> _onFilterChanged(
    TripTimelineCategoryFilterChanged event,
    Emitter<TripTimelineState> emit,
  ) async {
    emit(state.copyWith(filter: () => event.category));
  }
}
