import 'package:equatable/equatable.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip_itinerary.dart';

enum TripTimelineStatus { initial, loading, success, failure }

class TripTimelineState extends Equatable {
  const TripTimelineState({
    this.itineraries = const [],
    this.status = TripTimelineStatus.initial,
    this.totalExpenses = 0.0,
    this.filter = Category.all,
  });

  final List<TripItinerary?> itineraries;
  final TripTimelineStatus status;
  final double totalExpenses;
  final Category filter;

  Iterable<TripItinerary?> get filteredItineraries => filter.applyAll(itineraries);

  TripTimelineState copyWith({
    List<TripItinerary?> Function()? itineraries,
    TripTimelineStatus Function()? status,
    double Function()? totalExpenses,
    Category Function()? filter,
  }) {
    return TripTimelineState(
      itineraries: itineraries != null ? itineraries() : this.itineraries,
      status: status != null ? status() : this.status,
      totalExpenses:
          totalExpenses != null ? totalExpenses() : this.totalExpenses,
      filter: filter != null ? filter() : this.filter,
    );
  }

  factory TripTimelineState.initial() {
    return const TripTimelineState();
  }

  @override
  List<Object?> get props => [itineraries, status, totalExpenses, filter];
}
