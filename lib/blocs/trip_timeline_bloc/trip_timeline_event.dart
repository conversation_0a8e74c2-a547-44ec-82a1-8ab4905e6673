import 'package:equatable/equatable.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip_itinerary.dart';

class TripTimelineEvent extends Equatable {
  const TripTimelineEvent();

  @override
  List<Object?> get props => [];

}

final class TripTimelineSubscriptionRequested extends TripTimelineEvent {
  const TripTimelineSubscriptionRequested();
}

final class TripTimelineItemDeleted extends TripTimelineEvent {
  const TripTimelineItemDeleted({required this.itinerary});

  final TripItinerary itinerary;

  @override
  List<Object> get props => [itinerary];
}

final class TripTimelineCategoryFilterChanged extends TripTimelineEvent {
  const TripTimelineCategoryFilterChanged({required this.category});

  final Category category;

  @override
  List<Object> get props => [category];
}

final class TripTimelineReorder extends TripTimelineEvent {
  const TripTimelineReorder({
    required this.movedItinerary,
    required this.targetItinerary,
  });

  final TripItinerary movedItinerary;
  final TripItinerary targetItinerary;

  @override
  List<Object> get props => [movedItinerary, targetItinerary];
}
