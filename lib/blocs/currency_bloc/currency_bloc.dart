import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:roamr/data/settings_preference.dart';

// State
class CurrencyState extends Equatable {
  final String selectedCurrency;

  const CurrencyState({this.selectedCurrency = 'USD'});

  @override
  List<Object> get props => [selectedCurrency];

  CurrencyState copyWith({
    String? selectedCurrency,
  }) {
    return CurrencyState(
      selectedCurrency: selectedCurrency ?? this.selectedCurrency,
    );
  }
}

// Cubit
class CurrencyCubit extends Cubit<CurrencyState> {
  CurrencyCubit() : super(const CurrencyState()) {
    loadSavedCurrency();
  }

  Future<void> loadSavedCurrency() async {
    final savedCurrency = await SettingsPreferences.getCurrency();
    if (savedCurrency != null) {
      emit(state.copyWith(selectedCurrency: savedCurrency));
    }
  }

  Future<void> changeCurrency(String currency) async {
    await SettingsPreferences.saveCurrency(currency);
    emit(state.copyWith(selectedCurrency: currency));
  }
}
