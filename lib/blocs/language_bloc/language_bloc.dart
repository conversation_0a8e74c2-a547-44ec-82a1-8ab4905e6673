import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:roamr/data/settings_preference.dart';

// State
class LanguageState {
  final Locale locale;
  
  const LanguageState({
    this.locale = const Locale('en'),
  });

  LanguageState copyWith({
    Locale? locale,
  }) {
    return LanguageState(
      locale: locale ?? this.locale,
    );
  }
}

// Events
abstract class LanguageEvent {}

class LoadSavedLanguageEvent extends LanguageEvent {}

class ChangeLanguageEvent extends LanguageEvent {
  final Locale locale;
  ChangeLanguageEvent(this.locale);
}

// Bloc
class LanguageBloc extends Bloc<LanguageEvent, LanguageState> {
  LanguageBloc() : super(const LanguageState()) {
    on<LoadSavedLanguageEvent>(_onLoadSavedLanguage);
    on<ChangeLanguageEvent>(_onChangeLanguage);
    add(LoadSavedLanguageEvent());
  }

  Future<void> _onLoadSavedLanguage(LoadSavedLanguageEvent event, Emitter<LanguageState> emit) async {
    final savedLanguage = await SettingsPreferences.getLanguage();
    if (savedLanguage != null) {
      emit(state.copyWith(locale: Locale(savedLanguage)));
    }
  }

  Future<void> _onChangeLanguage(ChangeLanguageEvent event, Emitter<LanguageState> emit) async {
    await SettingsPreferences.saveLanguage(event.locale.languageCode);
    emit(state.copyWith(locale: event.locale));
  }
}