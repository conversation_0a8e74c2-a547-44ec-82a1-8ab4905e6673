import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart';
import 'package:roamr/data/settings_preference.dart';

// State
class ThemeState {
  final ThemeMode themeMode;
  
  const ThemeState({
    this.themeMode = ThemeMode.system,
  });

  ThemeState copyWith({
    ThemeMode? themeMode,
  }) {
    return ThemeState(
      themeMode: themeMode ?? this.themeMode,
    );
  }
}

// Events
abstract class ThemeEvent {}

class LoadSavedThemeEvent extends ThemeEvent {}

class ChangeThemeEvent extends ThemeEvent {
  final ThemeMode themeMode;
  ChangeThemeEvent(this.themeMode);
}

// Bloc
class ThemeBloc extends Bloc<ThemeEvent, ThemeState> {
  ThemeBloc() : super(const ThemeState()) {
    on<LoadSavedThemeEvent>(_onLoadSavedTheme);
    on<ChangeThemeEvent>(_onChangeTheme);
    add(LoadSavedThemeEvent());
  }

  Future<void> _onLoadSavedTheme(LoadSavedThemeEvent event, Emitter<ThemeState> emit) async {
    final savedTheme = await SettingsPreferences.getTheme();
    if (savedTheme != null) {
      final themeMode = ThemeMode.values.firstWhere(
        (mode) => mode.toString() == savedTheme,
        orElse: () => ThemeMode.system,
      );
      emit(state.copyWith(themeMode: themeMode));
    }
  }

  Future<void> _onChangeTheme(ChangeThemeEvent event, Emitter<ThemeState> emit) async {
    await SettingsPreferences.saveTheme(event.themeMode.toString());
    emit(state.copyWith(themeMode: event.themeMode));
  }
}