import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/category.dart';

sealed class ItineraryFormEvent extends Equatable {
  const ItineraryFormEvent();

  @override
  List<Object?> get props => [];
}

final class ItineraryTitleChanged extends ItineraryFormEvent {
  const ItineraryTitleChanged(this.title);

  final String title;

  @override
  List<Object> get props => [title];
}

final class ItineraryAmountChanged extends ItineraryFormEvent {
  const ItineraryAmountChanged(this.amount);

  final String amount;

  @override
  List<Object> get props => [amount];
}

final class ItineraryDateChanged extends ItineraryFormEvent {
  const ItineraryDateChanged(this.date);

  final DateTime date;

  @override
  List<Object> get props => [date];
}

final class ItineraryCategoryChanged extends ItineraryFormEvent {
  const ItineraryCategoryChanged(this.category);

  final Category category;

  @override
  List<Object> get props => [category];
}

final class ItineraryLocationChanged extends ItineraryFormEvent {
  final LatLng? location;
  final String? locationText;

  const ItineraryLocationChanged(this.location, this.locationText);

  @override
  List<Object?> get props => [location, locationText];
}

class ItineraryFlightNumberChanged extends ItineraryFormEvent {
  final String flightNumber;

  const ItineraryFlightNumberChanged(this.flightNumber);

  @override
  List<Object> get props => [flightNumber];
}

class ItineraryAirlineNameChanged extends ItineraryFormEvent {
  final String airlineName;

  const ItineraryAirlineNameChanged(this.airlineName);

  @override
  List<Object> get props => [airlineName];
}

class ItineraryCheckoutDateChanged extends ItineraryFormEvent {
  final DateTime? checkoutDate;
  const ItineraryCheckoutDateChanged(this.checkoutDate);

  @override
  List<Object?> get props => [checkoutDate];
}

class ItineraryDescriptionChanged extends ItineraryFormEvent {
  final String description;
  const ItineraryDescriptionChanged(this.description);

  @override
  List<Object> get props => [description];
}

final class ItinerarySubmitted extends ItineraryFormEvent {
  const ItinerarySubmitted();
}

final class ItineraryPhotoAdded extends ItineraryFormEvent {
  const ItineraryPhotoAdded(this.file);

  final File file;

  @override
  List<Object> get props => [file];
}

final class ItineraryPhotoRemoved extends ItineraryFormEvent {
  const ItineraryPhotoRemoved(this.file);

  final File file;

  @override
  List<Object> get props => [file];
}

final class ItineraryAttachmentRemoved extends ItineraryFormEvent {
  const ItineraryAttachmentRemoved(this.attachment);

  final AttachmentModel attachment;

  @override
  List<Object> get props => [attachment];
}

final class ItineraryAttachmentsLoaded extends ItineraryFormEvent {
  const ItineraryAttachmentsLoaded(this.attachments);

  final List<AttachmentModel> attachments;

  @override
  List<Object> get props => [attachments];
}

final class ItineraryDocumentAdded extends ItineraryFormEvent {
  const ItineraryDocumentAdded(this.file);

  final File file;

  @override
  List<Object> get props => [file];
}

final class ItineraryDocumentRemoved extends ItineraryFormEvent {
  const ItineraryDocumentRemoved(this.file);

  final File file;

  @override
  List<Object> get props => [file];
}