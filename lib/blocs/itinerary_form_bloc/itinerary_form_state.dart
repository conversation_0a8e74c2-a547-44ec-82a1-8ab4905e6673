import 'dart:io';
import 'package:equatable/equatable.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip_itinerary.dart';

enum ItineraryFormStatus { initial, loading, success, failure }

extension ItineraryFormStatusX on ItineraryFormStatus {
  bool get isLoaded => this != ItineraryFormStatus.loading;
}

final class ItineraryFormState extends Equatable {
  const ItineraryFormState({
    required this.tripId,
    required this.title,
    this.amount,
    required this.date,
    this.category = Category.other,
    this.location,
    this.status = ItineraryFormStatus.initial,
    this.initialItinerary,
    this.locationText = '',
    this.checkoutDate,
    this.airlineName,
    this.flightNumber,
    this.description,
    this.pendingPhotoAttachments = const [],
    this.pendingDocumentAttachments = const [],
    this.savedAttachments = const [],
  });

  final String tripId;
  final String title;
  final double? amount;
  final DateTime date;
  final Category category;
  final LatLng? location;
  final ItineraryFormStatus status;
  final TripItinerary? initialItinerary;
  final String locationText;
  final DateTime? checkoutDate;
  final String? airlineName;
  final String? flightNumber;
  final String? description;
  final List<File> pendingPhotoAttachments;
  final List<File> pendingDocumentAttachments;
  final List<AttachmentModel> savedAttachments;

  ItineraryFormState copyWith({
    String? tripId,
    String? title,
    double? amount,
    DateTime? date,
    Category? category,
    LatLng? location,
    ItineraryFormStatus? status,
    TripItinerary? initialItinerary,
    String? locationText,
    DateTime? checkoutDate,
    String? airlineName,
    String? flightNumber,
    String? description,
    List<File>? pendingPhotoAttachments,
    List<File>? pendingDocumentAttachments,
    List<AttachmentModel>? savedAttachments,
  }) {
    return ItineraryFormState(
      tripId: tripId ?? this.tripId,
      title: title ?? this.title,
      amount: amount ?? this.amount,
      date: date ?? this.date,
      category: category ?? this.category,
      location: location ?? this.location,
      status: status ?? this.status,
      initialItinerary: initialItinerary ?? this.initialItinerary,
      locationText: locationText ?? this.locationText,
      checkoutDate: checkoutDate ?? this.checkoutDate,
      airlineName: airlineName ?? this.airlineName,
      flightNumber: flightNumber ?? this.flightNumber,
      description: description ?? this.description,
      pendingPhotoAttachments: pendingPhotoAttachments ?? this.pendingPhotoAttachments,
      pendingDocumentAttachments: pendingDocumentAttachments ?? this.pendingDocumentAttachments,
      savedAttachments: savedAttachments ?? this.savedAttachments,
    );
  }

  @override
  List<Object?> get props => [
    tripId,
    title,
    amount,
    date,
    category,
    location,
    status,
    initialItinerary,
    locationText,
    checkoutDate,
    airlineName,
    flightNumber,
    description,
    pendingPhotoAttachments,
    pendingDocumentAttachments,
    savedAttachments,
  ];

  bool get isFormValid => title != "";
}
