import 'dart:io';
import 'package:bloc/bloc.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_event.dart';
import 'package:roamr/blocs/itinerary_form_bloc/itinerary_form_state.dart';
import 'package:roamr/models/attachment_model.dart';
import 'package:roamr/models/category.dart';
import 'package:roamr/models/trip_itinerary.dart';
import 'package:roamr/repositories/attachment_repository.dart';
import 'package:roamr/repositories/trip_repository.dart';
import 'package:uuid/uuid.dart';
import 'package:roamr/firebase/analytics_service.dart';
import 'package:roamr/di/service_locator.dart';
import 'package:roamr/blocs/user_usage_cubit.dart';

class ItineraryFormBloc extends Bloc<ItineraryFormEvent, ItineraryFormState> {
  ItineraryFormBloc({
    required String tripId,
    required TripRepository repository,
    required AttachmentRepository attachmentRepository,
    TripItinerary? initialItinerary,
  }) : _repository = repository,
       _attachmentRepository = attachmentRepository,
       _tripId = tripId,
       super(
         ItineraryFormState(
           tripId: tripId,
           initialItinerary: initialItinerary,
           title: initialItinerary?.title ?? "",
           amount: initialItinerary?.amount ?? 0.0,
           date: initialItinerary?.date ?? DateTime.now(),
           category: initialItinerary?.category ?? Category.flight,
           location: initialItinerary?.location,
           locationText: initialItinerary?.locationText ?? '',
           airlineName: initialItinerary?.airlineName,
           flightNumber: initialItinerary?.flightNumber,
           checkoutDate: initialItinerary?.checkoutDate,
           description: initialItinerary?.description,
         ),
       ) {
    on<ItineraryTitleChanged>(_onTitleChanged);
    on<ItineraryAmountChanged>(_onAmountChanged);
    on<ItineraryDateChanged>(_onDateChanged);
    on<ItineraryCategoryChanged>(_onCategoryChanged);
    on<ItineraryLocationChanged>(_onLocationChanged);
    on<ItinerarySubmitted>(_onSubmitted);
    on<ItineraryCheckoutDateChanged>((event, emit) {
      emit(state.copyWith(checkoutDate: event.checkoutDate));
    });
    on<ItineraryDescriptionChanged>((event, emit) {
      emit(state.copyWith(description: event.description));
    });
    on<ItineraryAirlineNameChanged>((event, emit) {
      emit(state.copyWith(airlineName: event.airlineName));
    });
    on<ItineraryFlightNumberChanged>((event, emit) {
      emit(state.copyWith(flightNumber: event.flightNumber));
    });
    on<ItineraryPhotoAdded>(_onPhotoAdded);
    on<ItineraryPhotoRemoved>(_onPhotoRemoved);
    on<ItineraryDocumentAdded>(_onDocumentAdded);
    on<ItineraryDocumentRemoved>(_onDocumentRemoved);
    on<ItineraryAttachmentRemoved>(_onAttachmentRemoved);
    on<ItineraryAttachmentsLoaded>(_onAttachmentsLoaded);

    // Load attachments if editing an existing itinerary
    if (initialItinerary != null) {
      _loadAttachments(initialItinerary.id);
    }
  }

  final TripRepository _repository;
  final AttachmentRepository _attachmentRepository;
  final String _tripId;

  void _onTitleChanged(
    ItineraryTitleChanged event,
    Emitter<ItineraryFormState> emit,
  ) {
    emit(state.copyWith(title: event.title));
  }

  void _onAmountChanged(
    ItineraryAmountChanged event,
    Emitter<ItineraryFormState> emit,
  ) {
    final amount = double.tryParse(event.amount) ?? 0.0; // Set default value to 0.0 if parsing fails
    emit(state.copyWith(amount: amount));
  }

  void _onDateChanged(
    ItineraryDateChanged event,
    Emitter<ItineraryFormState> emit,
  ) {
    emit(state.copyWith(date: event.date));
  }

  void _onCategoryChanged(
    ItineraryCategoryChanged event,
    Emitter<ItineraryFormState> emit,
  ) {
    // Keep only the date when changing category, reset everything else
    emit(ItineraryFormState(
      tripId: state.tripId,
      title: "",  // Clear title
      amount: 0.0, // Reset amount
      date: state.date, // Keep the date
      category: event.category, // Set new category
      status: state.status,
      initialItinerary: state.initialItinerary,
      location: null, // Clear location
      locationText: '', // Clear location text
      checkoutDate: null, // Clear checkout date
      airlineName: "", // Clear airline name
      flightNumber: "", // Clear flight number
      description: "", // Clear description
      pendingPhotoAttachments: state.pendingPhotoAttachments, // Keep photo attachments
      pendingDocumentAttachments: state.pendingDocumentAttachments, // Keep document attachments
      savedAttachments: state.savedAttachments, // Keep saved attachments
    ));
  }

  void _onLocationChanged(ItineraryLocationChanged event, Emitter<ItineraryFormState> emit) {
    if (event.location == null) {
      // If location is being cleared, also clear the location text
      emit(state.copyWith(
        location: null,
        locationText: '',
      ));
    } else {
      // Otherwise update both location and location text
      // Make sure we have a non-empty location text
      final locationText = event.locationText?.isNotEmpty == true
          ? event.locationText!
          : 'Selected Location';

      emit(state.copyWith(
        location: event.location,
        locationText: locationText,
      ));
    }
  }

  Future<void> _onSubmitted(
    ItinerarySubmitted event,
    Emitter<ItineraryFormState> emit,
  ) async {
    final itinerary =
        state.initialItinerary?.copyWith(
          title: state.title,
          amount: state.amount,
          date: state.date,
          category: state.category,
          location: state.location,
          locationText: state.locationText,
          airlineName: state.airlineName,
          flightNumber: state.flightNumber,
          checkoutDate: state.checkoutDate,
          description: state.description,
        ) ??
        TripItinerary(
          id: const Uuid().v4(),
          title: state.title,
          amount: state.amount,
          date: state.date,
          category: state.category,
          location: state.location,
          locationText: state.locationText,
          airlineName: state.airlineName,
          flightNumber: state.flightNumber,
          checkoutDate: state.checkoutDate,
          description: state.description,
        );

    emit(state.copyWith(status: ItineraryFormStatus.loading));

    try {
      // Save the itinerary first and get the actual ID used
      String actualItineraryId;
      if (state.initialItinerary == null) {
        // For new itineraries, get the actual ID from the repository
        actualItineraryId = await _repository.createItinerary(itinerary, _tripId);
        
        // Log itinerary item added
        await AnalyticsService.instance.logItineraryItemAdded(
          tripId: _tripId,
          itinerary: itinerary,
        );
        // Increment global itinerary usage
        await getIt<UserUsageCubit>().incrementUsage('itineraries');
      } else {
        // For existing itineraries, use the original ID
        await _repository.editItinerary(itinerary, _tripId);
        actualItineraryId = itinerary.id;
        
        // Log itinerary item updated
        await AnalyticsService.instance.logItineraryItemUpdated(
          tripId: _tripId,
          itinerary: itinerary,
        );
      }

      // Save any pending photo attachments with the actual itinerary ID
      if (state.pendingPhotoAttachments.isNotEmpty) {
        for (final file in state.pendingPhotoAttachments) {
          await _attachmentRepository.saveAttachment(
            actualItineraryId,
            AttachmentType.photo,
            file,
          );
          
          // Log photo attachment added
          if (itinerary != null) {
            await AnalyticsService.instance.logAttachmentAdded(
              itinerary: itinerary,
              attachmentType: 'photo',
              fileSizeKB: await file.length() ~/ 1024, // Convert bytes to KB
            );
          }
        }
      }

      // Save any pending document attachments with the actual itinerary ID
      if (state.pendingDocumentAttachments.isNotEmpty) {
        for (final file in state.pendingDocumentAttachments) {
          await _attachmentRepository.saveAttachment(
            actualItineraryId,
            AttachmentType.document,
            file,
          );
          
          // Log document attachment added
          if (itinerary != null) {
            await AnalyticsService.instance.logAttachmentAdded(
              itinerary: itinerary,
              attachmentType: 'document',
              fileSizeKB: await file.length() ~/ 1024, // Convert bytes to KB
            );
          }
        }
      }

      emit(state.copyWith(
        status: ItineraryFormStatus.success,
        initialItinerary: null,
        pendingPhotoAttachments: [],
        pendingDocumentAttachments: [],
      ));
    } catch (e) {
      emit(state.copyWith(status: ItineraryFormStatus.failure));
    }
  }

  void _onPhotoAdded(
    ItineraryPhotoAdded event,
    Emitter<ItineraryFormState> emit,
  ) {
    final updatedPhotos = List<File>.from(state.pendingPhotoAttachments)..add(event.file);
    emit(state.copyWith(pendingPhotoAttachments: updatedPhotos));
  }

  void _onPhotoRemoved(
    ItineraryPhotoRemoved event,
    Emitter<ItineraryFormState> emit,
  ) {
    final updatedPhotos = List<File>.from(state.pendingPhotoAttachments)
      ..removeWhere((file) => file.path == event.file.path);
    emit(state.copyWith(pendingPhotoAttachments: updatedPhotos));
  }

  void _onAttachmentRemoved(
    ItineraryAttachmentRemoved event,
    Emitter<ItineraryFormState> emit,
  ) async {
    // Remove from database
    await _attachmentRepository.deleteAttachment(event.attachment.id);

    // Update state
    final updatedAttachments = List<AttachmentModel>.from(state.savedAttachments)
      ..removeWhere((attachment) => attachment.id == event.attachment.id);
    emit(state.copyWith(savedAttachments: updatedAttachments));
  }

  void _onAttachmentsLoaded(
    ItineraryAttachmentsLoaded event,
    Emitter<ItineraryFormState> emit,
  ) {
    emit(state.copyWith(savedAttachments: event.attachments));
  }

  void _onDocumentAdded(
    ItineraryDocumentAdded event,
    Emitter<ItineraryFormState> emit,
  ) {
    final updatedDocuments = List<File>.from(state.pendingDocumentAttachments)..add(event.file);
    emit(state.copyWith(pendingDocumentAttachments: updatedDocuments));
  }

  void _onDocumentRemoved(
    ItineraryDocumentRemoved event,
    Emitter<ItineraryFormState> emit,
  ) {
    final updatedDocuments = List<File>.from(state.pendingDocumentAttachments)
      ..removeWhere((file) => file.path == event.file.path);
    emit(state.copyWith(pendingDocumentAttachments: updatedDocuments));
  }

  Future<void> _loadAttachments(String itineraryId) async {
    try {
      final attachments = await _attachmentRepository.getAttachmentsByItineraryId(itineraryId);
      add(ItineraryAttachmentsLoaded(attachments));
    } catch (e) {
      // Handle error
    }
  }
}