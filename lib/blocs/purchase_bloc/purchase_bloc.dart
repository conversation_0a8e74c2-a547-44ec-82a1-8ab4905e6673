import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:purchases_flutter/purchases_flutter.dart' as purchases;
import 'package:roamr/services/revenue_cat_service.dart';

// States
sealed class PurchaseState {
  final bool isPremium;
  final List<purchases.Package> packages;

  PurchaseState({
    required this.isPremium,
    required this.packages,
  });
}

class PurchaseInitial extends PurchaseState {
  PurchaseInitial() : super(isPremium: false, packages: const []);
}

class PurchaseLoading extends PurchaseState {
  PurchaseLoading({
    required super.isPremium,
    required super.packages,
  });
}

class PurchaseLoaded extends PurchaseState {
  PurchaseLoaded({
    required super.isPremium,
    required super.packages,
  });
}

class PurchaseError extends PurchaseState {
  final String message;

  PurchaseError({
    required this.message,
    required super.isPremium,
    required super.packages,
  });
}

class PurchaseCubit extends Cubit<PurchaseState> {
  final RevenueCatService _revenueCatService;
  StreamSubscription? _purchaseStatusSubscription;

  PurchaseCubit({
    required RevenueCatService revenueCatService,
  })  : _revenueCatService = revenueCatService,
        super(PurchaseInitial()) {
    _purchaseStatusSubscription = _revenueCatService.purchaseStatus.listen(
      _onPurchaseStatusChanged,
    );
  }

  void _onPurchaseStatusChanged(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        emit(PurchaseLoaded(
          isPremium: true,
          packages: state.packages,
        ));
      case SubscriptionStatus.inactive:
        emit(PurchaseLoaded(
          isPremium: false,
          packages: state.packages,
        ));
      case SubscriptionStatus.pending:
        emit(PurchaseLoading(
          isPremium: state.isPremium,
          packages: state.packages,
        ));
    }
  }

  Future<void> initialize() async {
    emit(PurchaseLoading(
      isPremium: state.isPremium,
      packages: state.packages,
    ));

    try {
      // RevenueCat is already initialized in main.dart
      // Just load packages and check subscription status
      final packages = await _revenueCatService.loadProducts();

      // If no packages are available, log a message
      if (packages.isEmpty) {
        debugPrint('No packages available from RevenueCat');
      }

      emit(PurchaseLoaded(
        isPremium: await _revenueCatService.isSubscriptionActive(),
        packages: packages,
      ));
    } catch (e) {
      emit(PurchaseError(
        message: 'Failed to load products: $e',
        isPremium: state.isPremium,
        packages: state.packages,
      ));
    }
  }

  Future<void> restorePurchases() async {
    emit(PurchaseLoading(
      isPremium: state.isPremium,
      packages: state.packages,
    ));

    try {
      await _revenueCatService.restorePurchases();
    } catch (e) {
      emit(PurchaseError(
        message: 'Failed to restore purchases: $e',
        isPremium: state.isPremium,
        packages: state.packages,
      ));
    }
  }

  @override
  Future<void> close() {
    _purchaseStatusSubscription?.cancel();
    _revenueCatService.dispose();
    return super.close();
  }
}