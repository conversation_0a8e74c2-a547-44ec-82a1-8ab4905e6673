import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/services/gemini_chat_service.dart';
import 'package:roamr/di/service_locator.dart';

class AIChatMessage {
  final String text;
  final bool isUser;
  final bool isLoading;
  AIChatMessage({required this.text, required this.isUser, this.isLoading = false});
}

class AIChatState {
  final List<AIChatMessage> messages;
  final bool isAwaitingAI;
  AIChatState({required this.messages, this.isAwaitingAI = false});

  AIChatState copyWith({List<AIChatMessage>? messages, bool? isAwaitingAI}) {
    return AIChatState(
      messages: messages ?? this.messages,
      isAwaitingAI: isAwaitingAI ?? this.isAwaitingAI,
    );
  }
}

class AIChatCubit extends Cubit<AIChatState> {
  final GeminiChatService _geminiChatService = getIt<GeminiChatService>();

  AIChatCubit() : super(AIChatState(messages: []));

  Future<void> sendMessage(String text) async {
    if (text.trim().isEmpty || state.isAwaitingAI) return;
    final userMsg = AIChatMessage(text: text, isUser: true);
    emit(state.copyWith(
      messages: List.from(state.messages)..add(userMsg)..add(AIChatMessage(text: '', isUser: false, isLoading: true)),
      isAwaitingAI: true,
    ));
    await _geminiChatService.initialize();
    String aiResponse = '';
    await for (final chunk in _geminiChatService.sendMessage(text)) {
      aiResponse += chunk;
      emit(state.copyWith(
        messages: List.from(state.messages)
          ..removeLast()
          ..add(AIChatMessage(text: aiResponse, isUser: false, isLoading: true)),
      ));
    }
    emit(state.copyWith(
      messages: List.from(state.messages)
        ..removeLast()
        ..add(AIChatMessage(text: aiResponse, isUser: false)),
      isAwaitingAI: false,
    ));
  }

  void resetChat() {
    _geminiChatService.resetChat();
    emit(AIChatState(messages: []));
  }
} 