import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:roamr/models/user_usage.dart';
import 'package:roamr/services/usage_service.dart';
import 'package:roamr/di/service_locator.dart';

class UserUsageCubit extends Cubit<UserUsage?> {
  UserUsageCubit() : super(null);

  Future<void> fetchUsage() async {
    final usage = await getIt<UsageService>().getUsage();
    emit(usage);
  }

  Future<void> incrementUsage(String usageType) async {
    await getIt<UsageService>().incrementUsage(usageType);
    await fetchUsage();
  }

  Future<void> refresh() async {
    await fetchUsage();
  }
} 