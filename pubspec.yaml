name: roamr
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.4.9+29

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your packag000e dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^3.13.1
  firebase_analytics: ^11.4.6
  firebase_crashlytics: ^4.3.6
  firebase_performance: ^0.10.1+6 # Latest version that's compatible with other Firebase packages
  google_maps_flutter: ^2.5.3
  intl: ^0.20.2
  flutter_localizations:
    sdk: flutter
  # Temporarily commenting out packages that require Dart command during Android build
  # super_clipboard: ^0.8.24
  bloc: ^9.0.0
  flutter_bloc: ^9.1.1
  uuid: ^4.3.3
  equatable: ^2.0.5
  shared_preferences: ^2.2.2
  figma_squircle: ^0.5.3
  rxdart: ^0.27.7
  drift: ^2.5.0
  sqlite3_flutter_libs: ^0.5.15
  sqlite3: ^2.7.5
  path: ^1.8.2
  path_provider: ^2.0.12
  purchases_flutter: ^8.7.2
  purchases_ui_flutter: ^8.7.2
  location: ^8.0.0
  geolocator: ^14.0.0
  geocoding: ^3.0.0
  rive: ^0.13.20
  google_place: ^0.4.0
  flutter_slidable: ^4.0.0
  supabase_flutter: ^2.3.4
  package_info_plus: ^8.3.0
  share_plus: ^11.0.0
  url_launcher: ^6.2.5
  fl_chart: ^1.0.0
  country_picker: ^2.0.27
  flutter_svg: ^2.0.9
  font_awesome_flutter: ^10.7.0
  image_picker: ^1.0.7
  file_picker: ^10.1.9
  flutter_pdfview: ^1.3.2
  in_app_review: ^2.0.8
  google_sign_in: ^6.2.1
  sign_in_with_apple: ^7.0.1
  flutter_local_notifications: ^17.2.4
  timezone: ^0.9.2

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  carousel_slider: ^5.0.0
  table_calendar: ^3.2.0
  get_it: ^8.0.3

dev_dependencies:
  drift_dev: ^2.5.0
  build_runner: ^2.4.5
  intl_translation: ^0.20.1
  flutter_launcher_icons: ^0.13.1
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

dependency_overrides:
  intl: ^0.20.2

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  generate: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/banner.png
    - assets/roamlogo.png
    - assets/plane.png
    - assets/destination_badge.svg
    - assets/expense_badge.svg
    - assets/frequency_badge.svg
    - assets/duration_badge.svg
    - assets/flight_badge.svg
    - assets/data/trip_japan_adventure.json
    - assets/data/trip_european_getaway.json
    - assets/data/trip_southeast_asia_tour.json
    - assets/data/trip_california_road_trip.json
    - assets/data/trip_australian_exploration.json
    - assets/data/
    - assets/icons/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
