package com.pankaj6apr.roamr

import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import io.flutter.embedding.android.FlutterFragmentActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity : FlutterFragmentActivity() {
    private val CHANNEL = "com.pankaj6apr.roamr/oauth"
    private var initialDeepLink: String? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleIntent(intent)
    }

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)
        
        // Set up method channel for handling OAuth callbacks
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "getDeepLinkPrefix" -> {
                    // Return the deep link prefix for OAuth callbacks
                    result.success("roamr://")
                }
                "getInitialDeepLink" -> {
                    // Return any initial deep link
                    result.success(initialDeepLink)
                }
                else -> result.notImplemented()
            }
        }
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        handleIntent(intent)
    }

    private fun handleIntent(intent: Intent?) {
        if (intent == null) return
        
        val action = intent.action
        val data = intent.data
        
        if (Intent.ACTION_VIEW == action && data != null) {
            // This is a deep link
            val deepLink = data.toString()
            Log.d("MainActivity", "Handling deep link: $deepLink")
            
            // Store the initial deep link if we don't have one yet
            if (initialDeepLink == null) {
                initialDeepLink = deepLink
            }
            
            // Forward the deep link to Flutter
            sendDeepLinkToFlutter(deepLink)
        } else if (intent.hasExtra("deep_link_redirect")) {
            // Handle redirect from OAuth flow
            val redirectUrl = intent.getStringExtra("deep_link_redirect")
            if (redirectUrl != null) {
                Log.d("MainActivity", "Handling OAuth redirect: $redirectUrl")
                sendDeepLinkToFlutter(redirectUrl)
            }
        }
    }
    
    private fun sendDeepLinkToFlutter(deepLink: String) {
        // This will be handled by the DeepLinkHandler in Flutter
        // The Flutter app will receive this through the onNewIntent/onResume lifecycle
        Log.d("MainActivity", "Sending deep link to Flutter: $deepLink")
    }
    
    companion object {
        private const val TAG = "MainActivity"
    }
}
